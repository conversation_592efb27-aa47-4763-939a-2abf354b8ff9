from django import forms
from django.utils.translation import gettext_lazy as _
from .models import NutritionPlan, Meal, MealItem, Food
from patients.models import Patient


class NutritionPlanForm(forms.ModelForm):
    """نموذج إنشاء/تعديل الخطة الغذائية"""
    
    class Meta:
        model = NutritionPlan
        fields = [
            'title', 'patient', 'start_date', 'end_date',
            'target_calories', 'target_protein', 'target_carbs', 'target_fat',
            'instructions', 'status'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'patient': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'target_calories': forms.NumberInput(attrs={'class': 'form-control', 'min': '500', 'max': '5000'}),
            'target_protein': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '500'}),
            'target_carbs': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '1000'}),
            'target_fat': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '300'}),
            'instructions': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        doctor = kwargs.pop('doctor', None)
        super().__init__(*args, **kwargs)

        if doctor:
            # تحديد المرضى التابعين للطبيب فقط
            patients = Patient.objects.filter(doctor=doctor).select_related('user')
            self.fields['patient'].queryset = patients

            # تخصيص عرض أسماء المرضى
            self.fields['patient'].empty_label = "-- اختر مريض --"

        # تعيين قيم افتراضية
        if not self.instance.pk:
            self.fields['target_calories'].initial = 2000
            self.fields['target_protein'].initial = 150
            self.fields['target_carbs'].initial = 250
            self.fields['target_fat'].initial = 65


class MealForm(forms.ModelForm):
    """نموذج إضافة/تعديل الوجبة"""

    class Meta:
        model = Meal
        fields = ['name', 'meal_type', 'day_number', 'time_to_eat', 'instructions']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'meal_type': forms.Select(attrs={'class': 'form-select'}),
            'day_number': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '365', 'value': '1'}),
            'time_to_eat': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'instructions': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class MealItemForm(forms.ModelForm):
    """نموذج إضافة طعام للوجبة"""

    class Meta:
        model = MealItem
        fields = ['food', 'quantity']
        widgets = {
            'food': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '2000'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # ترتيب الأطعمة أبجدياً
        self.fields['food'].queryset = Food.objects.all().order_by('name_ar')


class FoodForm(forms.ModelForm):
    """نموذج إضافة/تعديل الطعام"""
    
    class Meta:
        model = Food
        fields = [
            'name_ar', 'name_en', 'name_ku', 'category',
            'calories_per_100g', 'protein_per_100g', 'carbs_per_100g', 'fat_per_100g',
            'fiber_per_100g'
        ]
        widgets = {
            'name_ar': forms.TextInput(attrs={'class': 'form-control'}),
            'name_en': forms.TextInput(attrs={'class': 'form-control'}),
            'name_ku': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'calories_per_100g': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '1000'}),
            'protein_per_100g': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '100', 'step': '0.1'}),
            'carbs_per_100g': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '100', 'step': '0.1'}),
            'fat_per_100g': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '100', 'step': '0.1'}),
            'fiber_per_100g': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'max': '50', 'step': '0.1'}),
        }


class NutritionPlanSearchForm(forms.Form):
    """نموذج البحث في الخطط الغذائية"""
    
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('البحث في الخطط...')
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', _('جميع الحالات'))] + NutritionPlan.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    patient = forms.ModelChoiceField(
        queryset=Patient.objects.none(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        empty_label=_('جميع المرضى')
    )

    def __init__(self, *args, **kwargs):
        doctor = kwargs.pop('doctor', None)
        super().__init__(*args, **kwargs)
        
        if doctor:
            self.fields['patient'].queryset = Patient.objects.filter(doctor=doctor)


class FoodSearchForm(forms.Form):
    """نموذج البحث في الأطعمة"""
    
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('البحث في الأطعمة...')
        })
    )
    
    category = forms.ChoiceField(
        choices=[('', _('جميع الفئات'))] + Food.CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


# Formset للوجبات
MealItemFormSet = forms.inlineformset_factory(
    Meal,
    MealItem,
    form=MealItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)
