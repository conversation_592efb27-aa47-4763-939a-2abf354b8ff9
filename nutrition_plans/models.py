from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator


class NutritionPlan(models.Model):
    """الخطة الغذائية"""
    STATUS_CHOICES = [
        ('active', _('نشطة')),
        ('inactive', _('غير نشطة')),
        ('completed', _('مكتملة')),
    ]

    patient = models.ForeignKey(
        'patients.Patient',
        on_delete=models.CASCADE,
        related_name='nutrition_plans',
        verbose_name=_('المريض')
    )
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_plans',
        limit_choices_to={'user_type': 'doctor'},
        verbose_name=_('الطبيب')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الخطة')
    )
    description = models.TextField(
        verbose_name=_('وصف الخطة')
    )

    # السعرات والمغذيات المستهدفة
    target_calories = models.IntegerField(
        validators=[MinValueValidator(800), MaxValueValidator(5000)],
        verbose_name=_('السعرات المستهدفة (يومياً)')
    )
    target_protein = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(500)],
        verbose_name=_('البروتين المستهدف (غرام)')
    )
    target_carbs = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(1000)],
        verbose_name=_('الكربوهيدرات المستهدفة (غرام)')
    )
    target_fat = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(300)],
        verbose_name=_('الدهون المستهدفة (غرام)')
    )

    # فترة الخطة
    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )
    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name=_('حالة الخطة')
    )

    # ملاحظات وتعليمات
    instructions = models.TextField(
        blank=True,
        verbose_name=_('تعليمات خاصة')
    )
    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    # ملف PDF للخطة
    pdf_file = models.FileField(
        upload_to='nutrition_plans/',
        blank=True,
        null=True,
        verbose_name=_('ملف PDF للخطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('خطة غذائية')
        verbose_name_plural = _('الخطط الغذائية')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['doctor', '-created_at']),
            models.Index(fields=['patient']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.title} - {self.patient}"

    @property
    def is_active(self):
        """التحقق من كون الخطة نشطة"""
        from django.utils import timezone
        today = timezone.now().date()
        return (self.status == 'active' and
                self.start_date <= today <= self.end_date)


class Food(models.Model):
    """الأطعمة"""
    CATEGORY_CHOICES = [
        ('protein', _('بروتينات')),
        ('grains', _('حبوب ونشويات')),
        ('vegetables', _('خضروات')),
        ('fruits', _('فواكه')),
        ('dairy', _('منتجات الألبان')),
        ('fats', _('دهون وزيوت')),
        ('snacks', _('وجبات خفيفة')),
        ('beverages', _('مشروبات')),
    ]

    name_ar = models.CharField(
        max_length=100,
        verbose_name=_('الاسم بالعربية')
    )
    name_en = models.CharField(
        max_length=100,
        verbose_name=_('الاسم بالإنجليزية')
    )
    name_ku = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('الاسم بالكردية')
    )

    # القيم الغذائية لكل 100 غرام
    calories_per_100g = models.FloatField(
        verbose_name=_('السعرات لكل 100غ')
    )
    protein_per_100g = models.FloatField(
        verbose_name=_('البروتين لكل 100غ')
    )
    carbs_per_100g = models.FloatField(
        verbose_name=_('الكربوهيدرات لكل 100غ')
    )
    fat_per_100g = models.FloatField(
        verbose_name=_('الدهون لكل 100غ')
    )
    fiber_per_100g = models.FloatField(
        default=0,
        verbose_name=_('الألياف لكل 100غ')
    )

    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        verbose_name=_('الفئة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإضافة')
    )

    class Meta:
        verbose_name = _('طعام')
        verbose_name_plural = _('الأطعمة')
        indexes = [
            models.Index(fields=['name_ar']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return self.name_ar


class Meal(models.Model):
    """الوجبة"""
    MEAL_TYPE_CHOICES = [
        ('breakfast', _('إفطار')),
        ('morning_snack', _('وجبة خفيفة صباحية')),
        ('lunch', _('غداء')),
        ('afternoon_snack', _('وجبة خفيفة بعد الظهر')),
        ('dinner', _('عشاء')),
        ('evening_snack', _('وجبة خفيفة مسائية')),
    ]

    nutrition_plan = models.ForeignKey(
        NutritionPlan,
        on_delete=models.CASCADE,
        related_name='meals',
        verbose_name=_('الخطة الغذائية')
    )

    meal_type = models.CharField(
        max_length=20,
        choices=MEAL_TYPE_CHOICES,
        verbose_name=_('نوع الوجبة')
    )

    day_number = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        verbose_name=_('رقم اليوم')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم الوجبة')
    )

    instructions = models.TextField(
        blank=True,
        verbose_name=_('تعليمات التحضير')
    )

    time_to_eat = models.TimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت تناول الوجبة')
    )

    class Meta:
        verbose_name = _('وجبة')
        verbose_name_plural = _('الوجبات')
        ordering = ['day_number', 'meal_type']
        indexes = [
            models.Index(fields=['nutrition_plan', 'day_number']),
            models.Index(fields=['meal_type']),
        ]

    def __str__(self):
        return f"{self.name} - يوم {self.day_number}"

    @property
    def total_calories(self):
        """إجمالي السعرات في الوجبة"""
        return sum(item.calories for item in self.meal_items.all())

    @property
    def total_protein(self):
        """إجمالي البروتين في الوجبة"""
        return sum(item.protein for item in self.meal_items.all())

    @property
    def total_carbs(self):
        """إجمالي الكربوهيدرات في الوجبة"""
        return sum(item.carbs for item in self.meal_items.all())

    @property
    def total_fat(self):
        """إجمالي الدهون في الوجبة"""
        return sum(item.fat for item in self.meal_items.all())


class MealItem(models.Model):
    """عنصر الوجبة"""
    meal = models.ForeignKey(
        Meal,
        on_delete=models.CASCADE,
        related_name='meal_items',
        verbose_name=_('الوجبة')
    )
    food = models.ForeignKey(
        Food,
        on_delete=models.CASCADE,
        verbose_name=_('الطعام')
    )
    quantity = models.FloatField(
        validators=[MinValueValidator(0.1), MaxValueValidator(2000)],
        verbose_name=_('الكمية (غرام)')
    )

    notes = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('ملاحظات')
    )

    class Meta:
        verbose_name = _('عنصر وجبة')
        verbose_name_plural = _('عناصر الوجبات')

    def __str__(self):
        return f"{self.food.name_ar} - {self.quantity}غ"

    @property
    def calories(self):
        """السعرات في هذا العنصر"""
        return round((self.food.calories_per_100g * self.quantity) / 100, 2)

    @property
    def protein(self):
        """البروتين في هذا العنصر"""
        return round((self.food.protein_per_100g * self.quantity) / 100, 2)

    @property
    def carbs(self):
        """الكربوهيدرات في هذا العنصر"""
        return round((self.food.carbs_per_100g * self.quantity) / 100, 2)

    @property
    def fat(self):
        """الدهون في هذا العنصر"""
        return round((self.food.fat_per_100g * self.quantity) / 100, 2)

    @property
    def fiber(self):
        """الألياف في هذا العنصر"""
        return round((self.food.fiber_per_100g * self.quantity) / 100, 2)
