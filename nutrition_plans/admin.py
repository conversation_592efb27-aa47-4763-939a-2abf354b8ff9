from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import NutritionPlan, Food, Meal, MealItem


class MealItemInline(admin.TabularInline):
    """عناصر الوجبة كـ inline"""
    model = MealItem
    extra = 1


class MealInline(admin.TabularInline):
    """الوجبات كـ inline"""
    model = Meal
    extra = 0
    readonly_fields = ('total_calories',)

    def total_calories(self, obj):
        return obj.total_calories if obj.pk else 0
    total_calories.short_description = _('إجمالي السعرات')


@admin.register(NutritionPlan)
class NutritionPlanAdmin(admin.ModelAdmin):
    """إدارة الخطط الغذائية"""
    list_display = ('title', 'patient', 'doctor', 'target_calories', 'start_date', 'end_date', 'status')
    list_filter = ('status', 'start_date', 'doctor')
    search_fields = ('title', 'patient__user__first_name', 'patient__user__last_name')
    date_hierarchy = 'start_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('patient', 'doctor', 'title', 'description')
        }),
        (_('الأهداف الغذائية'), {
            'fields': ('target_calories', 'target_protein', 'target_carbs', 'target_fat')
        }),
        (_('فترة الخطة'), {
            'fields': ('start_date', 'end_date', 'status')
        }),
        (_('تعليمات وملاحظات'), {
            'fields': ('instructions', 'notes', 'pdf_file')
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
    inlines = [MealInline]


@admin.register(Food)
class FoodAdmin(admin.ModelAdmin):
    """إدارة الأطعمة"""
    list_display = ('name_ar', 'name_en', 'category', 'calories_per_100g', 'protein_per_100g')
    list_filter = ('category',)
    search_fields = ('name_ar', 'name_en', 'name_ku')

    fieldsets = (
        (_('أسماء الطعام'), {
            'fields': ('name_ar', 'name_en', 'name_ku', 'category')
        }),
        (_('القيم الغذائية لكل 100غ'), {
            'fields': ('calories_per_100g', 'protein_per_100g', 'carbs_per_100g', 'fat_per_100g', 'fiber_per_100g')
        }),
    )

    readonly_fields = ('created_at',)


@admin.register(Meal)
class MealAdmin(admin.ModelAdmin):
    """إدارة الوجبات"""
    list_display = ('name', 'nutrition_plan', 'meal_type', 'day_number', 'time_to_eat', 'total_calories')
    list_filter = ('meal_type', 'day_number', 'nutrition_plan__doctor')
    search_fields = ('name', 'nutrition_plan__title', 'nutrition_plan__patient__user__first_name')

    fieldsets = (
        (_('معلومات الوجبة'), {
            'fields': ('nutrition_plan', 'name', 'meal_type', 'day_number', 'time_to_eat')
        }),
        (_('تعليمات'), {
            'fields': ('instructions',)
        }),
    )

    inlines = [MealItemInline]

    def total_calories(self, obj):
        return obj.total_calories
    total_calories.short_description = _('إجمالي السعرات')


@admin.register(MealItem)
class MealItemAdmin(admin.ModelAdmin):
    """إدارة عناصر الوجبات"""
    list_display = ('meal', 'food', 'quantity', 'calories', 'protein')
    list_filter = ('meal__meal_type', 'food__category')
    search_fields = ('meal__name', 'food__name_ar', 'food__name_en')

    fieldsets = (
        (_('معلومات العنصر'), {
            'fields': ('meal', 'food', 'quantity', 'notes')
        }),
    )

    def calories(self, obj):
        return obj.calories
    calories.short_description = _('السعرات')

    def protein(self, obj):
        return obj.protein
    protein.short_description = _('البروتين')
