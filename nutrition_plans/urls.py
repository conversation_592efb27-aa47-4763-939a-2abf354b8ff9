from django.urls import path
from . import views

app_name = 'nutrition_plans'

urlpatterns = [
    # قائمة الخطط الغذائية
    path('', views.nutrition_plan_list, name='nutrition_plan_list'),
    
    # إنشاء خطة غذائية جديدة
    path('create/<int:patient_id>/', views.create_nutrition_plan, name='create_nutrition_plan'),
    
    # تفاصيل الخطة الغذائية
    path('<int:plan_id>/', views.nutrition_plan_detail, name='nutrition_plan_detail'),
    
    # تعديل الخطة الغذائية
    path('<int:plan_id>/edit/', views.edit_nutrition_plan, name='edit_nutrition_plan'),
    
    # حذف الخطة الغذائية
    path('<int:plan_id>/delete/', views.delete_nutrition_plan, name='delete_nutrition_plan'),
    
    # إضافة وجبة
    path('<int:plan_id>/meal/add/', views.add_meal, name='add_meal'),
    
    # تعديل وجبة
    path('meal/<int:meal_id>/edit/', views.edit_meal, name='edit_meal'),
    
    # حذف وجبة
    path('meal/<int:meal_id>/delete/', views.delete_meal, name='delete_meal'),
    
    # إرسال الخطة عبر WhatsApp
    path('<int:plan_id>/send-whatsapp/', views.send_plan_whatsapp, name='send_plan_whatsapp'),
    
    # تصدير الخطة كـ PDF
    path('<int:plan_id>/pdf/', views.export_plan_pdf, name='export_plan_pdf'),
    
    # إدارة الأطعمة
    path('foods/', views.food_list, name='food_list'),
    path('foods/add/', views.add_food, name='add_food'),
    path('foods/<int:food_id>/edit/', views.edit_food, name='edit_food'),

    # AJAX
    path('get-patient-info/<int:patient_id>/', views.get_patient_info, name='get_patient_info'),

    # Debug
    path('debug/<int:plan_id>/', views.debug_plan, name='debug_plan'),
    path('create-test-patients/', views.create_test_patients, name='create_test_patients'),
    path('debug-patients/', views.debug_patients, name='debug_patients'),
    path('test-patients/', views.test_patients, name='test_patients'),
]
