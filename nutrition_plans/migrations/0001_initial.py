# Generated by Django 5.2.3 on 2025-06-20 18:01

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Food',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=100, verbose_name='الاسم بالعربية')),
                ('name_en', models.CharField(max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('name_ku', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالكردية')),
                ('calories_per_100g', models.FloatField(verbose_name='السعرات لكل 100غ')),
                ('protein_per_100g', models.FloatField(verbose_name='البروتين لكل 100غ')),
                ('carbs_per_100g', models.FloatField(verbose_name='الكربوهيدرات لكل 100غ')),
                ('fat_per_100g', models.FloatField(verbose_name='الدهون لكل 100غ')),
                ('fiber_per_100g', models.FloatField(default=0, verbose_name='الألياف لكل 100غ')),
                ('category', models.CharField(max_length=50, verbose_name='الفئة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
            ],
            options={
                'verbose_name': 'طعام',
                'verbose_name_plural': 'الأطعمة',
            },
        ),
        migrations.CreateModel(
            name='Meal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('meal_type', models.CharField(choices=[('breakfast', 'إفطار'), ('morning_snack', 'وجبة خفيفة صباحية'), ('lunch', 'غداء'), ('afternoon_snack', 'وجبة خفيفة بعد الظهر'), ('dinner', 'عشاء'), ('evening_snack', 'وجبة خفيفة مسائية')], max_length=20, verbose_name='نوع الوجبة')),
                ('day_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)], verbose_name='رقم اليوم')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الوجبة')),
                ('instructions', models.TextField(blank=True, verbose_name='تعليمات التحضير')),
                ('time_to_eat', models.TimeField(blank=True, null=True, verbose_name='وقت تناول الوجبة')),
            ],
            options={
                'verbose_name': 'وجبة',
                'verbose_name_plural': 'الوجبات',
                'ordering': ['day_number', 'meal_type'],
            },
        ),
        migrations.CreateModel(
            name='MealItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.FloatField(validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(2000)], verbose_name='الكمية (غرام)')),
                ('notes', models.CharField(blank=True, max_length=200, verbose_name='ملاحظات')),
                ('food', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='nutrition_plans.food', verbose_name='الطعام')),
                ('meal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meal_items', to='nutrition_plans.meal', verbose_name='الوجبة')),
            ],
            options={
                'verbose_name': 'عنصر وجبة',
                'verbose_name_plural': 'عناصر الوجبات',
            },
        ),
        migrations.CreateModel(
            name='NutritionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الخطة')),
                ('description', models.TextField(verbose_name='وصف الخطة')),
                ('target_calories', models.IntegerField(validators=[django.core.validators.MinValueValidator(800), django.core.validators.MaxValueValidator(5000)], verbose_name='السعرات المستهدفة (يومياً)')),
                ('target_protein', models.FloatField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(500)], verbose_name='البروتين المستهدف (غرام)')),
                ('target_carbs', models.FloatField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000)], verbose_name='الكربوهيدرات المستهدفة (غرام)')),
                ('target_fat', models.FloatField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(300)], verbose_name='الدهون المستهدفة (غرام)')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('status', models.CharField(choices=[('active', 'نشطة'), ('inactive', 'غير نشطة'), ('completed', 'مكتملة')], default='active', max_length=20, verbose_name='حالة الخطة')),
                ('instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='nutrition_plans/', verbose_name='ملف PDF للخطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('doctor', models.ForeignKey(limit_choices_to={'user_type': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='created_plans', to=settings.AUTH_USER_MODEL, verbose_name='الطبيب')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='nutrition_plans', to='patients.patient', verbose_name='المريض')),
            ],
            options={
                'verbose_name': 'خطة غذائية',
                'verbose_name_plural': 'الخطط الغذائية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='meal',
            name='nutrition_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meals', to='nutrition_plans.nutritionplan', verbose_name='الخطة الغذائية'),
        ),
    ]
