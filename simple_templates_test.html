<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار القوالب</title>
    <style>
        body { font-family: Arial; direction: rtl; margin: 20px; }
        .btn { background: #3498db; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; font-size: 16px; }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .templates { display: none; background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .template-card { background: #3498db; color: white; padding: 20px; margin: 10px; border-radius: 10px; cursor: pointer; text-align: center; }
        .template-card:hover { background: #2980b9; }
        .popup { display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.5); z-index: 1000; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; }
        .overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999; }
    </style>
</head>
<body>
    <h1>🍽️ اختبار القوالب الغذائية</h1>
    
    <button class="btn btn-success" onclick="showTemplates()">
        📚 عرض القوالب الجاهزة
    </button>
    
    <div id="templates-section" class="templates">
        <h3>📚 القوالب الغذائية الجاهزة</h3>
        
        <div class="template-card" style="background: #e74c3c;" onclick="showTemplate('weight-loss')">
            <h4>⚖️ قالب إنقاص الوزن</h4>
            <p>1200-1500 سعرة حرارية</p>
        </div>
        
        <div class="template-card" style="background: #27ae60;" onclick="showTemplate('weight-gain')">
            <h4>📈 قالب زيادة الوزن</h4>
            <p>2500-3000 سعرة حرارية</p>
        </div>
        
        <div class="template-card" style="background: #f39c12;" onclick="showTemplate('maintenance')">
            <h4>⚖️ قالب الحفاظ على الوزن</h4>
            <p>1800-2200 سعرة حرارية</p>
        </div>
        
        <div class="template-card" style="background: #9b59b6;" onclick="showTemplate('diabetic')">
            <h4>🩺 قالب مرضى السكري</h4>
            <p>منخفض الكربوهيدرات</p>
        </div>
        
        <button class="btn" onclick="hideTemplates()" style="background: #6c757d; margin-top: 20px;">
            ❌ إخفاء القوالب
        </button>
    </div>
    
    <!-- نافذة تفاصيل القالب -->
    <div id="template-popup" class="popup">
        <h2 id="template-title">قالب غذائي</h2>
        <p id="template-description"></p>
        <div id="template-content"></div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-success" onclick="useTemplate()">
                ✅ استخدام هذا القالب
            </button>
            <button class="btn" onclick="printTemplate()" style="background: #17a2b8;">
                🖨️ طباعة القالب
            </button>
            <button class="btn" onclick="closeTemplate()" style="background: #6c757d;">
                ❌ إغلاق
            </button>
        </div>
    </div>
    
    <div id="template-overlay" class="overlay" onclick="closeTemplate()"></div>

    <script>
        var currentTemplate = null;
        
        var templates = {
            'weight-loss': {
                title: '⚖️ قالب إنقاص الوزن',
                description: 'خطة غذائية متوازنة لإنقاص الوزن بشكل صحي (1200-1500 سعرة حرارية)',
                content: `
                    <h4>الإفطار (300-400 سعرة)</h4>
                    <ul>
                        <li>2 شريحة خبز أسمر + ملعقة كبيرة لبنة قليلة الدسم</li>
                        <li>حبة خيار متوسطة + حبة طماطم</li>
                        <li>كوب شاي أخضر بدون سكر</li>
                        <li>حفنة صغيرة من اللوز (10 حبات)</li>
                    </ul>
                    
                    <h4>وجبة خفيفة صباحية (100-150 سعرة)</h4>
                    <ul>
                        <li>حبة تفاح متوسطة</li>
                        <li>أو كوب زبادي قليل الدسم</li>
                    </ul>
                    
                    <h4>الغداء (400-500 سعرة)</h4>
                    <ul>
                        <li>100 جرام دجاج مشوي منزوع الجلد</li>
                        <li>كوب أرز أسمر مسلوق</li>
                        <li>سلطة خضراء كبيرة بالليمون</li>
                        <li>كوب شوربة خضار</li>
                    </ul>
                    
                    <h4>العشاء (300-400 سعرة)</h4>
                    <ul>
                        <li>100 جرام سمك مشوي</li>
                        <li>كوب خضار مسلوقة</li>
                        <li>شريحة خبز أسمر</li>
                        <li>سلطة خضراء صغيرة</li>
                    </ul>
                    
                    <h4 style="color: #007bff;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>شرب 8-10 أكواب ماء يومياً</li>
                        <li>ممارسة المشي 30 دقيقة يومياً</li>
                        <li>تجنب السكريات والدهون المشبعة</li>
                        <li>تناول الطعام ببطء ومضغ جيد</li>
                    </ul>
                `
            },
            'weight-gain': {
                title: '📈 قالب زيادة الوزن',
                description: 'خطة غذائية غنية بالسعرات لزيادة الوزن بشكل صحي (2500-3000 سعرة حرارية)',
                content: `
                    <h4>الإفطار (600-700 سعرة)</h4>
                    <ul>
                        <li>3 شرائح خبز أسمر + 2 ملعقة كبيرة زبدة فول سوداني</li>
                        <li>كوب حليب كامل الدسم + ملعقة عسل</li>
                        <li>حبة موز + حفنة تمر</li>
                        <li>عجة بيضتين بالزيت</li>
                    </ul>
                    
                    <h4>الغداء (800-900 سعرة)</h4>
                    <ul>
                        <li>150 جرام لحم أحمر مشوي</li>
                        <li>كوب ونصف أرز أبيض</li>
                        <li>كوب فاصولياء بالزيت</li>
                        <li>سلطة بالزيت والليمون</li>
                        <li>كوب عصير طبيعي</li>
                    </ul>
                    
                    <h4>العشاء (500-600 سعرة)</h4>
                    <ul>
                        <li>150 جرام دجاج مشوي</li>
                        <li>كوب مكرونة بالصلصة</li>
                        <li>سلطة خضراء بالزيت</li>
                        <li>كوب زبادي كامل الدسم</li>
                    </ul>
                    
                    <h4 style="color: #007bff;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>تناول 5-6 وجبات صغيرة يومياً</li>
                        <li>شرب السوائل بين الوجبات وليس معها</li>
                        <li>إضافة الزيوت الصحية للطعام</li>
                        <li>ممارسة تمارين المقاومة</li>
                    </ul>
                `
            },
            'maintenance': {
                title: '⚖️ قالب الحفاظ على الوزن',
                description: 'خطة غذائية متوازنة للحفاظ على الوزن المثالي (1800-2200 سعرة حرارية)',
                content: `
                    <h4>الإفطار (400-500 سعرة)</h4>
                    <ul>
                        <li>2 شريحة خبز أسمر + جبنة قليلة الدسم</li>
                        <li>كوب حليب قليل الدسم</li>
                        <li>حبة فاكهة موسمية</li>
                        <li>ملعقة صغيرة عسل</li>
                    </ul>
                    
                    <h4>الغداء (600-700 سعرة)</h4>
                    <ul>
                        <li>120 جرام بروتين (دجاج/سمك/لحم)</li>
                        <li>كوب أرز أو مكرونة</li>
                        <li>كوب خضار مطبوخة</li>
                        <li>سلطة متنوعة</li>
                        <li>ملعقة زيت زيتون</li>
                    </ul>
                    
                    <h4>العشاء (500-600 سعرة)</h4>
                    <ul>
                        <li>100 جرام بروتين خفيف</li>
                        <li>كوب خضار مشكلة</li>
                        <li>شريحة خبز أسمر</li>
                        <li>سلطة خضراء</li>
                    </ul>
                    
                    <h4 style="color: #007bff;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>الحفاظ على نشاط بدني منتظم</li>
                        <li>شرب الماء بكميات كافية</li>
                        <li>تناول وجبات منتظمة</li>
                        <li>مراقبة الوزن أسبوعياً</li>
                    </ul>
                `
            },
            'diabetic': {
                title: '🩺 قالب مرضى السكري',
                description: 'خطة غذائية خاصة لمرضى السكري مع التحكم في الكربوهيدرات',
                content: `
                    <h4>الإفطار (350-400 سعرة)</h4>
                    <ul>
                        <li>شريحة خبز أسمر + بيضة مسلوقة</li>
                        <li>كوب حليب خالي الدسم بدون سكر</li>
                        <li>حبة خيار + طماطم</li>
                        <li>ملعقة صغيرة زيت زيتون</li>
                    </ul>
                    
                    <h4>الغداء (450-500 سعرة)</h4>
                    <ul>
                        <li>100 جرام دجاج مشوي</li>
                        <li>نصف كوب أرز أسمر</li>
                        <li>كوب خضار ورقية</li>
                        <li>سلطة خضراء بالليمون</li>
                        <li>كوب شوربة خضار</li>
                    </ul>
                    
                    <h4>العشاء (350-400 سعرة)</h4>
                    <ul>
                        <li>100 جرام سمك مشوي</li>
                        <li>كوب خضار مسلوقة</li>
                        <li>سلطة خضراء كبيرة</li>
                        <li>ملعقة صغيرة زيت زيتون</li>
                    </ul>
                    
                    <h4 style="color: #007bff;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>مراقبة مستوى السكر بانتظام</li>
                        <li>تجنب السكريات البسيطة</li>
                        <li>تناول الألياف بكثرة</li>
                        <li>ممارسة الرياضة بانتظام</li>
                        <li>شرب الماء بدلاً من العصائر</li>
                    </ul>
                `
            }
        };
        
        function showTemplates() {
            var section = document.getElementById('templates-section');
            if (section.style.display === 'none' || section.style.display === '') {
                section.style.display = 'block';
                alert('تم عرض القوالب الجاهزة');
            } else {
                section.style.display = 'none';
            }
        }
        
        function hideTemplates() {
            document.getElementById('templates-section').style.display = 'none';
            alert('تم إخفاء القوالب');
        }
        
        function showTemplate(templateId) {
            currentTemplate = templateId;
            var template = templates[templateId];
            
            document.getElementById('template-title').textContent = template.title;
            document.getElementById('template-description').textContent = template.description;
            document.getElementById('template-content').innerHTML = template.content;
            
            document.getElementById('template-popup').style.display = 'block';
            document.getElementById('template-overlay').style.display = 'block';
            
            alert('تم فتح تفاصيل القالب: ' + template.title);
        }
        
        function closeTemplate() {
            document.getElementById('template-popup').style.display = 'none';
            document.getElementById('template-overlay').style.display = 'none';
            alert('تم إغلاق نافذة القالب');
        }
        
        function useTemplate() {
            if (currentTemplate) {
                var template = templates[currentTemplate];
                alert('تم اختيار القالب: ' + template.title + '\n\nسيتم إنشاء خطة غذائية جديدة بناءً على هذا القالب.');
                closeTemplate();
            }
        }
        
        function printTemplate() {
            if (currentTemplate) {
                var template = templates[currentTemplate];
                var printWindow = window.open('', '_blank');
                var content = `
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <title>${template.title}</title>
                        <style>
                            body { font-family: Arial; padding: 20px; direction: rtl; line-height: 1.6; }
                            .header { text-align: center; border-bottom: 3px solid #2c3e50; padding-bottom: 20px; margin-bottom: 30px; }
                            h4 { color: #2c3e50; margin-top: 20px; }
                            ul { padding-right: 20px; }
                            li { margin-bottom: 8px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>${template.title}</h1>
                            <p>${template.description}</p>
                            <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                        </div>
                        ${template.content}
                        <div style="margin-top: 50px; text-align: center;">
                            <p>أخصائي التغذية: _______________</p>
                            <p>التوقيع: _______________</p>
                        </div>
                        <script>window.onload = function() { window.print(); };</script>
                    </body>
                    </html>
                `;
                printWindow.document.write(content);
                printWindow.document.close();
                alert('تم فتح نافذة طباعة القالب');
            }
        }
        
        alert('🎉 مرحباً بك في اختبار القوالب الغذائية!\n\nانقر على "عرض القوالب الجاهزة" لبدء الاختبار.');
    </script>
</body>
</html>
