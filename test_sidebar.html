<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الـ Sidebar</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            direction: rtl;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar h3 {
            text-align: center;
            margin-bottom: 30px;
            color: white;
            padding: 0 20px;
        }
        
        .sidebar-link {
            display: block;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
        }
        
        .sidebar-link:hover {
            background: #34495e;
            color: white;
            text-decoration: none;
        }
        
        .sidebar-link.active {
            background: #3498db;
            color: white;
        }
        
        .content {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .alert {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
            font-size: 18px;
            font-weight: bold;
        }
        
        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3>🏥 نظام العيادات</h3>
            
            <a href="#" class="sidebar-link active" onclick="showSection('dashboard', this)">
                📊 لوحة التحكم
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('patients', this)">
                👥 إدارة المرضى
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('nutrition', this)">
                🍽️ الخطط الغذائية
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('foods', this)">
                🍎 إدارة الأطعمة
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('accounting', this)">
                💰 إدارة الحسابات
            </a>
        </div>
        
        <!-- Content Area -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="content-section active">
                <div class="card">
                    <h1>📊 لوحة التحكم</h1>
                    <div class="alert">
                        ✅ مرحباً بك! الـ Sidebar يعمل بشكل مثالي!
                    </div>
                    <p>هذا هو قسم لوحة التحكم. إذا رأيت هذه الرسالة، فإن الـ Sidebar يعمل!</p>
                </div>
            </div>
            
            <!-- Patients -->
            <div id="patients" class="content-section">
                <div class="card">
                    <h1>👥 إدارة المرضى</h1>
                    <div class="alert">
                        ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                    </div>
                    <p>هذا هو قسم إدارة المرضى. الـ Sidebar يعمل بشكل صحيح!</p>
                </div>
            </div>
            
            <!-- Nutrition -->
            <div id="nutrition" class="content-section">
                <div class="card">
                    <h1>🍽️ الخطط الغذائية</h1>
                    <div class="alert">
                        ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                    </div>
                    <p>هذا هو قسم الخطط الغذائية. الـ Sidebar يعمل بشكل ممتاز!</p>
                </div>
            </div>
            
            <!-- Foods -->
            <div id="foods" class="content-section">
                <div class="card">
                    <h1>🍎 إدارة الأطعمة</h1>
                    <div class="alert">
                        ✅ تم الانتقال إلى قسم إدارة الأطعمة بنجاح!
                    </div>
                    <p>هذا هو قسم إدارة الأطعمة. الـ Sidebar يعمل بشكل رائع!</p>
                </div>
            </div>
            
            <!-- Accounting -->
            <div id="accounting" class="content-section">
                <div class="card">
                    <h1>💰 إدارة الحسابات</h1>
                    <div class="alert">
                        ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                    </div>
                    <p>هذا هو قسم إدارة الحسابات. الـ Sidebar يعمل بشكل مثالي!</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, linkElement) {
            console.log('تم النقر على:', sectionId);
            
            // إخفاء جميع الأقسام
            var sections = document.querySelectorAll('.content-section');
            for (var i = 0; i < sections.length; i++) {
                sections[i].classList.remove('active');
            }
            
            // إزالة active من جميع الروابط
            var links = document.querySelectorAll('.sidebar-link');
            for (var i = 0; i < links.length; i++) {
                links[i].classList.remove('active');
            }
            
            // إظهار القسم المحدد
            var targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('تم إظهار القسم:', sectionId);
            } else {
                console.error('لم يتم العثور على القسم:', sectionId);
            }
            
            // إضافة active للرابط المحدد
            if (linkElement) {
                linkElement.classList.add('active');
                console.log('تم تفعيل الرابط');
            }
            
            // رسالة تأكيد
            alert('تم الانتقال إلى: ' + sectionId);
        }
        
        console.log('تم تحميل الصفحة بنجاح!');
        console.log('الـ Sidebar جاهز للاستخدام!');
    </script>
</body>
</html>
