<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            right: 0;
            top: 0;
        }
        
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            padding: 0 20px;
            color: #3498db;
            border-bottom: 2px solid #34495e;
            padding-bottom: 15px;
        }
        
        .nav-btn {
            display: block;
            width: 100%;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border: none;
            background: none;
            text-align: right;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            border-bottom: 1px solid #34495e;
        }
        
        .nav-btn:hover {
            background: #34495e;
            padding-right: 30px;
        }
        
        .nav-btn.active {
            background: #3498db;
            border-right: 4px solid #2980b9;
        }
        
        .content {
            margin-right: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page.active {
            display: block;
        }
        
        .page-title {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 2rem;
        }
        
        .success-msg {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
            font-weight: bold;
            font-size: 18px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }
        
        .stat-card.patients { background: linear-gradient(135deg, #667eea, #764ba2); }
        .stat-card.plans { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .stat-card.appointments { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .stat-card.messages { background: linear-gradient(135deg, #43e97b, #38f9d7); }
        .stat-card.red { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .stat-card.green { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .stat-card.orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .stat-label {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .financial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .financial-card {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .financial-card.expense {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .financial-card.profit {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .money-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .currency-symbol {
            font-size: 18px;
            opacity: 0.9;
            display: block;
            margin-top: 5px;
        }
        
        .feature-list {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 5px solid #3498db;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <button class="nav-btn active" onclick="showPage('dashboard')">
                📊 لوحة التحكم
            </button>
            
            <button class="nav-btn" onclick="showPage('patients')">
                👥 إدارة المرضى
            </button>
            
            <button class="nav-btn" onclick="showPage('nutrition')">
                🍽️ الخطط الغذائية
            </button>
            
            <button class="nav-btn" onclick="showPage('prescriptions')">
                💊 الوصفات الطبية
            </button>
            
            <button class="nav-btn" onclick="showPage('accounting')">
                💰 إدارة الحسابات
            </button>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="page active">
                <h1 class="page-title">📊 لوحة التحكم</h1>
                <div class="success-msg">
                    ✨ مرحباً بك في نظام إدارة العيادات المتطور! النظام يعمل بكامل طاقته!
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card patients" onclick="alert('عدد المرضى: 25 مريض مسجل في النظام')">
                        <div class="stat-number">25</div>
                        <div class="stat-label">إجمالي المرضى</div>
                    </div>
                    <div class="stat-card plans" onclick="alert('الخطط النشطة: 12 خطة غذائية قيد التنفيذ')">
                        <div class="stat-number">12</div>
                        <div class="stat-label">خطط نشطة</div>
                    </div>
                    <div class="stat-card appointments" onclick="alert('مواعيد اليوم: 8 مواعيد محجوزة')">
                        <div class="stat-number">8</div>
                        <div class="stat-label">مواعيد اليوم</div>
                    </div>
                    <div class="stat-card messages" onclick="alert('الرسائل الجديدة: 45 رسالة غير مقروءة')">
                        <div class="stat-number">45</div>
                        <div class="stat-label">رسائل جديدة</div>
                    </div>
                </div>
                
                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">🎯 ميزات النظام المتطورة:</h3>
                    <ul>
                        <li>✅ إدارة شاملة للمرضى والملفات الطبية مع نظام متابعة متقدم</li>
                        <li>✅ كتابة وطباعة الوصفات الطبية بتصميم احترافي ومعتمد</li>
                        <li>✅ نظام محاسبي متكامل بالدينار العراقي مع تقارير مالية مفصلة</li>
                        <li>✅ إدارة الخطط الغذائية المتخصصة مع حساب السعرات والقيم الغذائية</li>
                        <li>✅ تكامل مع الواتساب لإرسال الرسائل والتذكيرات للمرضى</li>
                        <li>✅ نظام مواعيد ذكي مع إشعارات تلقائية ومتابعة دورية</li>
                    </ul>
                </div>
            </div>

            <!-- Patients -->
            <div id="patients" class="page">
                <h1 class="page-title">👥 إدارة المرضى</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>

                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">🔧 الوظائف المتاحة:</h3>
                    <ul>
                        <li>إضافة مرضى جدد مع جميع البيانات الشخصية والطبية</li>
                        <li>تعديل وتحديث بيانات المرضى الموجودين</li>
                        <li>عرض التاريخ الطبي الكامل لكل مريض</li>
                        <li>متابعة تقدم المرضى ونتائج العلاج</li>
                        <li>إنشاء خطط غذائية مخصصة لكل مريض</li>
                        <li>إرسال رسائل واتساب للمتابعة والتذكير</li>
                    </ul>
                </div>

                <button class="btn btn-success" onclick="alert('تم فتح نموذج إضافة مريض جديد')">
                    ➕ إضافة مريض جديد
                </button>
                <button class="btn" onclick="alert('تم فتح قائمة المرضى')">
                    📋 عرض قائمة المرضى
                </button>
                <button class="btn" onclick="alert('تم فتح نموذج البحث')">
                    🔍 البحث عن مريض
                </button>
            </div>

            <!-- Nutrition -->
            <div id="nutrition" class="page">
                <h1 class="page-title">🍽️ الخطط الغذائية</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>

                <div class="stats-grid">
                    <div class="stat-card red" onclick="alert('خطط إنقاص الوزن: 12 خطة نشطة')">
                        <div class="stat-number">12</div>
                        <div class="stat-label">خطط إنقاص الوزن</div>
                    </div>
                    <div class="stat-card green" onclick="alert('خطط زيادة الوزن: 8 خطط نشطة')">
                        <div class="stat-number">8</div>
                        <div class="stat-label">خطط زيادة الوزن</div>
                    </div>
                    <div class="stat-card orange" onclick="alert('خطط الحفاظ على الوزن: 5 خطط نشطة')">
                        <div class="stat-number">5</div>
                        <div class="stat-label">خطط الحفاظ على الوزن</div>
                    </div>
                </div>

                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">🎯 أنواع الخطط الغذائية:</h3>
                    <ul>
                        <li>خطط إنقاص الوزن مع حساب السعرات المناسبة</li>
                        <li>خطط زيادة الوزن الصحية مع التركيز على البروتين</li>
                        <li>خطط الحفاظ على الوزن للمرضى المستقرين</li>
                        <li>خطط خاصة للحالات الطبية (السكري، الضغط، إلخ)</li>
                        <li>خطط رياضية لزيادة الكتلة العضلية</li>
                    </ul>
                </div>

                <button class="btn btn-success" onclick="alert('تم فتح نموذج إنشاء خطة غذائية جديدة')">
                    ➕ إنشاء خطة جديدة
                </button>
                <button class="btn" onclick="alert('تم فتح مكتبة القوالب الجاهزة')">
                    📚 القوالب الجاهزة
                </button>
            </div>

            <!-- Prescriptions -->
            <div id="prescriptions" class="page">
                <h1 class="page-title">💊 الوصفات الطبية</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>

                <div class="stats-grid">
                    <div class="stat-card" onclick="alert('وصفات اليوم: 8 وصفات مكتوبة')">
                        <div class="stat-number">8</div>
                        <div class="stat-label">وصفات اليوم</div>
                    </div>
                    <div class="stat-card plans" onclick="alert('إجمالي الوصفات: 156 وصفة في الأرشيف')">
                        <div class="stat-number">156</div>
                        <div class="stat-label">إجمالي الوصفات</div>
                    </div>
                    <div class="stat-card appointments" onclick="alert('وصفات مطبوعة: 142 وصفة تم طباعتها')">
                        <div class="stat-number">142</div>
                        <div class="stat-label">وصفات مطبوعة</div>
                    </div>
                </div>

                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">📝 خدمات الوصفات الطبية:</h3>
                    <ul>
                        <li>كتابة وصفات طبية جديدة مع تفاصيل شاملة</li>
                        <li>إضافة أدوية متعددة مع الجرعات والتعليمات</li>
                        <li>طباعة وصفات بتصميم طبي احترافي ومعتمد</li>
                        <li>حفظ وأرشفة جميع الوصفات لسهولة الوصول</li>
                        <li>إعادة طباعة الوصفات السابقة عند الحاجة</li>
                        <li>تتبع تاريخ الوصفات لكل مريض</li>
                    </ul>
                </div>

                <button class="btn btn-success" onclick="alert('تم فتح نموذج كتابة وصفة طبية جديدة')">
                    📝 كتابة وصفة جديدة
                </button>
                <button class="btn" onclick="printPrescription()">
                    🖨️ طباعة وصفة تجريبية
                </button>
                <button class="btn" onclick="alert('تم فتح أرشيف الوصفات')">
                    📚 أرشيف الوصفات
                </button>
            </div>

            <!-- Accounting -->
            <div id="accounting" class="page">
                <h1 class="page-title">💰 إدارة الحسابات</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>

                <div class="financial-grid">
                    <div class="financial-card">
                        <h3>💚 إجمالي الإيرادات</h3>
                        <div class="money-amount">58,500,000</div>
                        <div class="currency-symbol">د.ع</div>
                        <small style="opacity: 0.8;">+15% هذا الشهر</small>
                    </div>
                    <div class="financial-card expense">
                        <h3>💸 إجمالي المصروفات</h3>
                        <div class="money-amount">24,050,000</div>
                        <div class="currency-symbol">د.ع</div>
                        <small style="opacity: 0.8;">-5% عن الشهر الماضي</small>
                    </div>
                    <div class="financial-card profit">
                        <h3>📈 صافي الربح</h3>
                        <div class="money-amount">34,450,000</div>
                        <div class="currency-symbol">د.ع</div>
                        <small style="opacity: 0.8;">+25% نمو ممتاز</small>
                    </div>
                </div>

                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">💼 الخدمات المالية المتاحة:</h3>
                    <ul>
                        <li>تتبع الإيرادات والمصروفات بالدينار العراقي بدقة عالية</li>
                        <li>إدارة الفواتير والمدفوعات مع نظام تذكير تلقائي</li>
                        <li>تقارير مالية شهرية وسنوية مفصلة وشاملة</li>
                        <li>حساب الضرائب المستحقة وفقاً للقوانين المحلية</li>
                        <li>تحليل الأداء المالي مع مؤشرات الربحية</li>
                        <li>إدارة رواتب الموظفين ومكافآت الأداء</li>
                    </ul>
                </div>

                <button class="btn btn-success" onclick="alert('تم فتح نموذج إضافة معاملة مالية جديدة')">
                    ➕ إضافة معاملة جديدة
                </button>
                <button class="btn" onclick="alert('تم فتح التقارير المالية')">
                    📊 عرض التقارير المالية
                </button>
                <button class="btn" onclick="alert('تم فتح إعدادات النظام المالي')">
                    ⚙️ إعدادات النظام المالي
                </button>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            console.log('تم النقر على القسم:', pageId);

            // إخفاء جميع الصفحات
            var pages = document.getElementsByClassName('page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }

            // إزالة active من جميع الأزرار
            var buttons = document.getElementsByClassName('nav-btn');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }

            // إظهار الصفحة المحددة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('تم عرض الصفحة:', pageId);
            }

            // إضافة active للزر المحدد
            event.target.classList.add('active');

            // رسالة تأكيد
            alert('✅ تم الانتقال بنجاح إلى: ' + getPageName(pageId));
        }

        function getPageName(pageId) {
            var names = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'prescriptions': 'الوصفات الطبية',
                'accounting': 'إدارة الحسابات'
            };
            return names[pageId] || pageId;
        }

        function printPrescription() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            var content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            line-height: 1.6;
                            direction: rtl;
                            background: white;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #2c3e50;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .clinic-name {
                            font-size: 28px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                        .doctor-info {
                            font-size: 16px;
                            color: #666;
                        }
                        .patient-info {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                            border-right: 5px solid #3498db;
                        }
                        .medicine {
                            background: #fff;
                            border: 2px solid #e9ecef;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 8px;
                            border-right: 4px solid #27ae60;
                        }
                        .medicine-name {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 8px;
                        }
                        .dosage {
                            color: #666;
                            margin: 5px 0;
                        }
                        .instructions {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .signature {
                            margin-top: 50px;
                            text-align: left;
                        }
                        .signature-line {
                            border-bottom: 2px solid #333;
                            width: 200px;
                            margin-bottom: 10px;
                        }
                        @media print {
                            body { margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="clinic-name">🏥 عيادة التغذية العلاجية</div>
                        <div class="doctor-info">د. [اسم الطبيب] - أخصائي التغذية العلاجية</div>
                        <div class="doctor-info">العنوان: [عنوان العيادة] | الهاتف: [رقم الهاتف]</div>
                    </div>

                    <div class="patient-info">
                        <strong>👤 بيانات المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>📅 تاريخ الوصفة:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                        <strong>🆔 رقم الوصفة:</strong> RX-${Math.floor(Math.random() * 100000)}<br>
                        <strong>🔍 التشخيص:</strong> نقص فيتامين د وضعف عام
                    </div>

                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">💊 الأدوية المطلوبة:</h3>

                    <div class="medicine">
                        <div class="medicine-name">1. فيتامين د3 (Vitamin D3)</div>
                        <div class="dosage"><strong>التركيز:</strong> 1000 وحدة دولية</div>
                        <div class="dosage"><strong>الجرعة:</strong> حبة واحدة يومياً مع الطعام</div>
                        <div class="dosage"><strong>المدة:</strong> 3 أشهر</div>
                    </div>

                    <div class="medicine">
                        <div class="medicine-name">2. كالسيوم (Calcium Carbonate)</div>
                        <div class="dosage"><strong>التركيز:</strong> 500 ملغ</div>
                        <div class="dosage"><strong>الجرعة:</strong> حبة واحدة مع الطعام مساءً</div>
                        <div class="dosage"><strong>المدة:</strong> شهر واحد</div>
                    </div>

                    <div class="instructions">
                        <h4 style="color: #856404; margin-top: 0;">⚠️ التعليمات العامة:</h4>
                        <ul>
                            <li>التعرض لأشعة الشمس صباحاً لمدة 15-20 دقيقة يومياً</li>
                            <li>شرب كمية كافية من الماء (8-10 أكواب يومياً)</li>
                            <li>تناول الأدوية مع الطعام لتجنب اضطراب المعدة</li>
                            <li>المتابعة بعد شهر واحد لتقييم التحسن</li>
                            <li>تجنب تناول الكالسيوم مع الحديد في نفس الوقت</li>
                        </ul>
                    </div>

                    <div class="signature">
                        <div>توقيع الطبيب:</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 14px; color: #666;">د. [اسم الطبيب]</div>
                        <div style="font-size: 12px; color: #999; margin-top: 20px;">
                            تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                        </div>
                    </div>

                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `;

            printWindow.document.write(content);
            printWindow.document.close();
        }

        console.log('✅ تم تحميل نظام إدارة العيادات بنجاح!');
        console.log('✅ جميع الوظائف جاهزة للاستخدام!');

        // رسالة ترحيب
        setTimeout(function() {
            alert('🎉 مرحباً بك في نظام إدارة العيادات المتطور!\n\nالنظام جاهز للاستخدام بكامل طاقته!\n\nجميع الأقسام تعمل بشكل مثالي.');
        }, 1000);
    </script>
</body>
</html>
