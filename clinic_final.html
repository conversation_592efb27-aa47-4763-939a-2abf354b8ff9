<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            padding: 0 20px;
            border-bottom: 1px solid #34495e;
            padding-bottom: 20px;
        }
        
        .menu-item {
            display: block;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            background: #34495e;
            padding-right: 30px;
        }
        
        .menu-item.active {
            background: #3498db;
            border-right: 4px solid #2980b9;
        }
        
        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .page {
            display: none;
        }
        
        .page.show {
            display: block;
        }
        
        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .financial-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .financial-card {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .financial-card.expense {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .financial-card.profit {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .money {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .currency {
            font-size: 1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <button class="menu-item active" onclick="switchPage('dashboard', this)">
                📊 لوحة التحكم
            </button>
            
            <button class="menu-item" onclick="switchPage('patients', this)">
                👥 إدارة المرضى
            </button>
            
            <button class="menu-item" onclick="switchPage('prescriptions', this)">
                💊 الوصفات الطبية
            </button>
            
            <button class="menu-item" onclick="switchPage('accounting', this)">
                💰 إدارة الحسابات
            </button>
            
            <button class="menu-item" onclick="switchPage('nutrition', this)">
                🍎 الخطط الغذائية
            </button>
        </div>
        
        <!-- Content Area -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="page show">
                <div class="card">
                    <h1>📊 لوحة التحكم</h1>
                    <div class="success">
                        ✅ مرحباً بك في نظام إدارة العيادات! النظام يعمل بشكل مثالي!
                    </div>
                    
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-number">25</div>
                            <div>إجمالي المرضى</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">12</div>
                            <div>خطط نشطة</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">8</div>
                            <div>وصفات اليوم</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">156</div>
                            <div>رسائل واتساب</div>
                        </div>
                    </div>
                    
                    <h3>🎯 ميزات النظام:</h3>
                    <ul>
                        <li>✅ إدارة شاملة للمرضى والملفات الطبية</li>
                        <li>✅ كتابة وطباعة الوصفات الطبية بتصميم احترافي</li>
                        <li>✅ نظام محاسبي متكامل بالدينار العراقي</li>
                        <li>✅ إدارة الخطط الغذائية المتخصصة</li>
                        <li>✅ تكامل مع الواتساب لإرسال الرسائل</li>
                    </ul>
                </div>
            </div>
            
            <!-- Patients -->
            <div id="patients" class="page">
                <div class="card">
                    <h1>👥 إدارة المرضى</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                    </div>
                    
                    <h3>🔧 الوظائف المتاحة:</h3>
                    <ul>
                        <li>إضافة مرضى جدد مع جميع البيانات الشخصية والطبية</li>
                        <li>تعديل وتحديث بيانات المرضى الحاليين</li>
                        <li>عرض التاريخ الطبي الكامل لكل مريض</li>
                        <li>متابعة تقدم المرضى وتطور حالتهم</li>
                        <li>إدارة المواعيد والزيارات</li>
                    </ul>
                    
                    <button class="btn">➕ إضافة مريض جديد</button>
                    <button class="btn">📋 عرض قائمة المرضى</button>
                    <button class="btn">🔍 البحث عن مريض</button>
                </div>
            </div>
            
            <!-- Prescriptions -->
            <div id="prescriptions" class="page">
                <div class="card">
                    <h1>💊 الوصفات الطبية</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                    </div>
                    
                    <h3>📝 نظام الوصفات المتكامل:</h3>
                    <ul>
                        <li>كتابة وصفات طبية جديدة بواجهة سهلة</li>
                        <li>طباعة الوصفات بتصميم احترافي ومعتمد</li>
                        <li>حفظ وأرشفة جميع الوصفات</li>
                        <li>متابعة تاريخ الوصفات لكل مريض</li>
                        <li>إدارة قاعدة بيانات الأدوية</li>
                    </ul>
                    
                    <button class="btn" onclick="printSamplePrescription()">🖨️ طباعة وصفة تجريبية</button>
                    <button class="btn">📝 كتابة وصفة جديدة</button>
                    <button class="btn">📚 أرشيف الوصفات</button>
                </div>
            </div>
            
            <!-- Accounting -->
            <div id="accounting" class="page">
                <div class="card">
                    <h1>💰 إدارة الحسابات</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                    </div>
                    
                    <div class="financial-stats">
                        <div class="financial-card">
                            <h3>💚 إجمالي الإيرادات</h3>
                            <div class="money">58,500,000</div>
                            <div class="currency">د.ع</div>
                            <small>+15% هذا الشهر</small>
                        </div>
                        <div class="financial-card expense">
                            <h3>💸 إجمالي المصروفات</h3>
                            <div class="money">24,050,000</div>
                            <div class="currency">د.ع</div>
                            <small>-5% عن الشهر الماضي</small>
                        </div>
                        <div class="financial-card profit">
                            <h3>📈 صافي الربح</h3>
                            <div class="money">34,450,000</div>
                            <div class="currency">د.ع</div>
                            <small>+25% نمو</small>
                        </div>
                    </div>
                    
                    <h3>💼 الخدمات المالية:</h3>
                    <ul>
                        <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                        <li>إدارة الفواتير والمدفوعات</li>
                        <li>تقارير مالية شهرية ومفصلة</li>
                        <li>حساب الضرائب المستحقة</li>
                        <li>تحليل الأداء المالي</li>
                    </ul>
                </div>
            </div>
            
            <!-- Nutrition -->
            <div id="nutrition" class="page">
                <div class="card">
                    <h1>🍎 الخطط الغذائية</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                    </div>
                    
                    <h3>🥗 إدارة التغذية العلاجية:</h3>
                    <ul>
                        <li>إنشاء خطط غذائية مخصصة لكل مريض</li>
                        <li>متابعة تقدم المرضى في تحقيق أهدافهم</li>
                        <li>تعديل الخطط حسب التطور والحاجة</li>
                        <li>قاعدة بيانات شاملة للأطعمة والقيم الغذائية</li>
                        <li>تقارير التقدم والنتائج المحققة</li>
                    </ul>
                    
                    <div class="stats">
                        <div class="stat" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                            <div class="stat-number">12</div>
                            <div>خطط إنقاص الوزن</div>
                        </div>
                        <div class="stat" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                            <div class="stat-number">8</div>
                            <div>خطط زيادة الوزن</div>
                        </div>
                        <div class="stat" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                            <div class="stat-number">5</div>
                            <div>خطط الحفاظ على الوزن</div>
                        </div>
                    </div>
                    
                    <button class="btn">➕ إنشاء خطة جديدة</button>
                    <button class="btn">📊 عرض الخطط النشطة</button>
                    <button class="btn">📈 تقارير التقدم</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchPage(pageId, buttonElement) {
            // إخفاء جميع الصفحات
            var pages = document.querySelectorAll('.page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('show');
            }
            
            // إزالة active من جميع الأزرار
            var buttons = document.querySelectorAll('.menu-item');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }
            
            // إظهار الصفحة المحددة
            document.getElementById(pageId).classList.add('show');
            
            // إضافة active للزر المحدد
            buttonElement.classList.add('active');
            
            console.log('تم التبديل إلى صفحة: ' + pageId);
        }
        
        function printSamplePrescription() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            var content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        .header { text-align: center; border-bottom: 3px solid #2c3e50; padding-bottom: 20px; margin-bottom: 30px; }
                        .clinic-name { font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
                        .doctor-info { font-size: 16px; color: #666; }
                        .patient-info { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-right: 5px solid #3498db; }
                        .medicine { background: #fff; border: 2px solid #e9ecef; padding: 15px; margin: 15px 0; border-radius: 8px; border-right: 4px solid #27ae60; }
                        .medicine-name { font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 8px; }
                        .dosage { color: #666; margin: 5px 0; }
                        .instructions { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }
                        .signature { margin-top: 50px; text-align: left; }
                        .signature-line { border-bottom: 2px solid #333; width: 200px; margin-bottom: 10px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="clinic-name">🏥 عيادة التغذية العلاجية</div>
                        <div class="doctor-info">د. [اسم الطبيب] - أخصائي التغذية العلاجية</div>
                        <div class="doctor-info">العنوان: [عنوان العيادة] | الهاتف: [رقم الهاتف]</div>
                    </div>
                    
                    <div class="patient-info">
                        <strong>👤 بيانات المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>📅 تاريخ الوصفة:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                        <strong>🆔 رقم الوصفة:</strong> RX-${Math.floor(Math.random() * 100000)}<br>
                        <strong>🔍 التشخيص:</strong> نقص فيتامين د وضعف عام
                    </div>
                    
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">💊 الأدوية المطلوبة:</h3>
                    
                    <div class="medicine">
                        <div class="medicine-name">1. فيتامين د3 (Vitamin D3)</div>
                        <div class="dosage"><strong>التركيز:</strong> 1000 وحدة دولية</div>
                        <div class="dosage"><strong>الجرعة:</strong> حبة واحدة يومياً مع الطعام</div>
                        <div class="dosage"><strong>المدة:</strong> 3 أشهر</div>
                    </div>
                    
                    <div class="medicine">
                        <div class="medicine-name">2. كالسيوم (Calcium Carbonate)</div>
                        <div class="dosage"><strong>التركيز:</strong> 500 ملغ</div>
                        <div class="dosage"><strong>الجرعة:</strong> حبة واحدة مع الطعام مساءً</div>
                        <div class="dosage"><strong>المدة:</strong> شهر واحد</div>
                    </div>
                    
                    <div class="instructions">
                        <h4 style="color: #856404; margin-top: 0;">⚠️ التعليمات العامة والتحذيرات:</h4>
                        <ul style="margin: 10px 0;">
                            <li>التعرض لأشعة الشمس صباحاً لمدة 15-20 دقيقة يومياً</li>
                            <li>شرب كمية كافية من الماء (8-10 أكواب يومياً)</li>
                            <li>تناول الأدوية مع الطعام لتجنب اضطراب المعدة</li>
                            <li>المتابعة بعد شهر واحد لتقييم التحسن</li>
                            <li>في حالة ظهور أي أعراض جانبية، يرجى التواصل فوراً</li>
                        </ul>
                    </div>
                    
                    <div class="signature">
                        <div>توقيع الطبيب:</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 14px; color: #666;">د. [اسم الطبيب]</div>
                        <div style="font-size: 12px; color: #999; margin-top: 20px;">
                            تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                        </div>
                    </div>
                    
                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `;
            
            printWindow.document.write(content);
            printWindow.document.close();
        }
        
        console.log('🎉 تم تحميل نظام إدارة العيادات بنجاح!');
        console.log('✅ جميع الوظائف تعمل بشكل مثالي!');
    </script>
</body>
</html>
