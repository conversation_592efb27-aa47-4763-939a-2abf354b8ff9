<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات - مُحسن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
        }
        
        .sidebar h2 {
            margin-bottom: 30px;
            text-align: center;
            color: #fff;
        }
        
        .nav-link {
            display: block;
            padding: 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            border-radius: 8px;
            transition: background 0.3s;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .page.active {
            display: block;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .medical-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #ffc107;
        }
        
        .bmi-section {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #28a745;
        }
        
        .bmi-reference {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            font-size: 12px;
            text-align: center;
            margin-top: 15px;
        }
        
        .bmi-ref-item {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-confirmed {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .status-pending {
            background: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .status-late {
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .radio-group {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        
        .radio-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
        }
        
        .radio-group input {
            margin-left: 8px;
            width: auto;
        }
        
        .details-field {
            display: none;
            margin-top: 10px;
        }
        
        .header-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-left: 15px;
        }
        
        .clinic-info h1 {
            margin: 0 0 5px 0;
            font-size: 24px;
        }
        
        .clinic-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 عيادة د. أحمد</h2>
            
            <a href="#" onclick="showPage('dashboard')" class="nav-link">
                📊 لوحة التحكم
            </a>
            
            <a href="#" onclick="showPage('appointments')" class="nav-link">
                📅 إدارة المواعيد
            </a>
            
            <a href="#" onclick="showPage('new-appointment')" class="nav-link">
                ➕ حجز موعد جديد
            </a>
            
            <a href="#" onclick="showPage('nutrition')" class="nav-link">
                🍽️ الخطط الغذائية
            </a>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Header -->
            <div class="header-info">
                <div style="display: flex; align-items: center;">
                    <div class="logo">🏥</div>
                    <div class="clinic-info">
                        <h1>عيادة الدكتور أحمد محمد علي</h1>
                        <p>أخصائي الطب الباطني والتغذية العلاجية</p>
                        <p>📍 شارع الملك فهد، الرياض | 📞 011-234-5678</p>
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="font-weight: bold; margin-bottom: 5px;">👨‍⚕️ د. أحمد محمد علي</div>
                    <div style="font-size: 12px; opacity: 0.8;">رقم الترخيص: 12345</div>
                    <div style="font-size: 12px; opacity: 0.8;">📅 <span id="current-date"></span></div>
                </div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" class="page active">
                <h1>📊 لوحة التحكم</h1>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;" id="today-count">0</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">12</div>
                        <div>خطط نشطة</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">156</div>
                        <div>إجمالي المرضى</div>
                    </div>
                </div>
                
                <div style="background: white; padding: 20px; border-radius: 10px; margin-top: 20px;">
                    <h3>📋 مواعيد اليوم</h3>
                    <div id="dashboard-appointments">
                        <p style="text-align: center; color: #666; padding: 20px;">لا توجد مواعيد لليوم</p>
                    </div>
                </div>
            </div>

            <!-- Appointments -->
            <div id="appointments" class="page">
                <h1>📅 إدارة المواعيد</h1>
                
                <div style="margin-bottom: 20px;">
                    <button onclick="showPage('new-appointment')" class="btn btn-success">➕ حجز موعد جديد</button>
                    <button onclick="addTestAppointment()" class="btn" style="background: #28a745;">🧪 إضافة موعد تجريبي</button>
                    <button onclick="refreshAppointments()" class="btn" style="background: #17a2b8;">🔄 تحديث</button>
                </div>
                
                <div style="background: white; padding: 20px; border-radius: 10px;">
                    <h3>📋 مواعيد اليوم - <span id="today-date-display"></span></h3>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>اسم المريض</th>
                                <th>العمر/الجنس</th>
                                <th>القياسات</th>
                                <th>الوظيفة</th>
                                <th>الهاتف</th>
                                <th>نوع الزيارة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="appointments-table">
                            <tr>
                                <td colspan="9" style="text-align: center; padding: 30px; color: #666;">
                                    📅 لا توجد مواعيد محجوزة لليوم<br>
                                    <small>احجز موعد جديد لرؤية البيانات هنا</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
