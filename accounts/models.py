from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """نموذج المستخدم المخصص"""
    USER_TYPE_CHOICES = [
        ('doctor', _('طبيب')),
        ('patient', _('مريض')),
    ]

    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPE_CHOICES,
        verbose_name=_('نوع المستخدم')
    )
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )
    whatsapp_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الواتساب')
    )
    profile_picture = models.ImageField(
        upload_to='profile_pictures/',
        blank=True,
        null=True,
        verbose_name=_('صورة الملف الشخصي')
    )
    date_of_birth = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الميلاد')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_user_type_display()})"


class DoctorProfile(models.Model):
    """ملف الطبيب الشخصي"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='doctor_profile',
        verbose_name=_('المستخدم')
    )
    specialization = models.CharField(
        max_length=100,
        verbose_name=_('التخصص')
    )
    license_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم الترخيص')
    )
    clinic_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم العيادة')
    )
    clinic_address = models.TextField(
        verbose_name=_('عنوان العيادة')
    )
    years_of_experience = models.PositiveIntegerField(
        verbose_name=_('سنوات الخبرة')
    )
    bio = models.TextField(
        blank=True,
        verbose_name=_('نبذة شخصية')
    )

    class Meta:
        verbose_name = _('ملف الطبيب')
        verbose_name_plural = _('ملفات الأطباء')

    def __str__(self):
        return f"د. {self.user.get_full_name()}"
