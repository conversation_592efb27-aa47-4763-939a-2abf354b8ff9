from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy
from django.views.generic import CreateView
from .forms import (
    DoctorRegistrationForm,
    PatientRegistrationForm,
    CustomAuthenticationForm,
    UserProfileForm,
    DoctorProfileForm
)
from .models import DoctorProfile


def home(request):
    """الصفحة الرئيسية - محسنة"""
    try:
        if request.user.is_authenticated:
            if request.user.user_type == 'doctor':
                return redirect('doctor_dashboard')
            elif request.user.user_type == 'patient':
                return redirect('patient_dashboard')
        return render(request, 'accounts/home.html')
    except Exception:
        # في حالة الخطأ، اعرض صفحة بسيطة
        from django.http import HttpResponse
        return HttpResponse("""
        <html>
        <head><title>نظام إدارة العيادات</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>🏥 نظام إدارة العيادات</h1>
            <p>مرحباً بك في نظام إدارة العيادات</p>
            <a href="/login/" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">تسجيل الدخول</a>
        </body>
        </html>
        """)


def register_choice(request):
    """اختيار نوع التسجيل"""
    return render(request, 'accounts/register_choice.html')


class DoctorRegistrationView(CreateView):
    """تسجيل الطبيب"""
    form_class = DoctorRegistrationForm
    template_name = 'accounts/doctor_register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('تم تسجيل حسابك بنجاح! يمكنك الآن تسجيل الدخول.'))
        return response


class PatientRegistrationView(CreateView):
    """تسجيل المريض"""
    form_class = PatientRegistrationForm
    template_name = 'accounts/patient_register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('تم تسجيل حسابك بنجاح! يمكنك الآن تسجيل الدخول.'))
        return response


def custom_login(request):
    """تسجيل دخول مخصص"""
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, _('مرحباً بك!'))

                # توجيه المستخدم حسب نوعه
                if user.user_type == 'doctor':
                    return redirect('doctor_dashboard')
                elif user.user_type == 'patient':
                    return redirect('patient_dashboard')
                else:
                    return redirect('home')
            else:
                messages.error(request, _('اسم المستخدم أو كلمة المرور غير صحيحة.'))
    else:
        form = CustomAuthenticationForm()

    return render(request, 'accounts/login.html', {'form': form})


@login_required
def profile(request):
    """الملف الشخصي"""
    user_form = UserProfileForm(instance=request.user)
    doctor_form = None

    if request.user.user_type == 'doctor':
        try:
            doctor_profile = request.user.doctor_profile
            doctor_form = DoctorProfileForm(instance=doctor_profile)
        except DoctorProfile.DoesNotExist:
            doctor_form = DoctorProfileForm()

    if request.method == 'POST':
        user_form = UserProfileForm(request.POST, request.FILES, instance=request.user)

        if user_form.is_valid():
            user_form.save()

            if request.user.user_type == 'doctor':
                doctor_form = DoctorProfileForm(request.POST, instance=getattr(request.user, 'doctor_profile', None))
                if doctor_form.is_valid():
                    doctor_profile = doctor_form.save(commit=False)
                    doctor_profile.user = request.user
                    doctor_profile.save()

            messages.success(request, _('تم تحديث ملفك الشخصي بنجاح!'))
            return redirect('profile')

    context = {
        'user_form': user_form,
        'doctor_form': doctor_form,
    }
    return render(request, 'accounts/profile.html', context)


@login_required
def doctor_dashboard(request):
    """لوحة تحكم الطبيب - محسن للأداء"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    from patients.models import Patient
    from nutrition_plans.models import NutritionPlan
    from django.core.cache import cache
    from django.db.models import Count

    # استخدام cache للإحصائيات
    cache_key = f'doctor_stats_{request.user.id}'
    stats = cache.get(cache_key)

    if stats is None:
        try:
            # إحصائيات محسنة باستعلام واحد
            total_patients = Patient.objects.filter(doctor=request.user).count()

            # استعلام واحد لجميع إحصائيات الخطط
            plan_stats = NutritionPlan.objects.filter(
                doctor=request.user
            ).values('status').annotate(count=Count('id'))

            # تحويل النتائج إلى dictionary
            plan_counts = {item['status']: item['count'] for item in plan_stats}

            stats = {
                'total_patients': total_patients,
                'active_plans': plan_counts.get('active', 0),
                'draft_plans': plan_counts.get('draft', 0),
                'completed_plans': plan_counts.get('completed', 0),
            }

            # حفظ في cache لمدة 10 دقائق
            cache.set(cache_key, stats, 600)
        except Exception as e:
            # في حالة الخطأ، استخدم قيم افتراضية
            stats = {
                'total_patients': 0,
                'active_plans': 0,
                'draft_plans': 0,
                'completed_plans': 0,
            }

    # المرضى الجدد مع تحسين الاستعلام
    try:
        recent_patients = Patient.objects.filter(
            doctor=request.user
        ).select_related('user').only(
            'id', 'gender', 'health_goal', 'created_at',
            'user__first_name', 'user__last_name', 'user__username'
        ).order_by('-created_at')[:5]
    except Exception:
        recent_patients = []

    context = {
        'total_patients': stats['total_patients'],
        'active_plans': stats['active_plans'],
        'draft_plans': stats['draft_plans'],
        'completed_plans': stats['completed_plans'],
        'recent_patients': recent_patients,
    }
    return render(request, 'accounts/doctor_dashboard.html', context)


@login_required
def patient_dashboard(request):
    """لوحة تحكم المريض"""
    if request.user.user_type != 'patient':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    try:
        patient = request.user.patient_profile

        # الخطة الغذائية النشطة
        from nutrition_plans.models import NutritionPlan
        active_plan = NutritionPlan.objects.filter(
            patient=patient,
            status='active'
        ).first()

        # آخر سجلات الوزن
        recent_weights = patient.weight_records.all()[:5]

        context = {
            'patient': patient,
            'active_plan': active_plan,
            'recent_weights': recent_weights,
        }
        return render(request, 'accounts/patient_dashboard.html', context)

    except:
        messages.error(request, _('لم يتم العثور على ملف المريض. يرجى التواصل مع الطبيب.'))
        return redirect('home')


def set_language(request):
    """تغيير لغة الواجهة - مبسط"""
    from django.utils import translation
    from django.http import HttpResponseRedirect
    from django.conf import settings

    # دعم GET و POST
    language = request.POST.get('language') or request.GET.get('language')

    if language and language in dict(settings.LANGUAGES).keys():
        # تفعيل اللغة
        translation.activate(language)

        # حفظ في الجلسة
        request.session[translation.LANGUAGE_SESSION_KEY] = language

        # إنشاء الاستجابة
        next_url = request.POST.get('next') or request.GET.get('next') or '/'
        response = HttpResponseRedirect(next_url)

        # حفظ في cookie
        response.set_cookie(
            'django_language',
            language,
            max_age=31536000,  # سنة واحدة
            path='/',
            samesite='Lax'
        )

        return response

    # في حالة عدم وجود لغة صحيحة
    return HttpResponseRedirect('/')


def test_language(request):
    """صفحة اختبار اللغة"""
    return render(request, 'test_language.html')


def language_test_final(request):
    """صفحة اختبار اللغة النهائية"""
    return render(request, 'language_test.html')


def simple_language_switch(request):
    """صفحة تغيير اللغة البسيطة"""
    return render(request, 'simple_language_switch.html')


def simple_login(request):
    """صفحة تسجيل دخول مبسطة للاختبار"""
    return render(request, 'simple_login.html')


def test_sidebar(request):
    """صفحة اختبار الـ Sidebar"""
    return render(request, 'test_sidebar.html')
