from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.home, name='home'),
    
    # التسجيل
    path('register/', views.register_choice, name='register_choice'),
    path('register/doctor/', views.DoctorRegistrationView.as_view(), name='doctor_register'),
    path('register/patient/', views.PatientRegistrationView.as_view(), name='patient_register'),
    
    # تسجيل الدخول والخروج
    path('login/', views.custom_login, name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    
    # الملف الشخصي
    path('profile/', views.profile, name='profile'),
    
    # لوحات التحكم
    path('doctor/dashboard/', views.doctor_dashboard, name='doctor_dashboard'),
    path('patient/dashboard/', views.patient_dashboard, name='patient_dashboard'),
    
    # إعادة تعيين كلمة المرور
    path('password_reset/', auth_views.PasswordResetView.as_view(
        template_name='accounts/password_reset.html'
    ), name='password_reset'),
    path('password_reset/done/', auth_views.PasswordResetDoneView.as_view(
        template_name='accounts/password_reset_done.html'
    ), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', auth_views.PasswordResetConfirmView.as_view(
        template_name='accounts/password_reset_confirm.html'
    ), name='password_reset_confirm'),
    path('reset/done/', auth_views.PasswordResetCompleteView.as_view(
        template_name='accounts/password_reset_complete.html'
    ), name='password_reset_complete'),

    # تغيير اللغة
    path('set-language/', views.set_language, name='set_language'),
    path('test-language/', views.test_language, name='test_language'),
    path('language-test-final/', views.language_test_final, name='language_test_final'),
    path('simple-language/', views.simple_language_switch, name='simple_language_switch'),
    path('simple-login/', views.simple_login, name='simple_login'),
    path('test-sidebar/', views.test_sidebar, name='test_sidebar'),
]
