from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, DoctorProfile


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    """إدارة المستخدمين المخصصة"""
    list_display = ('username', 'email', 'first_name', 'last_name', 'user_type', 'is_staff')
    list_filter = ('user_type', 'is_staff', 'is_superuser', 'is_active', 'date_joined')
    search_fields = ('username', 'first_name', 'last_name', 'email')

    fieldsets = UserAdmin.fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('user_type', 'phone_number', 'whatsapp_number', 'profile_picture', 'date_of_birth')
        }),
    )

    add_fieldsets = UserAdmin.add_fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('user_type', 'phone_number', 'whatsapp_number', 'date_of_birth')
        }),
    )


@admin.register(DoctorProfile)
class DoctorProfileAdmin(admin.ModelAdmin):
    """إدارة ملفات الأطباء"""
    list_display = ('user', 'specialization', 'license_number', 'clinic_name', 'years_of_experience')
    list_filter = ('specialization', 'years_of_experience')
    search_fields = ('user__first_name', 'user__last_name', 'license_number', 'clinic_name')

    fieldsets = (
        (_('معلومات الطبيب'), {
            'fields': ('user', 'specialization', 'license_number')
        }),
        (_('معلومات العيادة'), {
            'fields': ('clinic_name', 'clinic_address')
        }),
        (_('الخبرة والنبذة'), {
            'fields': ('years_of_experience', 'bio')
        }),
    )
