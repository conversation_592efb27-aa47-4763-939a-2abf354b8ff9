from django import template
from django.utils import timezone
from datetime import date

register = template.Library()


@register.filter
def age(birth_date):
    """حساب العمر من تاريخ الميلاد"""
    if not birth_date:
        return None
    
    today = date.today()
    age_years = today.year - birth_date.year
    
    # التحقق من عدم حلول عيد الميلاد بعد
    if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
        age_years -= 1
    
    return age_years


@register.filter
def subtract(value, arg):
    """طرح قيمة من أخرى"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def absolute(value):
    """القيمة المطلقة"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0


@register.filter
def percentage(value, total):
    """حساب النسبة المئوية"""
    try:
        if float(total) == 0:
            return 0
        return round((float(value) / float(total)) * 100, 1)
    except (ValueError, TypeError):
        return 0


@register.filter
def bmi_class(bmi_value):
    """تحديد فئة مؤشر كتلة الجسم للتصميم"""
    try:
        bmi = float(bmi_value)
        if bmi < 18.5:
            return 'warning'
        elif bmi < 25:
            return 'success'
        elif bmi < 30:
            return 'warning'
        else:
            return 'danger'
    except (ValueError, TypeError):
        return 'secondary'


@register.filter
def weight_change_class(change):
    """تحديد فئة تغيير الوزن للتصميم"""
    try:
        change_val = float(change)
        if change_val > 0:
            return 'success'
        elif change_val < 0:
            return 'danger'
        else:
            return 'muted'
    except (ValueError, TypeError):
        return 'muted'


@register.filter
def format_phone(phone_number):
    """تنسيق رقم الهاتف"""
    if not phone_number:
        return ""
    
    # إزالة المسافات والرموز الخاصة
    phone = ''.join(filter(str.isdigit, str(phone_number)))
    
    # تنسيق الرقم العراقي
    if phone.startswith('964'):
        return f"+964 {phone[3:6]} {phone[6:9]} {phone[9:]}"
    elif phone.startswith('0'):
        return f"0{phone[1:4]} {phone[4:7]} {phone[7:]}"
    else:
        return phone_number


@register.simple_tag
def get_item(dictionary, key):
    """الحصول على عنصر من قاموس"""
    return dictionary.get(key)


@register.simple_tag
def multiply(value, multiplier):
    """ضرب قيمة في أخرى"""
    try:
        return float(value) * float(multiplier)
    except (ValueError, TypeError):
        return 0


@register.simple_tag
def divide(value, divisor):
    """قسمة قيمة على أخرى"""
    try:
        if float(divisor) == 0:
            return 0
        return float(value) / float(divisor)
    except (ValueError, TypeError):
        return 0
