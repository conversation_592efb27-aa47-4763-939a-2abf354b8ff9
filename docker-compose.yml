version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: clinic_db
    environment:
      POSTGRES_DB: clinic_management
      POSTGRES_USER: clinic_user
      POSTGRES_PASSWORD: clinic_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clinic_user -d clinic_management"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Message Broker
  redis:
    image: redis:7-alpine
    container_name: clinic_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Web Application
  web:
    build: .
    container_name: clinic_web
    command: gunicorn --bind 0.0.0.0:8000 --workers 3 clinic_management.wsgi:application
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DB_NAME=clinic_management
      - DB_USER=clinic_user
      - DB_PASSWORD=clinic_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery:
    build: .
    container_name: clinic_celery
    command: celery -A clinic_management worker --loglevel=info
    volumes:
      - .:/app
      - media_volume:/app/media
    environment:
      - DEBUG=False
      - DB_NAME=clinic_management
      - DB_USER=clinic_user
      - DB_PASSWORD=clinic_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat (Scheduler)
  celery-beat:
    build: .
    container_name: clinic_celery_beat
    command: celery -A clinic_management beat --loglevel=info
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - DB_NAME=clinic_management
      - DB_USER=clinic_user
      - DB_PASSWORD=clinic_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: clinic_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: clinic_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana Dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: clinic_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped

  # Backup Service
  backup:
    image: postgres:15
    container_name: clinic_backup
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    environment:
      - PGPASSWORD=clinic_password
    command: >
      sh -c "
        chmod +x /backup.sh &&
        echo '0 2 * * * /backup.sh' | crontab - &&
        crond -f
      "
    depends_on:
      - db
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: clinic_network
