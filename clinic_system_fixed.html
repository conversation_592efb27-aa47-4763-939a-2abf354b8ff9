<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات الطبية</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&family=Almarai:wght@300;400;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON>wal', '<PERSON><PERSON>', 'Segoe UI', '<PERSON><PERSON>a', 'Arial', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #0f766e 0%, #14b8a6 50%, #5eead4 100%);
            color: white;
            padding: 0;
            box-shadow: 4px 0 20px rgba(15, 118, 110, 0.3);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle at 70% 60%, rgba(255,255,255,0.08) 1px, transparent 1px),
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.06) 1px, transparent 1px),
                radial-gradient(circle at 80% 30%, rgba(255,255,255,0.06) 1px, transparent 1px),
                linear-gradient(30deg, transparent 45%, rgba(255,255,255,0.03) 50%, transparent 55%),
                linear-gradient(-30deg, transparent 45%, rgba(255,255,255,0.03) 50%, transparent 55%);
            background-size: 28px 28px, 22px 22px, 32px 32px, 25px 25px, 14px 14px, 16px 16px;
            background-position: 0 0, 14px 14px, 6px 6px, 18px 18px, 0 0, 9px 9px;
            opacity: 0.8;
            pointer-events: none;
            z-index: 1;
        }

        .sidebar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    15deg,
                    transparent,
                    transparent 2px,
                    rgba(255,255,255,0.02) 2px,
                    rgba(255,255,255,0.02) 3px
                ),
                repeating-linear-gradient(
                    -15deg,
                    transparent,
                    transparent 2px,
                    rgba(255,255,255,0.02) 2px,
                    rgba(255,255,255,0.02) 3px
                );
            opacity: 0.6;
            pointer-events: none;
            z-index: 1;
        }

        .sidebar-header {
            padding: 30px 20px;
            text-align: center;
            background: rgba(0,0,0,0.15);
            border-bottom: 2px solid rgba(255,255,255,0.2);
            position: relative;
            z-index: 2;
        }

        .sidebar-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            border: 3px solid white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .sidebar-clinic-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
            position: relative;
            z-index: 2;
        }

        .sidebar-doctor-name {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
            position: relative;
            z-index: 2;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            display: block;
            padding: 18px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            font-size: 16px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .nav-item:hover {
            background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            padding-right: 35px;
            box-shadow: inset 5px 0 0 rgba(255,255,255,0.3);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .nav-item.active {
            background: linear-gradient(90deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 100%);
            box-shadow: inset 5px 0 0 rgba(255,255,255,0.4);
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.4);
        }

        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 0;
            background: #f8fafc;
        }

        .ads-banner {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 6px 22px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 6px rgba(5, 150, 105, 0.2);
            border-bottom: 1px solid #059669;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ads-banner:hover {
            background: linear-gradient(135deg, #047857 0%, #059669 100%);
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
            transform: translateY(-1px);
        }

        .ads-banner.hidden {
            display: none;
        }

        .ads-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            animation: slideIn 0.5s ease-in-out;
            max-width: 100%;
            overflow: hidden;
        }

        .ads-image {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid rgba(255,255,255,0.4);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .ads-text {
            flex: 1;
            max-width: 500px;
            text-align: center;
        }

        .ads-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 1px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .ads-description {
            font-size: 9px;
            opacity: 0.95;
            line-height: 1.1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .ads-close {
            position: absolute;
            top: 2px;
            left: 6px;
            background: rgba(255,255,255,0.25);
            border: none;
            color: white;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .ads-close:hover {
            background: rgba(231, 76, 60, 0.8);
            transform: scale(1.1);
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .ads-rotating {
            animation: fadeInOut 8s infinite;
        }

        @keyframes fadeInOut {
            0%, 20% { opacity: 1; }
            25%, 75% { opacity: 1; }
            80%, 100% { opacity: 0; }
        }

        .header {
            background: linear-gradient(135deg, #0f766e 0%, #14b8a6 50%, #5eead4 100%);
            color: white;
            padding: 15px 26px;
            box-shadow: 0 4px 20px rgba(15, 118, 110, 0.4);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1000;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 50%, rgba(255,255,255,0.12) 1px, transparent 1px),
                radial-gradient(circle at 80% 50%, rgba(255,255,255,0.08) 1px, transparent 1px),
                radial-gradient(circle at 40% 20%, rgba(255,255,255,0.06) 1px, transparent 1px),
                radial-gradient(circle at 60% 80%, rgba(255,255,255,0.06) 1px, transparent 1px),
                linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.04) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(255,255,255,0.04) 50%, transparent 60%);
            background-size: 25px 25px, 20px 20px, 30px 30px, 22px 22px, 12px 12px, 15px 15px;
            background-position: 0 0, 12px 12px, 4px 4px, 16px 16px, 0 0, 8px 8px;
            opacity: 0.7;
            pointer-events: none;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 1px,
                    rgba(255,255,255,0.03) 1px,
                    rgba(255,255,255,0.03) 2px
                ),
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 1px,
                    rgba(255,255,255,0.03) 1px,
                    rgba(255,255,255,0.03) 2px
                );
            opacity: 0.5;
            pointer-events: none;
        }

        .header h1 {
            font-size: 21px;
            font-weight: 800;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
            letter-spacing: 0.4px;
            position: relative;
            z-index: 3;
        }

        .header-doctor-info {
            text-align: right;
            background: rgba(255,255,255,0.15);
            padding: 9px;
            border-radius: 6px;
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 3;
            backdrop-filter: blur(5px);
        }

        .doctor-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .license-number {
            font-size: 10px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .content {
            padding: 22px;
            min-height: calc(100vh - 70px);
        }

        .page {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .page.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .page-title {
            font-size: 32px;
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 800;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-shadow: 1px 1px 3px rgba(30, 41, 59, 0.1);
            letter-spacing: 0.5px;
        }

        .btn {
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(71, 85, 105, 0.2);
            margin: 5px;
            letter-spacing: 0.3px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(71, 85, 105, 0.3);
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            box-shadow: 0 5px 15px rgba(5, 150, 105, 0.25);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.25);
        }

        .btn-warning {
            background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
            box-shadow: 0 5px 15px rgba(217, 119, 6, 0.25);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: #2c3e50;
            font-size: 16px;
            letter-spacing: 0.2px;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        /* تنسيق الأرقام والتواريخ - إنجليزي من اليسار لليمين */
        input[type="number"],
        input[type="tel"],
        input[type="date"],
        input[type="time"],
        input[type="email"],
        input[type="url"],
        .number-field,
        .date-field,
        .currency-field {
            direction: ltr !important;
            text-align: left !important;
            font-family: 'Courier New', monospace;
        }

        .currency-display,
        .number-display,
        .date-display,
        .time-display {
            direction: ltr !important;
            text-align: left !important;
            font-family: 'Courier New', monospace;
        }

        /* تنسيق خاص للعملة */
        .currency-amount {
            direction: ltr !important;
            text-align: right !important;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            color: #1e293b;
            padding: 15px 12px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 6px 15px rgba(30, 41, 59, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .dashboard-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(30, 41, 59, 0.15);
            border-color: #cbd5e1;
        }

        .dashboard-card h3 {
            font-size: 16px;
            font-weight: 700;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            margin-bottom: 8px;
            color: #475569;
            letter-spacing: 0.3px;
        }

        .dashboard-card .number {
            font-size: 26px;
            font-weight: 900;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            margin-bottom: 6px;
            color: #1e293b;
            letter-spacing: 1px;
        }

        .dashboard-card .currency {
            font-size: 12px;
            color: #64748b;
            display: block;
            margin-top: 2px;
        }

        .hidden {
            display: none !important;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
            color: white;
            padding: 15px 12px;
            text-align: right;
            font-weight: 700;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            letter-spacing: 0.3px;
            font-size: 14px;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            text-align: right;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 500;
            font-size: 14px;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .no-print {
            display: block;
        }

        /* تحسينات إضافية للخطوط */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 700;
            letter-spacing: 0.3px;
        }

        p, span, div {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 400;
        }

        strong, b {
            font-weight: 700;
        }

        input, textarea, select {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-right: 0;
                padding: 20px;
            }

            .header {
                padding: 11px 15px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media print {
            .no-print {
                display: none !important;
            }
            body {
                background: white;
                padding: 0;
            }
            .container {
                display: block;
            }
            .sidebar {
                display: none;
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div id="sidebar-logo-container" style="margin-bottom: 15px; display: none;">
                    <img id="sidebar-logo" style="max-height: 60px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                </div>
                <div class="sidebar-logo" id="sidebar-default-logo">♦</div>
                <div class="sidebar-clinic-name" id="sidebar-program-name">نظام إدارة العيادات</div>
                <div class="sidebar-doctor-name" id="sidebar-program-version" style="display: none;">الإصدار: 1.0.0</div>
            </div>
            <nav class="sidebar-nav">
                <a href="#dashboard" class="nav-item active" onclick="showPage('dashboard')">لوحة التحكم</a>
                <a href="#patients" class="nav-item" onclick="showPage('patients')">إدارة المرضى</a>
                <a href="#appointments" class="nav-item" onclick="showPage('appointments')">إدارة المواعيد</a>
                <a href="#prescriptions" class="nav-item" onclick="showPage('prescriptions')">الوصفات الطبية</a>
                <a href="#nutrition" class="nav-item" onclick="showPage('nutrition')">الخطط الغذائية</a>
                <a href="#accounting" class="nav-item" onclick="showPage('accounting')">إدارة المحاسبة</a>
                <a href="#medicine-ads" class="nav-item" onclick="showPage('medicine-ads')">إدارة الإعلانات الدوائية</a>
                <a href="#settings" class="nav-item" onclick="authenticateSettings()">إعدادات العيادة</a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Ads Banner -->
            <div id="ads-banner" class="ads-banner hidden">
                <button class="ads-close" onclick="closeAdsBanner(event)" title="إغلاق الإعلان">×</button>
                <div class="ads-content" id="ads-content">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>

            <div class="header">
                <h1 id="page-title">لوحة التحكم</h1>
                <div class="header-doctor-info">
                    <div class="doctor-name">د. أحمد محمد علي</div>
                    <div class="license-number">رقم الترخيص: 12345</div>
                    <div style="font-size: 14px; opacity: 0.8;"><span id="current-date"></span></div>
                    <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;"><span id="current-time"></span></div>
                    <button onclick="goHome()" class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 15px; font-size: 12px; margin-top: 10px; width: 100%;">
                        العودة للوحة التحكم
                    </button>
                </div>
            </div>

            <div class="content">
                <!-- Dashboard Page -->
                <div id="dashboard" class="page active">
                    <h2 class="page-title">لوحة التحكم الرئيسية</h2>

                    <div class="dashboard-cards">
                        <div class="dashboard-card" style="background: linear-gradient(135deg, #475569 0%, #64748b 100%); color: white;">
                            <h3 style="color: white;">إجمالي المرضى</h3>
                            <div class="number" id="total-patients" style="color: white;">0</div>
                        </div>

                        <div class="dashboard-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white;">
                            <h3 style="color: white;">مواعيد اليوم</h3>
                            <div class="number" id="today-appointments" style="color: white;">0</div>
                        </div>

                        <div class="dashboard-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white;">
                            <h3 style="color: white;">الوصفات الطبية</h3>
                            <div class="number" id="total-prescriptions" style="color: white;">0</div>
                        </div>

                        <div class="dashboard-card" style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%); color: white;">
                            <h3 style="color: white;">الخطط الغذائية</h3>
                            <div class="number" id="total-nutrition-plans" style="color: white;">0</div>
                        </div>

                        <div class="dashboard-card" style="background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%); color: white;">
                            <h3 style="color: white;">الإيرادات الشهرية</h3>
                            <div class="number" style="font-size: 32px; color: white; direction: ltr;" id="monthly-revenue">0</div>
                            <span class="currency" style="color: rgba(255,255,255,0.9);">IQD</span>
                        </div>

                        <div class="dashboard-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%); color: white;">
                            <h3 style="color: white;">متوسط الزيارات اليومية</h3>
                            <div class="number" id="daily-average" style="color: white;">0</div>
                        </div>
                    </div>

                    <!-- Medicine Ads Section -->
                    <div style="background: white; border-radius: 10px; padding: 15px; margin: 20px 0; box-shadow: 0 3px 8px rgba(30, 41, 59, 0.06); border: 2px solid #e2e8f0;">
                        <h3 style="color: #1e293b; margin-bottom: 15px; text-align: center; display: flex; align-items: center; justify-content: center; gap: 10px; font-size: 18px;">
                            <span>📢</span>
                            <span>الإعلانات الدوائية من الإدارة العامة</span>
                            <span style="background: #dc2626; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;" id="ads-count">0</span>
                        </h3>
                        <div id="medicine-ads-container" style="min-height: 100px;">
                            <!-- سيتم ملء الإعلانات بواسطة JavaScript -->
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <h3 style="color: #1e293b; margin-bottom: 15px; font-size: 18px;">🚀 الإجراءات السريعة</h3>
                        <button onclick="showPage('appointments')" class="btn btn-success" style="margin: 5px; padding: 8px 15px; font-size: 12px;">حجز موعد جديد</button>
                        <button onclick="showPage('prescriptions')" class="btn" style="margin: 5px; padding: 8px 15px; font-size: 12px;">كتابة وصفة طبية</button>
                        <button onclick="showPage('nutrition')" class="btn btn-warning" style="margin: 5px; padding: 8px 15px; font-size: 12px;">إنشاء خطة غذائية</button>
                        <button onclick="showPage('patients')" class="btn" style="margin: 5px; padding: 8px 15px; font-size: 12px;">إضافة مريض جديد</button>
                        <button onclick="showPage('accounting')" class="btn" style="background: #9b59b6; margin: 5px; padding: 8px 15px; font-size: 12px;">إدارة المحاسبة</button>
                        <button onclick="showPage('medicine-ads')" class="btn" style="background: #e67e22; margin: 5px; padding: 8px 15px; font-size: 12px;">إدارة الإعلانات</button>
                        <button onclick="authenticateSettings()" class="btn" style="background: #34495e; margin: 5px; padding: 8px 15px; font-size: 12px;">إعدادات العيادة</button>
                    </div>
                </div>

                <!-- Patients Page -->
                <div id="patients" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">👥 إدارة المرضى</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <div style="text-align: center; margin-bottom: 40px;">
                        <button onclick="showAddPatientForm()" class="btn btn-success" style="margin: 10px;">➕ إضافة مريض جديد</button>
                        <button onclick="showPatientsList()" class="btn" style="margin: 10px;">📋 عرض قائمة المرضى</button>
                        <button onclick="showSearchPatient()" class="btn btn-warning" style="margin: 10px;">🔍 البحث عن مريض</button>
                    </div>

                    <!-- Add Patient Form -->
                    <div id="add-patient-form" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">➕ إضافة مريض جديد</h3>
                        <form onsubmit="addNewPatient(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="patient-full-name" required>
                                </div>
                                <div class="form-group">
                                    <label>العمر *</label>
                                    <input type="number" class="form-control" id="patient-age" min="1" max="120" required>
                                </div>
                                <div class="form-group">
                                    <label>الجنس *</label>
                                    <select class="form-control" id="patient-gender" required onchange="toggleGenderSpecificSections()">
                                        <option value="">اختر الجنس</option>
                                        <option value="ذكر">ذكر</option>
                                        <option value="أنثى">أنثى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="patient-phone" required>
                                </div>
                                <div class="form-group">
                                    <label>الوظيفة الحالية</label>
                                    <input type="text" class="form-control" id="patient-occupation">
                                </div>
                                <div class="form-group">
                                    <label>المؤهل الدراسي</label>
                                    <select class="form-control" id="patient-education">
                                        <option value="">اختر المؤهل</option>
                                        <option value="ابتدائي">ابتدائي</option>
                                        <option value="متوسط">متوسط</option>
                                        <option value="ثانوي">ثانوي</option>
                                        <option value="دبلوم">دبلوم</option>
                                        <option value="بكالوريوس">بكالوريوس</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>العنوان</label>
                                <textarea class="form-control" id="patient-address" rows="3"></textarea>
                            </div>

                            <!-- الاستبيان الطبي -->
                            <div style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 25px; border-radius: 15px; margin: 30px 0;">
                                <h3 style="color: white; margin-bottom: 20px; text-align: center; font-size: 22px;">🏥 الاستبيان الطبي الشامل</h3>
                                <p style="text-align: center; margin-bottom: 25px; opacity: 0.9; font-size: 16px;">يرجى الإجابة على الأسئلة التالية بدقة لضمان تقديم أفضل رعاية طبية</p>

                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                    <!-- الحساسية -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🤧 الحساسية</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تعاني من أي حساسية؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="allergies" value="yes" style="margin-left: 8px;" onchange="toggleAllergyDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="allergies" value="no" style="margin-left: 8px;" onchange="toggleAllergyDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="allergy-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الحساسية:</label>
                                            <textarea id="allergy-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="اذكر نوع الحساسية والأعراض..."></textarea>
                                        </div>
                                    </div>

                                    <!-- الأدوية الحالية -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">💊 الأدوية الحالية</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تتناول أي أدوية حالياً؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="current-medications" value="yes" style="margin-left: 8px;" onchange="toggleMedicationDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="current-medications" value="no" style="margin-left: 8px;" onchange="toggleMedicationDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="medication-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الأدوية:</label>
                                            <textarea id="medication-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="اذكر أسماء الأدوية والجرعات..."></textarea>
                                        </div>
                                    </div>

                                    <!-- العمليات الجراحية -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🏥 العمليات الجراحية</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل أجريت أي عمليات جراحية؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="surgeries" value="yes" style="margin-left: 8px;" onchange="toggleSurgeryDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="surgeries" value="no" style="margin-left: 8px;" onchange="toggleSurgeryDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="surgery-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل العمليات:</label>
                                            <textarea id="surgery-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="اذكر نوع العملية والتاريخ..."></textarea>
                                        </div>
                                    </div>

                                    <!-- الأمراض المزمنة -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🩺 الأمراض المزمنة</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تعاني من أي أمراض مزمنة؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="chronic-diseases" value="yes" style="margin-left: 8px;" onchange="toggleChronicDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="chronic-diseases" value="no" style="margin-left: 8px;" onchange="toggleChronicDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="chronic-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الأمراض المزمنة:</label>
                                            <textarea id="chronic-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="مثل: السكري، ضغط الدم، أمراض القلب..."></textarea>
                                        </div>
                                    </div>

                                    <!-- الحمل (للإناث فقط) -->
                                    <div id="pregnancy-section" style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px); display: none;">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🤱 الحمل</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل أنت حامل حالياً؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="pregnancy" value="yes" style="margin-left: 8px;" onchange="togglePregnancyDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="pregnancy" value="no" style="margin-left: 8px;" onchange="togglePregnancyDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="pregnancy-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الحمل:</label>
                                            <input type="text" id="pregnancy-description" style="width: 100%; padding: 8px; border: none; border-radius: 5px;" placeholder="الشهر أو الأسبوع...">
                                        </div>
                                    </div>

                                    <!-- الرضاعة (للإناث فقط) -->
                                    <div id="breastfeeding-section" style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px); display: none;">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🍼 الرضاعة الطبيعية</h4>
                                        <div>
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل ترضعين طبيعياً حالياً؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="breastfeeding" value="yes" style="margin-left: 8px;"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="breastfeeding" value="no" style="margin-left: 8px;"> لا
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثاني من الأسئلة -->
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                                    <!-- المشروبات الكحولية -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🍷 المشروبات الكحولية</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تتناول المشروبات الكحولية؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="alcohol" value="yes" style="margin-left: 8px;" onchange="toggleAlcoholDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="alcohol" value="no" style="margin-left: 8px;" onchange="toggleAlcoholDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="alcohol-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الاستهلاك:</label>
                                            <input type="text" id="alcohol-description" style="width: 100%; padding: 8px; border: none; border-radius: 5px;" placeholder="الكمية والتكرار...">
                                        </div>
                                    </div>

                                    <!-- التدخين -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🚬 التدخين</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تدخن؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="smoking" value="yes" style="margin-left: 8px;" onchange="toggleSmokingDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="smoking" value="no" style="margin-left: 8px;" onchange="toggleSmokingDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="smoking-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل التدخين:</label>
                                            <input type="text" id="smoking-description" style="width: 100%; padding: 8px; border: none; border-radius: 5px;" placeholder="عدد السجائر يومياً، المدة...">
                                        </div>
                                    </div>

                                    <!-- الخطط الغذائية السابقة -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🍽️ الخطط الغذائية السابقة</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل اتبعت خطط غذائية سابقاً؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="previous-diets" value="yes" style="margin-left: 8px;" onchange="toggleDietDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="previous-diets" value="no" style="margin-left: 8px;" onchange="toggleDietDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="diet-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل الخطط السابقة:</label>
                                            <textarea id="diet-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="نوع النظام الغذائي والنتائج..."></textarea>
                                        </div>
                                    </div>

                                    <!-- النشاط البدني -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">🏃‍♂️ النشاط البدني</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تمارس الرياضة بانتظام؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="exercise" value="yes" style="margin-left: 8px;" onchange="toggleExerciseDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="exercise" value="no" style="margin-left: 8px;" onchange="toggleExerciseDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="exercise-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل النشاط البدني:</label>
                                            <input type="text" id="exercise-description" style="width: 100%; padding: 8px; border: none; border-radius: 5px;" placeholder="نوع الرياضة وعدد المرات أسبوعياً...">
                                        </div>
                                    </div>

                                    <!-- اضطرابات النوم -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">😴 اضطرابات النوم</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل تعاني من اضطرابات في النوم؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="sleep-disorders" value="yes" style="margin-left: 8px;" onchange="toggleSleepDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="sleep-disorders" value="no" style="margin-left: 8px;" onchange="toggleSleepDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="sleep-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل اضطرابات النوم:</label>
                                            <input type="text" id="sleep-description" style="width: 100%; padding: 8px; border: none; border-radius: 5px;" placeholder="أرق، شخير، انقطاع التنفس...">
                                        </div>
                                    </div>

                                    <!-- التاريخ العائلي للأمراض -->
                                    <div style="background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; backdrop-filter: blur(5px);">
                                        <h4 style="color: white; margin-bottom: 15px; font-size: 18px;">👨‍👩‍👧‍👦 التاريخ العائلي للأمراض</h4>
                                        <div style="margin-bottom: 15px;">
                                            <label style="display: block; margin-bottom: 8px; font-weight: bold;">هل يوجد تاريخ عائلي لأمراض مزمنة؟</label>
                                            <div style="display: flex; gap: 15px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="family-history" value="yes" style="margin-left: 8px;" onchange="toggleFamilyDetails(true)"> نعم
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="family-history" value="no" style="margin-left: 8px;" onchange="toggleFamilyDetails(false)"> لا
                                                </label>
                                            </div>
                                        </div>
                                        <div id="family-details" style="display: none;">
                                            <label style="display: block; margin-bottom: 5px; font-size: 14px;">تفاصيل التاريخ العائلي:</label>
                                            <textarea id="family-description" rows="2" style="width: 100%; padding: 8px; border: none; border-radius: 5px; resize: vertical;" placeholder="أمراض القلب، السكري، السرطان، ضغط الدم..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="submit" class="btn btn-success">✅ حفظ المريض</button>
                                <button type="button" onclick="hideAddPatientForm()" class="btn" style="background: #6c757d;">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Patients List -->
                    <div id="patients-list" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📋 قائمة المرضى</h3>
                        <div id="patients-table-container">
                            <!-- سيتم ملء الجدول بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- Search Patient -->
                    <div id="search-patient" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">🔍 البحث عن مريض</h3>
                        <div class="form-group">
                            <label>البحث بالاسم أو رقم الهاتف</label>
                            <input type="text" class="form-control" id="search-input" placeholder="أدخل اسم المريض أو رقم الهاتف" oninput="searchPatients()">
                        </div>
                        <div id="search-results">
                            <!-- سيتم عرض نتائج البحث هنا -->
                        </div>
                    </div>
                </div>

                <!-- Appointments Page -->
                <div id="appointments" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">📅 إدارة المواعيد</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <div style="text-align: center; margin-bottom: 40px;">
                        <button onclick="showBookAppointmentForm()" class="btn btn-success" style="margin: 10px;">➕ حجز موعد جديد</button>
                        <button onclick="showTodayAppointments()" class="btn" style="margin: 10px;">📅 مواعيد اليوم</button>
                        <button onclick="showAllAppointments()" class="btn btn-warning" style="margin: 10px;">📋 جميع المواعيد</button>
                    </div>

                    <!-- Book Appointment Form -->
                    <div id="book-appointment-form" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">➕ حجز موعد جديد</h3>
                        <form onsubmit="bookNewAppointment(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>اختيار المريض *</label>
                                    <div style="display: flex; gap: 10px; align-items: end;">
                                        <select class="form-control" id="appointment-patient-select" required onchange="loadPatientData()" style="flex: 1;">
                                            <option value="">-- اختر مريض مسجل --</option>
                                        </select>
                                        <button type="button" onclick="enableNewPatientMode()" style="background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; white-space: nowrap;">➕ مريض جديد</button>
                                    </div>
                                    <div id="new-patient-fields" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 2px solid #e9ecef;">
                                        <h5 style="color: #495057; margin-bottom: 15px;">👤 بيانات المريض الجديد</h5>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                            <div>
                                                <label style="font-size: 14px; color: #6c757d; margin-bottom: 5px; display: block;">الاسم الكامل *</label>
                                                <input type="text" id="new-patient-name" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 5px;">
                                            </div>
                                            <div>
                                                <label style="font-size: 14px; color: #6c757d; margin-bottom: 5px; display: block;">العمر</label>
                                                <input type="number" id="new-patient-age" min="1" max="120" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 5px;">
                                            </div>
                                        </div>
                                        <div style="margin-top: 10px; text-align: center;">
                                            <button type="button" onclick="disableNewPatientMode()" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 5px; cursor: pointer; font-size: 12px;">❌ إلغاء المريض الجديد</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="appointment-phone" required>
                                    <small style="color: #666; font-size: 12px;">سيتم تحميل الرقم تلقائياً عند اختيار مريض مسجل</small>
                                </div>
                                <div class="form-group">
                                    <label>تاريخ الموعد *</label>
                                    <input type="date" class="form-control" id="appointment-date" required>
                                </div>
                                <div class="form-group">
                                    <label>وقت الموعد *</label>
                                    <input type="time" class="form-control" id="appointment-time" required>
                                </div>
                                <div class="form-group">
                                    <label>نوع الزيارة *</label>
                                    <select class="form-control" id="appointment-type" required>
                                        <option value="">اختر نوع الزيارة</option>
                                        <option value="كشف عام">كشف عام</option>
                                        <option value="متابعة">متابعة</option>
                                        <option value="استشارة غذائية">استشارة غذائية</option>
                                        <option value="فحص دوري">فحص دوري</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>العمر</label>
                                    <input type="number" class="form-control" id="appointment-age" min="1" max="120" readonly style="background-color: #f8f9fa;">
                                    <small style="color: #666; font-size: 12px;">سيتم تحميل العمر تلقائياً من بيانات المريض</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea class="form-control" id="appointment-notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>

                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    <input type="checkbox" id="send-whatsapp-confirmation" style="width: auto;">
                                    إرسال تأكيد الموعد عبر واتساب
                                </label>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="submit" class="btn btn-success">✅ حجز الموعد</button>
                                <button type="button" onclick="sendTestWhatsApp()" class="btn" style="background: #25d366;">📱 اختبار واتساب</button>
                                <button type="button" onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                            </div>
                        </form>
                    </div>

                    <!-- Today's Appointments -->
                    <div id="today-appointments" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📅 مواعيد اليوم</h3>
                        <div id="today-appointments-container">
                            <!-- سيتم ملء المواعيد بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- All Appointments -->
                    <div id="all-appointments" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📋 جميع المواعيد</h3>
                        <div id="all-appointments-container">
                            <!-- سيتم ملء المواعيد بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Prescriptions Page -->
                <div id="prescriptions" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">📝 الوصفات الطبية</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <!-- إدارة الأدوية الأكثر استخداماً -->
                    <div style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h3 style="margin: 0; font-size: 20px;">💊 الأدوية الأكثر استخداماً</h3>
                            <button onclick="showAddCommonMedicineModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 15px; border-radius: 8px; cursor: pointer; font-weight: bold;">➕ إضافة دواء جديد</button>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px;" id="commonMedicinesGrid">
                            <!-- سيتم ملء الأدوية بواسطة JavaScript -->
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <button onclick="manageCommonMedicines()" style="background: rgba(255,255,255,0.15); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-size: 14px;">⚙️ إدارة الأدوية</button>
                        </div>
                    </div>

                    <div style="text-align: center; margin-bottom: 40px;">
                        <button onclick="showNewPrescriptionForm()" class="btn btn-success" style="margin: 10px;">➕ وصفة جديدة</button>
                        <button onclick="showPrescriptionsArchive()" class="btn" style="margin: 10px;">📚 أرشيف الوصفات</button>
                    </div>

                    <!-- New Prescription Form -->
                    <div id="new-prescription-form" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📝 كتابة وصفة طبية جديدة</h3>
                        <form onsubmit="savePrescription(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>اسم المريض *</label>
                                    <input type="text" class="form-control" id="prescription-patient-name" required>
                                </div>
                                <div class="form-group">
                                    <label>العمر</label>
                                    <input type="number" class="form-control" id="prescription-patient-age" min="1" max="120">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>التشخيص *</label>
                                <textarea class="form-control" id="prescription-diagnosis" rows="3" required placeholder="أدخل التشخيص..."></textarea>
                            </div>

                            <div class="form-group">
                                <label>الأدوية الموصوفة *</label>

                                <!-- إضافة دواء من القائمة المنسدلة -->
                                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #e9ecef;">
                                    <h5 style="color: #495057; margin-bottom: 15px;">💊 إضافة دواء من القائمة</h5>
                                    <div style="display: grid; grid-template-columns: 2fr 1fr auto; gap: 10px; align-items: end;">
                                        <div>
                                            <label style="font-size: 14px; color: #6c757d; margin-bottom: 5px; display: block;">اختر الدواء</label>
                                            <select id="commonMedicineSelect" style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 5px;">
                                                <option value="">-- اختر دواء من القائمة --</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 14px; color: #6c757d; margin-bottom: 5px; display: block;">الجرعة المخصصة</label>
                                            <input type="text" id="customDosage" placeholder="مثال: حبة واحدة مرتين يومياً" style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 5px;">
                                        </div>
                                        <button type="button" onclick="addMedicineFromList()" style="background: #28a745; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; height: 42px;">➕ إضافة</button>
                                    </div>
                                </div>

                                <!-- إضافة دواء يدوياً -->
                                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #ffeaa7;">
                                    <h5 style="color: #856404; margin-bottom: 15px;">✏️ إضافة دواء يدوياً</h5>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 10px; align-items: end;">
                                        <div>
                                            <label style="font-size: 14px; color: #856404; margin-bottom: 5px; display: block;">اسم الدواء</label>
                                            <input type="text" id="manualMedicineName" placeholder="أدخل اسم الدواء" style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 14px; color: #856404; margin-bottom: 5px; display: block;">الجرعة والتعليمات</label>
                                            <input type="text" id="manualMedicineDosage" placeholder="مثال: حبة واحدة مرتين يومياً" style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;">
                                        </div>
                                        <button type="button" onclick="addManualMedicine()" style="background: #ffc107; color: #212529; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; height: 42px; font-weight: bold;">➕ إضافة</button>
                                    </div>
                                </div>

                                <!-- قائمة الأدوية المضافة -->
                                <div style="background: white; border: 2px solid #dee2e6; border-radius: 10px; padding: 15px;">
                                    <h5 style="color: #495057; margin-bottom: 15px;">📋 الأدوية المضافة للوصفة</h5>
                                    <div id="addedMedicinesList" style="min-height: 100px;">
                                        <p style="color: #6c757d; text-align: center; margin: 40px 0;">لم يتم إضافة أي أدوية بعد</p>
                                    </div>
                                </div>

                                <!-- الحقل المخفي للأدوية (للحفظ) -->
                                <textarea class="form-control hidden" id="prescription-medications" rows="5" required></textarea>
                            </div>

                            <div class="form-group">
                                <label>تعليمات خاصة</label>
                                <textarea class="form-control" id="prescription-instructions" rows="3" placeholder="تعليمات إضافية للمريض..."></textarea>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="button" onclick="saveAndPrintPrescription()" class="btn" style="background: #17a2b8;">🖨️ حفظ وطباعة</button>
                                <button type="button" onclick="sendPrescriptionWhatsApp()" class="btn" style="background: #25d366;">📱 إرسال بالواتساب</button>
                                <button type="button" onclick="testClinicSettings()" class="btn" style="background: #ffc107; color: #000;">🔧 اختبار الإعدادات</button>
                                <button type="button" onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                            </div>
                        </form>
                    </div>

                    <!-- Prescriptions Archive -->
                    <div id="prescriptions-archive" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📚 أرشيف الوصفات الطبية</h3>
                        <div id="prescriptions-list">
                            <!-- سيتم ملء الأرشيف بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Nutrition Page -->
                <div id="nutrition" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">🍽️ الخطط الغذائية</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <div style="text-align: center; margin-bottom: 40px;">
                        <button onclick="showCreateNutritionPlan()" class="btn btn-success" style="margin: 10px;">➕ خطة جديدة</button>
                        <button onclick="showNutritionTemplates()" class="btn" style="background: #e67e22; margin: 10px;">📋 القوالب الجاهزة</button>
                        <button onclick="showSavedNutritionPlans()" class="btn" style="margin: 10px;">📋 الخطط المحفوظة</button>
                    </div>

                    <!-- New Nutrition Plan Form -->
                    <div id="new-nutrition-plan-form" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">🍽️ إنشاء خطة غذائية جديدة</h3>
                        <form onsubmit="saveNutritionPlan(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>اسم المريض *</label>
                                    <input type="text" class="form-control" id="nutrition-patient-name" required>
                                </div>
                                <div class="form-group">
                                    <label>العمر *</label>
                                    <input type="number" class="form-control" id="nutrition-patient-age" min="1" max="120" required>
                                </div>
                            </div>

                            <!-- Meals Section -->
                            <div style="margin-top: 30px;">
                                <h4 style="color: #2c3e50; margin-bottom: 20px;">🍽️ الوجبات اليومية</h4>

                                <!-- Breakfast -->
                                <div class="form-group">
                                    <label>🌅 الإفطار</label>
                                    <textarea class="form-control" id="nutrition-breakfast" rows="3" placeholder="أدخل وجبة الإفطار..."></textarea>
                                </div>

                                <!-- Morning Snack -->
                                <div class="form-group">
                                    <label>☕ وجبة خفيفة صباحية</label>
                                    <textarea class="form-control" id="nutrition-morning-snack" rows="2" placeholder="أدخل الوجبة الخفيفة الصباحية..."></textarea>
                                </div>

                                <!-- Lunch -->
                                <div class="form-group">
                                    <label>🍽️ الغداء</label>
                                    <textarea class="form-control" id="nutrition-lunch" rows="3" placeholder="أدخل وجبة الغداء..."></textarea>
                                </div>

                                <!-- Evening Snack -->
                                <div class="form-group">
                                    <label>🍎 وجبة خفيفة مسائية</label>
                                    <textarea class="form-control" id="nutrition-evening-snack" rows="2" placeholder="أدخل الوجبة الخفيفة المسائية..."></textarea>
                                </div>

                                <!-- Dinner -->
                                <div class="form-group">
                                    <label>🌙 العشاء</label>
                                    <textarea class="form-control" id="nutrition-dinner" rows="3" placeholder="أدخل وجبة العشاء..."></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>💡 نصائح وتعليمات</label>
                                <textarea class="form-control" id="nutrition-notes" rows="3" placeholder="نصائح غذائية وتعليمات خاصة..."></textarea>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="button" onclick="saveAndPrintNutritionPlan()" class="btn" style="background: #17a2b8;">🖨️ حفظ وطباعة</button>
                                <button type="button" onclick="showSavedNutritionPlans()" class="btn" style="background: #28a745;">📋 عرض الخطط المحفوظة</button>
                                <button type="button" onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                            </div>
                        </form>
                    </div>

                    <!-- Saved Plans -->
                    <div id="saved-nutrition-plans" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📋 الخطط الغذائية المحفوظة</h3>
                        <div id="saved-plans-container">
                            <!-- سيتم ملء الخطط بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- Nutrition Templates -->
                    <div id="nutrition-templates" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📋 القوالب الغذائية الجاهزة</h3>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin-bottom: 30px;">
                            <!-- قالب إنقاص الوزن -->
                            <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="font-size: 48px; margin-bottom: 10px;">⬇️</div>
                                    <h4 style="margin: 0; font-size: 22px;">خطة إنقاص الوزن</h4>
                                    <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 14px;">نظام غذائي متوازن لفقدان الوزن الصحي</p>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                    <h6 style="margin: 0 0 10px 0; font-size: 14px;">🎯 الأهداف:</h6>
                                    <ul style="margin: 0; padding-right: 20px; font-size: 13px; line-height: 1.6;">
                                        <li>تقليل السعرات الحرارية بشكل صحي</li>
                                        <li>زيادة البروتين والألياف</li>
                                        <li>تقليل الكربوهيدرات المكررة</li>
                                        <li>شرب الماء بكثرة</li>
                                    </ul>
                                </div>
                                <button onclick="useNutritionTemplate('weight_loss')" style="width: 100%; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">📋 استخدام هذا القالب</button>
                            </div>

                            <!-- قالب زيادة الوزن -->
                            <div style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="font-size: 48px; margin-bottom: 10px;">⬆️</div>
                                    <h4 style="margin: 0; font-size: 22px;">خطة زيادة الوزن</h4>
                                    <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 14px;">نظام غذائي عالي السعرات لزيادة الوزن الصحي</p>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                    <h6 style="margin: 0 0 10px 0; font-size: 14px;">🎯 الأهداف:</h6>
                                    <ul style="margin: 0; padding-right: 20px; font-size: 13px; line-height: 1.6;">
                                        <li>زيادة السعرات الحرارية الصحية</li>
                                        <li>التركيز على البروتين والدهون الصحية</li>
                                        <li>وجبات متعددة ومتكررة</li>
                                        <li>تناول المكسرات والفواكه المجففة</li>
                                    </ul>
                                </div>
                                <button onclick="useNutritionTemplate('weight_gain')" style="width: 100%; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">📋 استخدام هذا القالب</button>
                            </div>

                            <!-- قالب زيادة الكتلة العضلية -->
                            <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="font-size: 48px; margin-bottom: 10px;">💪</div>
                                    <h4 style="margin: 0; font-size: 22px;">خطة زيادة الكتلة العضلية</h4>
                                    <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 14px;">نظام غذائي عالي البروتين لبناء العضلات</p>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                    <h6 style="margin: 0 0 10px 0; font-size: 14px;">🎯 الأهداف:</h6>
                                    <ul style="margin: 0; padding-right: 20px; font-size: 13px; line-height: 1.6;">
                                        <li>تناول البروتين عالي الجودة</li>
                                        <li>كربوهيدرات معقدة للطاقة</li>
                                        <li>وجبات ما قبل وبعد التمرين</li>
                                        <li>مكملات البروتين الطبيعية</li>
                                    </ul>
                                </div>
                                <button onclick="useNutritionTemplate('muscle_gain')" style="width: 100%; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">📋 استخدام هذا القالب</button>
                            </div>

                            <!-- قالب مريض السكري -->
                            <div style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="font-size: 48px; margin-bottom: 10px;">🩺</div>
                                    <h4 style="margin: 0; font-size: 22px;">خطة مريض السكري</h4>
                                    <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 14px;">نظام غذائي متوازن للتحكم في السكري</p>
                                </div>
                                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                    <h6 style="margin: 0 0 10px 0; font-size: 14px;">🎯 الأهداف:</h6>
                                    <ul style="margin: 0; padding-right: 20px; font-size: 13px; line-height: 1.6;">
                                        <li>التحكم في مستوى السكر</li>
                                        <li>تجنب السكريات المكررة</li>
                                        <li>الألياف والحبوب الكاملة</li>
                                        <li>وجبات صغيرة ومتكررة</li>
                                    </ul>
                                </div>
                                <button onclick="useNutritionTemplate('diabetes')" style="width: 100%; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">📋 استخدام هذا القالب</button>
                            </div>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #3498db;">
                            <h5 style="color: #2c3e50; margin: 0 0 10px 0;">📝 ملاحظة مهمة</h5>
                            <p style="margin: 0; color: #666; line-height: 1.6;">
                                هذه القوالب هي اقتراحات عامة ويجب تخصيصها حسب احتياجات كل مريض.
                                يُنصح بمراجعة أخصائي التغذية قبل تطبيق أي نظام غذائي، خاصة للمرضى الذين يعانون من حالات صحية خاصة.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Accounting Page -->
                <div id="accounting" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">💰 إدارة المحاسبة</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <div style="text-align: center; margin-bottom: 40px;">
                        <button onclick="showAddIncomeForm()" class="btn btn-success" style="margin: 10px;">➕ إضافة إيراد</button>
                        <button onclick="showAddExpenseForm()" class="btn btn-danger" style="margin: 10px;">➖ إضافة مصروف</button>
                        <button onclick="showFinancialReport()" class="btn" style="margin: 10px;">📊 التقرير المالي</button>
                        <button onclick="showAccountsArchive()" class="btn btn-warning" style="margin: 10px;">📚 أرشيف الحسابات</button>
                    </div>

                    <!-- Add Income Form -->
                    <div id="add-income-form" class="hidden">
                        <h3 style="color: #27ae60; margin-bottom: 30px; text-align: center;">➕ إضافة إيراد جديد</h3>
                        <form onsubmit="addNewIncome(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>نوع الإيراد *</label>
                                    <select class="form-control" id="income-type" required>
                                        <option value="">اختر نوع الإيراد</option>
                                        <option value="كشف عام">كشف عام</option>
                                        <option value="متابعة">متابعة</option>
                                        <option value="استشارة غذائية">استشارة غذائية</option>
                                        <option value="فحص دوري">فحص دوري</option>
                                        <option value="خدمات أخرى">خدمات أخرى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>المبلغ (د.ع) *</label>
                                    <input type="number" class="form-control" id="income-amount" min="0" step="1000" required>
                                </div>
                                <div class="form-group">
                                    <label>اسم المريض</label>
                                    <input type="text" class="form-control" id="income-patient-name">
                                </div>
                                <div class="form-group">
                                    <label>التاريخ *</label>
                                    <input type="date" class="form-control" id="income-date" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea class="form-control" id="income-notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="submit" class="btn btn-success">✅ حفظ الإيراد</button>
                                <button type="button" onclick="hideAllAccountingSections()" class="btn" style="background: #6c757d;">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Add Expense Form -->
                    <div id="add-expense-form" class="hidden">
                        <h3 style="color: #e74c3c; margin-bottom: 30px; text-align: center;">➖ إضافة مصروف جديد</h3>
                        <form onsubmit="addNewExpense(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>نوع المصروف *</label>
                                    <select class="form-control" id="expense-type" required>
                                        <option value="">اختر نوع المصروف</option>
                                        <option value="أدوية ومستلزمات طبية">أدوية ومستلزمات طبية</option>
                                        <option value="إيجار العيادة">إيجار العيادة</option>
                                        <option value="فواتير الكهرباء والماء">فواتير الكهرباء والماء</option>
                                        <option value="رواتب الموظفين">رواتب الموظفين</option>
                                        <option value="صيانة وتطوير">صيانة وتطوير</option>
                                        <option value="تسويق وإعلان">تسويق وإعلان</option>
                                        <option value="مصاريف إدارية">مصاريف إدارية</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>المبلغ (د.ع) *</label>
                                    <input type="number" class="form-control" id="expense-amount" min="0" step="1000" required>
                                </div>
                                <div class="form-group">
                                    <label>المورد/الجهة</label>
                                    <input type="text" class="form-control" id="expense-vendor">
                                </div>
                                <div class="form-group">
                                    <label>التاريخ *</label>
                                    <input type="date" class="form-control" id="expense-date" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>تفاصيل المصروف</label>
                                <textarea class="form-control" id="expense-details" rows="3" placeholder="تفاصيل المصروف..."></textarea>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="submit" class="btn btn-danger">✅ حفظ المصروف</button>
                                <button type="button" onclick="hideAllAccountingSections()" class="btn" style="background: #6c757d;">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Financial Report -->
                    <div id="financial-report" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📊 التقرير المالي</h3>

                        <!-- Financial Summary Cards -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                            <div style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 18px; border-radius: 12px; text-align: center;">
                                <h4 style="margin-bottom: 12px; font-size: 16px;">إجمالي الإيرادات</h4>
                                <div style="font-size: 26px; font-weight: bold; direction: ltr;" id="total-income">0</div>
                                <div style="font-size: 12px; margin-top: 4px;">IQD</div>
                            </div>

                            <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 18px; border-radius: 12px; text-align: center;">
                                <h4 style="margin-bottom: 12px; font-size: 16px;">إجمالي المصروفات</h4>
                                <div style="font-size: 26px; font-weight: bold; direction: ltr;" id="total-expenses">0</div>
                                <div style="font-size: 12px; margin-top: 4px;">IQD</div>
                            </div>

                            <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 18px; border-radius: 12px; text-align: center;">
                                <h4 style="margin-bottom: 12px; font-size: 16px;">صافي الربح</h4>
                                <div style="font-size: 26px; font-weight: bold; direction: ltr;" id="net-profit">0</div>
                                <div style="font-size: 12px; margin-top: 4px;">IQD</div>
                            </div>
                        </div>

                        <!-- Monthly Filter -->
                        <div style="text-align: center; margin-bottom: 30px;">
                            <label style="margin-left: 10px; font-weight: bold;">فلترة حسب الشهر:</label>
                            <select id="month-filter" class="form-control" style="width: 200px; display: inline-block;" onchange="updateFinancialReport()">
                                <option value="">جميع الأشهر</option>
                                <option value="01">يناير</option>
                                <option value="02">فبراير</option>
                                <option value="03">مارس</option>
                                <option value="04">أبريل</option>
                                <option value="05">مايو</option>
                                <option value="06">يونيو</option>
                                <option value="07">يوليو</option>
                                <option value="08">أغسطس</option>
                                <option value="09">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                            <button onclick="printFinancialReport()" class="btn" style="background: #17a2b8; margin-right: 10px;">🖨️ طباعة التقرير</button>
                        </div>

                        <!-- Recent Transactions -->
                        <div id="recent-transactions">
                            <!-- سيتم ملء المعاملات الأخيرة بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- Accounts Archive -->
                    <div id="accounts-archive" class="hidden">
                        <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">📚 أرشيف الحسابات</h3>

                        <div style="text-align: center; margin-bottom: 30px;">
                            <button onclick="showIncomeArchive()" class="btn btn-success" style="margin: 10px;">💰 أرشيف الإيرادات</button>
                            <button onclick="showExpenseArchive()" class="btn btn-danger" style="margin: 10px;">💸 أرشيف المصروفات</button>
                        </div>

                        <div id="archive-content">
                            <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Medicine Ads Management Page -->
                <div id="medicine-ads" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">📢 إدارة الإعلانات الدوائية</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <!-- إحصائيات الإعلانات -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap: 15px; margin-bottom: 25px;">
                        <div style="background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; margin-bottom: 4px;" id="total-ads-count">0</div>
                            <div style="font-size: 12px; opacity: 0.9;">إجمالي الإعلانات</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; margin-bottom: 4px;" id="active-ads-count">0</div>
                            <div style="font-size: 12px; opacity: 0.9;">الإعلانات النشطة</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; margin-bottom: 4px;" id="total-views">0</div>
                            <div style="font-size: 12px; opacity: 0.9;">إجمالي المشاهدات</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; margin-bottom: 4px;" id="total-clicks">0</div>
                            <div style="font-size: 12px; opacity: 0.9;">إجمالي النقرات</div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <button onclick="showAddMedicineAdForm()" class="btn btn-success" style="margin: 10px;">➕ إضافة إعلان دوائي جديد</button>
                        <button onclick="showAllMedicineAds()" class="btn" style="margin: 10px;">👁️ عرض جميع الإعلانات</button>
                        <button onclick="showAdsAnalytics()" class="btn btn-warning" style="margin: 10px;">📊 إحصائيات الإعلانات</button>
                        <button onclick="exportAdsData()" class="btn" style="background: #6c757d; margin: 10px;">📤 تصدير البيانات</button>
                    </div>

                    <!-- قائمة الإعلانات -->
                    <div id="ads-list-container" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 12px rgba(30, 41, 59, 0.06); border: 2px solid #e2e8f0;">
                        <h3 style="color: #1e293b; margin-bottom: 20px; text-align: center;">📋 قائمة الإعلانات الدوائية</h3>
                        <div id="medicine-ads-list">
                            <!-- سيتم ملء الإعلانات بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- نموذج إضافة إعلان دوائي -->
                    <div id="add-medicine-ad-form" class="hidden" style="background: white; border-radius: 12px; padding: 25px; margin-top: 20px; box-shadow: 0 4px 12px rgba(30, 41, 59, 0.06); border: 2px solid #e2e8f0;">
                        <h3 style="color: #1e293b; margin-bottom: 20px; text-align: center;">➕ إضافة إعلان دوائي جديد</h3>
                        <form id="medicine-ad-form" onsubmit="saveMedicineAd(event)">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <!-- معلومات أساسية -->
                                <div>
                                    <h4 style="color: #475569; margin-bottom: 15px;">📋 المعلومات الأساسية</h4>
                                    <div class="form-group">
                                        <label for="adMedicineName">اسم الدواء *</label>
                                        <input type="text" id="adMedicineName" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="adCompany">اسم الشركة المصنعة *</label>
                                        <input type="text" id="adCompany" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="adCategory">فئة الدواء</label>
                                        <select id="adCategory" class="form-control">
                                            <option value="">اختر الفئة</option>
                                            <option value="مضادات حيوية">مضادات حيوية</option>
                                            <option value="مسكنات">مسكنات</option>
                                            <option value="فيتامينات">فيتامينات ومكملات</option>
                                            <option value="أدوية القلب">أدوية القلب والأوعية</option>
                                            <option value="أدوية الجهاز الهضمي">أدوية الجهاز الهضمي</option>
                                            <option value="أدوية الجهاز التنفسي">أدوية الجهاز التنفسي</option>
                                            <option value="أدوية الأطفال">أدوية الأطفال</option>
                                            <option value="أخرى">أخرى</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="adPrice">السعر (د.ع)</label>
                                        <input type="number" id="adPrice" class="form-control" min="0">
                                    </div>
                                </div>

                                <!-- تفاصيل الدواء -->
                                <div>
                                    <h4 style="color: #475569; margin-bottom: 15px;">💊 تفاصيل الدواء</h4>
                                    <div class="form-group">
                                        <label for="adDescription">وصف الدواء *</label>
                                        <textarea id="adDescription" class="form-control" rows="3" required placeholder="وصف مختصر عن الدواء واستخداماته"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="adAvailability">حالة التوفر</label>
                                        <select id="adAvailability" class="form-control">
                                            <option value="متوفر">متوفر</option>
                                            <option value="غير متوفر">غير متوفر</option>
                                            <option value="قريباً">قريباً</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="adPriority">أولوية الإعلان</label>
                                        <select id="adPriority" class="form-control">
                                            <option value="عادي">عادي</option>
                                            <option value="مهم">مهم</option>
                                            <option value="عاجل">عاجل</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="adActive">حالة الإعلان</label>
                                        <select id="adActive" class="form-control">
                                            <option value="true">نشط</option>
                                            <option value="false">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- صورة الدواء -->
                            <div style="margin-top: 20px;">
                                <h4 style="color: #475569; margin-bottom: 15px;">🖼️ صورة الدواء</h4>
                                <div id="uploadArea" style="border: 2px dashed #cbd5e1; border-radius: 8px; padding: 20px; text-align: center; background: #f8fafc; cursor: pointer; transition: all 0.3s ease;"
                                     onclick="document.getElementById('adImageFile').click()"
                                     ondragover="handleDragOver(event)"
                                     ondrop="handleDrop(event)">
                                    <div style="font-size: 48px; margin-bottom: 10px;">📷</div>
                                    <p style="margin: 0; color: #64748b;">انقر هنا لرفع صورة الدواء أو اسحب الصورة إلى هنا</p>
                                    <p style="margin: 5px 0 0 0; font-size: 12px; color: #94a3b8;">الحد الأقصى: 5MB | الصيغ المدعومة: JPG, PNG, GIF, WebP</p>
                                </div>
                                <input type="file" id="adImageFile" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">

                                <!-- معاينة الصورة -->
                                <div id="imagePreviewContainer" style="display: none; margin-top: 15px; text-align: center;">
                                    <img id="imagePreview" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #e2e8f0;">
                                    <div style="margin-top: 10px;">
                                        <button type="button" onclick="removeImage()" class="btn btn-danger" style="font-size: 12px; padding: 5px 10px;">🗑️ إزالة الصورة</button>
                                    </div>
                                    <div id="imageInfo" style="margin-top: 10px; font-size: 12px; color: #64748b;"></div>
                                </div>
                            </div>

                            <!-- أزرار التحكم -->
                            <div style="text-align: center; margin-top: 25px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                                <button type="button" onclick="previewMedicineAd()" class="btn btn-warning" style="margin: 5px;">👁️ معاينة الإعلان</button>
                                <button type="submit" class="btn btn-success" style="margin: 5px;">✅ حفظ الإعلان</button>
                                <button type="button" onclick="cancelAddMedicineAd()" class="btn" style="background: #6c757d; margin: 5px;">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Settings Page -->
                <div id="settings" class="page">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <h2 class="page-title" style="margin: 0;">⚙️ إعدادات العيادة</h2>
                        <button onclick="goBack()" class="btn" style="background: #6c757d;">← العودة للصفحة السابقة</button>
                    </div>

                    <!-- إشعار الحماية -->
                    <div style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 30px; text-align: center; box-shadow: 0 4px 15px rgba(15, 118, 110, 0.3);">
                        <div style="font-size: 32px; margin-bottom: 10px;">🔐</div>
                        <h3 style="margin: 0 0 10px 0; font-size: 20px;">صفحة محمية</h3>
                        <p style="margin: 0; opacity: 0.9; font-size: 16px;">تم الوصول لهذه الصفحة بنجاح بعد المصادقة من لوحة الإدارة العامة</p>
                        <div style="margin-top: 15px; font-size: 14px; opacity: 0.8;">
                            <span>🕒 صلاحية الجلسة: 30 دقيقة</span> |
                            <span>🔒 مستوى الأمان: عالي</span>
                        </div>
                    </div>

                    <!-- قسم إعدادات البرنامج -->
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px; border: 2px solid #e9ecef;">
                        <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            <span style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 8px; border-radius: 8px;">🎨</span>
                            إعدادات البرنامج
                        </h3>

                        <div id="program-settings-display" style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                            <div style="text-align: center; margin-bottom: 20px;">
                                <div id="current-program-logo" style="margin-bottom: 15px;">
                                    <div style="color: #666; font-size: 48px;">🏥</div>
                                </div>
                                <h4 id="current-program-name" style="color: #2c3e50; margin: 0;">نظام إدارة العيادات</h4>
                                <p id="current-program-description" style="color: #666; margin: 10px 0; display: none;"></p>
                                <small id="current-program-version" style="color: #999; display: none;"></small>
                            </div>

                            <div style="text-align: center; padding-top: 15px; border-top: 1px solid #e9ecef;">
                                <button type="button" onclick="refreshProgramSettings()" style="background: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">🔄 تحديث إعدادات البرنامج</button>
                                <small style="display: block; color: #666; margin-top: 10px;">يتم تحميل هذه الإعدادات من لوحة الإدارة العامة</small>
                            </div>
                        </div>
                    </div>

                    <!-- قسم إدارة المستخدمين -->
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px; border: 2px solid #e9ecef;">
                        <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            <span style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white; padding: 8px; border-radius: 8px;">👥</span>
                            إدارة المستخدمين
                        </h3>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
                            <h6 style="color: #856404; margin: 0 0 5px 0;">⚠️ ملاحظة مهمة</h6>
                            <p style="margin: 0; color: #856404; font-size: 14px;">
                                <strong>الحد الأقصى للمستخدمين</strong> يتم تحديده من الإدارة العامة فقط.
                                باقي إعدادات المستخدمين (الإضافة، التعديل، الحذف، الصلاحيات) تحت تحكم admin العيادة.
                            </p>
                        </div>

                        <div id="users-management-section">
                            <!-- معلومات الحد الأقصى للمستخدمين -->
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h5 style="color: #1976d2; margin: 0 0 5px 0;">📊 حالة المستخدمين</h5>
                                        <p style="margin: 0; color: #424242;">
                                            <span id="current-users-count">0</span> من <span id="max-users-limit">غير محدد</span> مستخدم
                                        </p>
                                        <small style="color: #666; font-size: 12px;">الحد الأقصى محدد من الإدارة العامة</small>
                                    </div>
                                    <button type="button" onclick="refreshUsersLimit()" style="background: #2196f3; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 12px;">🔄 تحديث الحد</button>
                                </div>
                            </div>

                            <!-- قائمة المستخدمين الحاليين -->
                            <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <h5 style="color: #495057; margin: 0;">👤 المستخدمين الحاليين</h5>
                                    <button type="button" onclick="addNewUser()" id="add-user-btn" style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 14px;">➕ إضافة مستخدم</button>
                                </div>
                                <div id="users-list">
                                    <p style="color: #666; text-align: center; padding: 20px;">لا توجد مستخدمين مضافين</p>
                                </div>
                            </div>

                            <!-- نموذج إضافة مستخدم جديد -->
                            <div id="add-user-form" style="display: none; background: #fff3cd; padding: 20px; border-radius: 10px; border: 2px solid #ffeaa7; margin-bottom: 20px;">
                                <h5 style="color: #856404; margin-bottom: 15px;">➕ إضافة مستخدم جديد</h5>
                                <form id="new-user-form" onsubmit="saveNewUser(event)">
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #856404;">اسم المستخدم *</label>
                                            <input type="text" id="new-username" required style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #856404;">كلمة المرور *</label>
                                            <div style="position: relative;">
                                                <input type="password" id="new-password" required style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;">
                                                <button type="button" onclick="togglePasswordVisibility('new-password')" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">👁️</button>
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #856404;">الاسم الكامل *</label>
                                            <input type="text" id="new-fullname" required style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #856404;">المسمى الوظيفي</label>
                                            <input type="text" id="new-job-title" style="width: 100%; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;" placeholder="مثال: ممرض، سكرتير، مساعد طبيب">
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #856404;">الصلاحيات *</label>
                                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="patients" style="margin-left: 8px;">
                                                👥 إدارة المرضى
                                            </label>
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="appointments" style="margin-left: 8px;">
                                                📅 إدارة المواعيد
                                            </label>
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="prescriptions" style="margin-left: 8px;">
                                                📝 الوصفات الطبية
                                            </label>
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="nutrition" style="margin-left: 8px;">
                                                🍎 الخطط الغذائية
                                            </label>
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="accounting" style="margin-left: 8px;">
                                                💰 المحاسبة
                                            </label>
                                            <label style="display: flex; align-items: center; background: white; padding: 10px; border-radius: 5px; cursor: pointer;">
                                                <input type="checkbox" name="permissions" value="reports" style="margin-left: 8px;">
                                                📊 التقارير
                                            </label>
                                        </div>
                                    </div>

                                    <div style="text-align: center;">
                                        <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 5px;">✅ حفظ المستخدم</button>
                                        <button type="button" onclick="cancelAddUser()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات العيادة الأساسية -->
                    <div style="background: white; padding: 25px; border-radius: 15px; margin-bottom: 30px; border: 2px solid #e9ecef;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 8px; border-radius: 8px;">🏥</span>
                            إعدادات العيادة الأساسية
                        </h4>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                            <h6 style="color: #155724; margin: 0 0 5px 0;">🔒 إعدادات محلية</h6>
                            <p style="margin: 0; color: #155724; font-size: 14px;">
                                هذه الإعدادات تحت تحكم admin العيادة ولا تتأثر بالإدارة العامة.
                                يمكن تعديلها حسب احتياجات العيادة المحددة.
                            </p>
                        </div>

                        <form onsubmit="saveClinicSettings(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label>اسم الطبيب *</label>
                                    <input type="text" class="form-control" id="settings-doctor-name" value="د. أحمد محمد علي" required>
                                </div>
                                <div class="form-group">
                                    <label>اسم العيادة *</label>
                                    <input type="text" class="form-control" id="settings-clinic-name" value="عيادة الدكتور أحمد محمد علي" required>
                                </div>
                                <div class="form-group">
                                    <label>التخصص *</label>
                                    <input type="text" class="form-control" id="settings-specialty" value="أخصائي الطب الباطني والتغذية العلاجية" required>
                                </div>
                                <div class="form-group">
                                    <label>رقم الترخيص *</label>
                                    <input type="text" class="form-control" id="settings-license" value="12345" required>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="settings-phone" value="************" required>
                                </div>
                                <div class="form-group">
                                    <label>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="settings-email" value="<EMAIL>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>العنوان *</label>
                                <textarea class="form-control" id="settings-address" rows="2" required>شارع الملك فهد، الرياض</textarea>
                            </div>

                            <div class="form-group">
                                <label>شعار العيادة</label>
                                <input type="text" class="form-control" id="settings-logo" value="🏥" placeholder="أدخل رمز تعبيري أو نص">
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button type="submit" class="btn btn-success">🚀 تطبيق التغييرات</button>
                                <button type="button" onclick="resetSettings()" class="btn btn-warning">🔄 إعادة تعيين</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        var navigationHistory = ['dashboard'];
        var currentPage = 'dashboard';

        // دالة العودة للصفحة السابقة
        function goBack() {
            console.log('🔙 محاولة العودة للصفحة السابقة');
            console.log('📋 تاريخ التنقل الحالي:', navigationHistory);

            if (navigationHistory.length > 1) {
                navigationHistory.pop();
                var previousPage = navigationHistory[navigationHistory.length - 1];
                console.log('⬅️ العودة إلى:', previousPage);
                showPage(previousPage);
            } else {
                console.log('🏠 العودة للوحة التحكم (افتراضي)');
                showPage('dashboard');
            }
        }

        // دالة العودة للوحة التحكم مباشرة
        function goHome() {
            console.log('🏠 العودة للوحة التحكم');
            navigationHistory = ['dashboard'];
            showPage('dashboard');
        }

        // دالة عرض الصفحات
        function showPage(pageId) {
            console.log('📄 الانتقال إلى صفحة:', pageId);

            // إخفاء جميع الصفحات
            var pages = document.querySelectorAll('.page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }

            // إزالة active من جميع عناصر التنقل
            var navItems = document.querySelectorAll('.nav-item');
            for (var i = 0; i < navItems.length; i++) {
                navItems[i].classList.remove('active');
            }

            // عرض الصفحة المطلوبة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                currentPage = pageId;

                // تحديث عنوان الصفحة
                var titles = {
                    'dashboard': 'لوحة التحكم',
                    'patients': 'إدارة المرضى',
                    'appointments': 'إدارة المواعيد',
                    'prescriptions': 'الوصفات الطبية',
                    'nutrition': 'الخطط الغذائية',
                    'accounting': 'إدارة المحاسبة',
                    'medicine-ads': 'إدارة الإعلانات الدوائية',
                    'settings': 'إعدادات العيادة'
                };

                var pageTitle = document.getElementById('page-title');
                if (pageTitle && titles[pageId]) {
                    pageTitle.textContent = titles[pageId];
                }

                // تحديث التنقل النشط
                var activeNav = document.querySelector('a[href="#' + pageId + '"]');
                if (activeNav) {
                    activeNav.classList.add('active');
                }

                // إضافة الصفحة لتاريخ التنقل
                if (navigationHistory[navigationHistory.length - 1] !== pageId) {
                    navigationHistory.push(pageId);

                    // الاحتفاظ بآخر 10 صفحات فقط
                    if (navigationHistory.length > 10) {
                        navigationHistory.shift();
                    }
                }

                // تحميل البيانات الخاصة بكل صفحة
                if (pageId === 'settings') {
                    loadUsersManagement();
                    loadProgramSettings();
                }

                console.log('📋 تاريخ التنقل المحدث:', navigationHistory);
            }
        }

        // دوال إدارة المرضى
        function showAddPatientForm() {
            hideAllPatientSections();
            document.getElementById('add-patient-form').classList.remove('hidden');
        }

        function hideAddPatientForm() {
            document.getElementById('add-patient-form').classList.add('hidden');
        }

        function showPatientsList() {
            hideAllPatientSections();
            document.getElementById('patients-list').classList.remove('hidden');
            loadPatientsList();
        }

        function showSearchPatient() {
            hideAllPatientSections();
            document.getElementById('search-patient').classList.remove('hidden');
        }

        function hideAllPatientSections() {
            document.getElementById('add-patient-form').classList.add('hidden');
            document.getElementById('patients-list').classList.add('hidden');
            document.getElementById('search-patient').classList.add('hidden');
        }

        // دوال الاستبيان الطبي
        function toggleAllergyDetails(show) {
            document.getElementById('allergy-details').style.display = show ? 'block' : 'none';
        }

        function toggleMedicationDetails(show) {
            document.getElementById('medication-details').style.display = show ? 'block' : 'none';
        }

        function toggleSurgeryDetails(show) {
            document.getElementById('surgery-details').style.display = show ? 'block' : 'none';
        }

        function toggleChronicDetails(show) {
            document.getElementById('chronic-details').style.display = show ? 'block' : 'none';
        }

        function togglePregnancyDetails(show) {
            document.getElementById('pregnancy-details').style.display = show ? 'block' : 'none';
        }

        function toggleAlcoholDetails(show) {
            document.getElementById('alcohol-details').style.display = show ? 'block' : 'none';
        }

        function toggleSmokingDetails(show) {
            document.getElementById('smoking-details').style.display = show ? 'block' : 'none';
        }

        function toggleDietDetails(show) {
            document.getElementById('diet-details').style.display = show ? 'block' : 'none';
        }

        function toggleExerciseDetails(show) {
            document.getElementById('exercise-details').style.display = show ? 'block' : 'none';
        }

        function toggleSleepDetails(show) {
            document.getElementById('sleep-details').style.display = show ? 'block' : 'none';
        }

        function toggleFamilyDetails(show) {
            document.getElementById('family-details').style.display = show ? 'block' : 'none';
        }

        // إظهار/إخفاء أقسام خاصة بالإناث
        function toggleGenderSpecificSections() {
            var gender = document.getElementById('patient-gender').value;
            var pregnancySection = document.getElementById('pregnancy-section');
            var breastfeedingSection = document.getElementById('breastfeeding-section');

            if (gender === 'أنثى') {
                pregnancySection.style.display = 'block';
                breastfeedingSection.style.display = 'block';
            } else {
                pregnancySection.style.display = 'none';
                breastfeedingSection.style.display = 'none';
                // إعادة تعيين القيم
                document.querySelectorAll('input[name="pregnancy"]').forEach(function(input) {
                    input.checked = false;
                });
                document.querySelectorAll('input[name="breastfeeding"]').forEach(function(input) {
                    input.checked = false;
                });
                document.getElementById('pregnancy-description').value = '';
            }
        }

        // جمع بيانات الاستبيان الطبي
        function collectMedicalQuestionnaire() {
            var questionnaire = {};

            // الحساسية
            var allergiesRadio = document.querySelector('input[name="allergies"]:checked');
            questionnaire.allergies = {
                hasAllergies: allergiesRadio ? allergiesRadio.value === 'yes' : false,
                details: document.getElementById('allergy-description').value || ''
            };

            // الأدوية الحالية
            var medicationsRadio = document.querySelector('input[name="current-medications"]:checked');
            questionnaire.currentMedications = {
                takingMedications: medicationsRadio ? medicationsRadio.value === 'yes' : false,
                details: document.getElementById('medication-description').value || ''
            };

            // العمليات الجراحية
            var surgeriesRadio = document.querySelector('input[name="surgeries"]:checked');
            questionnaire.surgeries = {
                hadSurgeries: surgeriesRadio ? surgeriesRadio.value === 'yes' : false,
                details: document.getElementById('surgery-description').value || ''
            };

            // الأمراض المزمنة
            var chronicRadio = document.querySelector('input[name="chronic-diseases"]:checked');
            questionnaire.chronicDiseases = {
                hasChronicDiseases: chronicRadio ? chronicRadio.value === 'yes' : false,
                details: document.getElementById('chronic-description').value || ''
            };

            // الحمل (للإناث فقط)
            var pregnancyRadio = document.querySelector('input[name="pregnancy"]:checked');
            questionnaire.pregnancy = {
                isPregnant: pregnancyRadio ? pregnancyRadio.value === 'yes' : false,
                details: document.getElementById('pregnancy-description').value || ''
            };

            // الرضاعة (للإناث فقط)
            var breastfeedingRadio = document.querySelector('input[name="breastfeeding"]:checked');
            questionnaire.breastfeeding = {
                isBreastfeeding: breastfeedingRadio ? breastfeedingRadio.value === 'yes' : false
            };

            // المشروبات الكحولية
            var alcoholRadio = document.querySelector('input[name="alcohol"]:checked');
            questionnaire.alcohol = {
                consumesAlcohol: alcoholRadio ? alcoholRadio.value === 'yes' : false,
                details: document.getElementById('alcohol-description').value || ''
            };

            // التدخين
            var smokingRadio = document.querySelector('input[name="smoking"]:checked');
            questionnaire.smoking = {
                smokes: smokingRadio ? smokingRadio.value === 'yes' : false,
                details: document.getElementById('smoking-description').value || ''
            };

            // الخطط الغذائية السابقة
            var dietsRadio = document.querySelector('input[name="previous-diets"]:checked');
            questionnaire.previousDiets = {
                hadPreviousDiets: dietsRadio ? dietsRadio.value === 'yes' : false,
                details: document.getElementById('diet-description').value || ''
            };

            // النشاط البدني
            var exerciseRadio = document.querySelector('input[name="exercise"]:checked');
            questionnaire.exercise = {
                exercisesRegularly: exerciseRadio ? exerciseRadio.value === 'yes' : false,
                details: document.getElementById('exercise-description').value || ''
            };

            // اضطرابات النوم
            var sleepRadio = document.querySelector('input[name="sleep-disorders"]:checked');
            questionnaire.sleepDisorders = {
                hasSleepDisorders: sleepRadio ? sleepRadio.value === 'yes' : false,
                details: document.getElementById('sleep-description').value || ''
            };

            // التاريخ العائلي للأمراض
            var familyRadio = document.querySelector('input[name="family-history"]:checked');
            questionnaire.familyHistory = {
                hasFamilyHistory: familyRadio ? familyRadio.value === 'yes' : false,
                details: document.getElementById('family-description').value || ''
            };

            return questionnaire;
        }

        // إعادة تعيين الاستبيان الطبي
        function resetMedicalQuestionnaire() {
            // إعادة تعيين جميع الراديو buttons
            document.querySelectorAll('input[type="radio"]').forEach(function(input) {
                input.checked = false;
            });

            // إعادة تعيين جميع النصوص
            document.querySelectorAll('textarea, input[type="text"]').forEach(function(input) {
                if (input.id.includes('allergy-') || input.id.includes('medication-') ||
                    input.id.includes('surgery-') || input.id.includes('chronic-') ||
                    input.id.includes('pregnancy-') || input.id.includes('alcohol-') ||
                    input.id.includes('smoking-') || input.id.includes('diet-') ||
                    input.id.includes('exercise-') || input.id.includes('sleep-') ||
                    input.id.includes('family-')) {
                    input.value = '';
                }
            });

            // إخفاء جميع التفاصيل
            document.querySelectorAll('[id$="-details"]').forEach(function(element) {
                element.style.display = 'none';
            });

            // إخفاء أقسام الإناث
            document.getElementById('pregnancy-section').style.display = 'none';
            document.getElementById('breastfeeding-section').style.display = 'none';
        }

        function addNewPatient(event) {
            event.preventDefault();

            // جمع بيانات الاستبيان الطبي
            var medicalQuestionnaire = collectMedicalQuestionnaire();

            var patientData = {
                id: Date.now().toString(),
                fullName: document.getElementById('patient-full-name').value,
                age: document.getElementById('patient-age').value,
                gender: document.getElementById('patient-gender').value,
                phone: document.getElementById('patient-phone').value,
                occupation: document.getElementById('patient-occupation').value,
                education: document.getElementById('patient-education').value,
                address: document.getElementById('patient-address').value,
                dateAdded: formatDateLTR(new Date()),
                medicalQuestionnaire: medicalQuestionnaire
            };

            // حفظ المريض في localStorage
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            patients.push(patientData);
            localStorage.setItem('patients', JSON.stringify(patients));

            alert('✅ تم إضافة المريض بنجاح!');

            // إعادة تعيين النموذج والاستبيان الطبي
            event.target.reset();
            resetMedicalQuestionnaire();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function loadPatientsList() {
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            var container = document.getElementById('patients-table-container');

            if (patients.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد مرضى مسجلين حتى الآن</p>';
                return;
            }

            var html = '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>الاسم</th>' +
                '<th>العمر</th>' +
                '<th>الجنس</th>' +
                '<th>الهاتف</th>' +
                '<th>الوظيفة</th>' +
                '<th>المؤهل</th>' +
                '<th>تاريخ التسجيل</th>' +
                '<th>الإجراءات</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < patients.length; i++) {
                var patient = patients[i];
                html += '<tr>' +
                    '<td>' + patient.fullName + '</td>' +
                    '<td>' + patient.age + '</td>' +
                    '<td>' + patient.gender + '</td>' +
                    '<td>' + patient.phone + '</td>' +
                    '<td>' + (patient.occupation || 'غير محدد') + '</td>' +
                    '<td>' + (patient.education || 'غير محدد') + '</td>' +
                    '<td>' + patient.dateAdded + '</td>' +
                    '<td>' +
                        '<button onclick="viewMedicalQuestionnaire(\'' + patient.id + '\')" class="btn" style="background: #17a2b8; padding: 5px 10px; font-size: 12px; margin: 2px;">🏥 الاستبيان الطبي</button>' +
                        '<button onclick="editPatient(\'' + patient.id + '\')" class="btn" style="background: #f39c12; padding: 5px 10px; font-size: 12px; margin: 2px;">✏️ تعديل</button>' +
                        '<button onclick="deletePatient(\'' + patient.id + '\')" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px; margin: 2px;">🗑️ حذف</button>' +
                    '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function searchPatients() {
            var searchTerm = document.getElementById('search-input').value.toLowerCase();
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            var results = [];

            for (var i = 0; i < patients.length; i++) {
                var patient = patients[i];
                if (patient.fullName.toLowerCase().includes(searchTerm) ||
                    patient.phone.includes(searchTerm)) {
                    results.push(patient);
                }
            }

            var container = document.getElementById('search-results');

            if (searchTerm === '') {
                container.innerHTML = '';
                return;
            }

            if (results.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لم يتم العثور على نتائج</p>';
                return;
            }

            var html = '<h4 style="color: #2c3e50; margin: 20px 0;">نتائج البحث (' + results.length + ')</h4>';
            html += '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>الاسم</th>' +
                '<th>العمر</th>' +
                '<th>الجنس</th>' +
                '<th>الهاتف</th>' +
                '<th>الوظيفة</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < results.length; i++) {
                var patient = results[i];
                html += '<tr>' +
                    '<td>' + patient.fullName + '</td>' +
                    '<td>' + patient.age + '</td>' +
                    '<td>' + patient.gender + '</td>' +
                    '<td>' + patient.phone + '</td>' +
                    '<td>' + (patient.occupation || 'غير محدد') + '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function viewMedicalQuestionnaire(patientId) {
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            var patient = patients.find(function(p) { return p.id === patientId; });

            if (!patient) {
                alert('⚠️ لم يتم العثور على المريض');
                return;
            }

            var questionnaire = patient.medicalQuestionnaire || {};

            var modal = document.createElement('div');
            modal.id = 'medicalQuestionnaireModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Cairo', 'Tajawal', sans-serif;
                overflow-y: auto;
                padding: 20px;
            `;

            var content = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 900px; width: 100%; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; border-bottom: 2px solid #e9ecef; padding-bottom: 15px;">
                        <h3 style="color: #2c3e50; margin: 0;">🏥 الاستبيان الطبي - ${patient.fullName}</h3>
                        <button onclick="closeMedicalQuestionnaireModal()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 18px;">×</button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            `;

            // الحساسية
            content += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.allergies?.hasAllergies ? '#e74c3c' : '#27ae60'};">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🤧 الحساسية</h4>
                    <p style="margin: 0 0 10px 0;"><strong>الحالة:</strong> ${questionnaire.allergies?.hasAllergies ? 'يعاني من حساسية' : 'لا يعاني من حساسية'}</p>
                    ${questionnaire.allergies?.details ? `<p style="margin: 0; color: #666;"><strong>التفاصيل:</strong> ${questionnaire.allergies.details}</p>` : ''}
                </div>
            `;

            // الأدوية الحالية
            content += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.currentMedications?.takingMedications ? '#f39c12' : '#27ae60'};">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">💊 الأدوية الحالية</h4>
                    <p style="margin: 0 0 10px 0;"><strong>الحالة:</strong> ${questionnaire.currentMedications?.takingMedications ? 'يتناول أدوية' : 'لا يتناول أدوية'}</p>
                    ${questionnaire.currentMedications?.details ? `<p style="margin: 0; color: #666;"><strong>التفاصيل:</strong> ${questionnaire.currentMedications.details}</p>` : ''}
                </div>
            `;

            // العمليات الجراحية
            content += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.surgeries?.hadSurgeries ? '#e74c3c' : '#27ae60'};">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🏥 العمليات الجراحية</h4>
                    <p style="margin: 0 0 10px 0;"><strong>الحالة:</strong> ${questionnaire.surgeries?.hadSurgeries ? 'أجرى عمليات جراحية' : 'لم يجر عمليات جراحية'}</p>
                    ${questionnaire.surgeries?.details ? `<p style="margin: 0; color: #666;"><strong>التفاصيل:</strong> ${questionnaire.surgeries.details}</p>` : ''}
                </div>
            `;

            // الأمراض المزمنة
            content += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.chronicDiseases?.hasChronicDiseases ? '#e74c3c' : '#27ae60'};">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🩺 الأمراض المزمنة</h4>
                    <p style="margin: 0 0 10px 0;"><strong>الحالة:</strong> ${questionnaire.chronicDiseases?.hasChronicDiseases ? 'يعاني من أمراض مزمنة' : 'لا يعاني من أمراض مزمنة'}</p>
                    ${questionnaire.chronicDiseases?.details ? `<p style="margin: 0; color: #666;"><strong>التفاصيل:</strong> ${questionnaire.chronicDiseases.details}</p>` : ''}
                </div>
            `;

            // الحمل والرضاعة (للإناث فقط)
            if (patient.gender === 'أنثى') {
                content += `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.pregnancy?.isPregnant ? '#e91e63' : '#27ae60'};">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🤱 الحمل</h4>
                        <p style="margin: 0 0 10px 0;"><strong>الحالة:</strong> ${questionnaire.pregnancy?.isPregnant ? 'حامل' : 'غير حامل'}</p>
                        ${questionnaire.pregnancy?.details ? `<p style="margin: 0; color: #666;"><strong>التفاصيل:</strong> ${questionnaire.pregnancy.details}</p>` : ''}
                    </div>
                `;

                content += `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid ${questionnaire.breastfeeding?.isBreastfeeding ? '#9c27b0' : '#27ae60'};">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🍼 الرضاعة الطبيعية</h4>
                        <p style="margin: 0;"><strong>الحالة:</strong> ${questionnaire.breastfeeding?.isBreastfeeding ? 'ترضع طبيعياً' : 'لا ترضع طبيعياً'}</p>
                    </div>
                `;
            }

            content += `
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button onclick="printMedicalQuestionnaire('${patientId}')" style="background: #17a2b8; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">🖨️ طباعة الاستبيان</button>
                        <button onclick="closeMedicalQuestionnaireModal()" style="background: #6c757d; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 5px;">❌ إغلاق</button>
                    </div>
                </div>
            `;

            modal.innerHTML = content;
            document.body.appendChild(modal);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeMedicalQuestionnaireModal();
                }
            });
        }

        function closeMedicalQuestionnaireModal() {
            var modal = document.getElementById('medicalQuestionnaireModal');
            if (modal) {
                modal.remove();
            }
        }

        function printMedicalQuestionnaire(patientId) {
            alert('🖨️ ميزة الطباعة قيد التطوير - قريباً');
        }

        // دوال إدارة المواعيد
        function showBookAppointmentForm() {
            hideAllAppointmentSections();
            document.getElementById('book-appointment-form').classList.remove('hidden');

            // تعيين التاريخ الافتراضي لليوم
            var today = new Date().toISOString().split('T')[0];
            document.getElementById('appointment-date').value = today;

            // تحميل قائمة المرضى المسجلين
            loadPatientsForAppointment();

            // إعادة تعيين النموذج
            resetAppointmentForm();
        }

        function loadPatientsForAppointment() {
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            var select = document.getElementById('appointment-patient-select');

            // مسح الخيارات الحالية
            select.innerHTML = '<option value="">-- اختر مريض مسجل --</option>';

            // إضافة المرضى للقائمة
            patients.forEach(function(patient) {
                var option = document.createElement('option');
                option.value = patient.id;
                option.textContent = patient.fullName + ' - ' + patient.age + ' سنة';
                option.setAttribute('data-phone', patient.phone);
                option.setAttribute('data-age', patient.age);
                option.setAttribute('data-name', patient.fullName);
                select.appendChild(option);
            });

            if (patients.length === 0) {
                var option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا توجد مرضى مسجلين - يرجى إضافة مريض جديد';
                option.disabled = true;
                select.appendChild(option);
            }
        }

        function loadPatientData() {
            var select = document.getElementById('appointment-patient-select');
            var selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value) {
                // تحميل بيانات المريض المختار
                var patientPhone = selectedOption.getAttribute('data-phone');
                var patientAge = selectedOption.getAttribute('data-age');

                document.getElementById('appointment-phone').value = patientPhone || '';
                document.getElementById('appointment-age').value = patientAge || '';

                // إخفاء حقول المريض الجديد
                disableNewPatientMode();
            } else {
                // مسح البيانات
                document.getElementById('appointment-phone').value = '';
                document.getElementById('appointment-age').value = '';
            }
        }

        function enableNewPatientMode() {
            // إظهار حقول المريض الجديد
            document.getElementById('new-patient-fields').style.display = 'block';

            // إعادة تعيين اختيار المريض
            document.getElementById('appointment-patient-select').value = '';

            // مسح البيانات المحملة
            document.getElementById('appointment-phone').value = '';
            document.getElementById('appointment-age').value = '';

            // جعل حقل العمر قابل للتعديل
            var ageField = document.getElementById('appointment-age');
            ageField.removeAttribute('readonly');
            ageField.style.backgroundColor = 'white';

            // التركيز على حقل اسم المريض الجديد
            setTimeout(function() {
                document.getElementById('new-patient-name').focus();
            }, 100);
        }

        function disableNewPatientMode() {
            // إخفاء حقول المريض الجديد
            document.getElementById('new-patient-fields').style.display = 'none';

            // مسح حقول المريض الجديد
            document.getElementById('new-patient-name').value = '';
            document.getElementById('new-patient-age').value = '';

            // جعل حقل العمر للقراءة فقط
            var ageField = document.getElementById('appointment-age');
            ageField.setAttribute('readonly', 'readonly');
            ageField.style.backgroundColor = '#f8f9fa';
        }

        function resetAppointmentForm() {
            // إعادة تعيين جميع الحقول
            document.getElementById('appointment-patient-select').value = '';
            document.getElementById('appointment-phone').value = '';
            document.getElementById('appointment-age').value = '';
            document.getElementById('appointment-time').value = '';
            document.getElementById('appointment-type').value = '';
            document.getElementById('appointment-notes').value = '';

            // إخفاء حقول المريض الجديد
            disableNewPatientMode();
        }

        function showTodayAppointments() {
            hideAllAppointmentSections();
            document.getElementById('today-appointments').classList.remove('hidden');
            loadTodayAppointments();
        }

        function showAllAppointments() {
            hideAllAppointmentSections();
            document.getElementById('all-appointments').classList.remove('hidden');
            loadAllAppointments();
        }

        function hideAllAppointmentSections() {
            document.getElementById('book-appointment-form').classList.add('hidden');
            document.getElementById('today-appointments').classList.add('hidden');
            document.getElementById('all-appointments').classList.add('hidden');
        }

        function bookNewAppointment(event) {
            event.preventDefault();

            var patientSelect = document.getElementById('appointment-patient-select');
            var newPatientName = document.getElementById('new-patient-name').value;
            var isNewPatient = document.getElementById('new-patient-fields').style.display !== 'none';

            var patientName, patientId;

            if (isNewPatient && newPatientName) {
                // مريض جديد
                patientName = newPatientName;
                patientId = null; // سيتم إنشاء معرف جديد إذا تم حفظ المريض
            } else if (patientSelect.value) {
                // مريض مسجل
                var selectedOption = patientSelect.options[patientSelect.selectedIndex];
                patientName = selectedOption.getAttribute('data-name');
                patientId = patientSelect.value;
            } else {
                alert('⚠️ يرجى اختيار مريض مسجل أو إدخال بيانات مريض جديد');
                return;
            }

            var appointmentData = {
                id: Date.now().toString(),
                patientName: patientName,
                patientId: patientId,
                isNewPatient: isNewPatient,
                phone: document.getElementById('appointment-phone').value,
                date: document.getElementById('appointment-date').value,
                time: document.getElementById('appointment-time').value,
                type: document.getElementById('appointment-type').value,
                age: document.getElementById('appointment-age').value || (isNewPatient ? document.getElementById('new-patient-age').value : ''),
                notes: document.getElementById('appointment-notes').value,
                sendWhatsApp: document.getElementById('send-whatsapp-confirmation') ? document.getElementById('send-whatsapp-confirmation').checked : false,
                status: 'مجدول',
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            };

            // حفظ المريض الجديد إذا لزم الأمر
            if (isNewPatient && newPatientName) {
                var newPatientData = {
                    id: Date.now().toString() + '_patient',
                    fullName: newPatientName,
                    age: document.getElementById('new-patient-age').value || '',
                    gender: '', // سيتم تحديده لاحقاً
                    phone: appointmentData.phone,
                    occupation: '',
                    education: '',
                    address: '',
                    dateAdded: formatDateLTR(new Date()),
                    medicalQuestionnaire: {} // استبيان طبي فارغ
                };

                var patients = JSON.parse(localStorage.getItem('patients') || '[]');
                patients.push(newPatientData);
                localStorage.setItem('patients', JSON.stringify(patients));

                // تحديث معرف المريض في الموعد
                appointmentData.patientId = newPatientData.id;
            }

            // حفظ الموعد في localStorage
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            appointments.push(appointmentData);
            localStorage.setItem('appointments', JSON.stringify(appointments));

            // إرسال تأكيد واتساب إذا كان مطلوباً
            if (appointmentData.sendWhatsApp) {
                sendWhatsAppConfirmation(appointmentData);
            }

            var successMessage = '✅ تم حجز الموعد بنجاح!';
            if (isNewPatient && newPatientName) {
                successMessage += '\n\n👤 تم أيضاً إضافة المريض الجديد: ' + newPatientName + ' إلى قاعدة البيانات';
            }
            alert(successMessage);

            // إعادة تعيين النموذج
            event.target.reset();
            resetAppointmentForm();

            // إعادة تحميل قائمة المرضى (في حالة إضافة مريض جديد)
            loadPatientsForAppointment();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function sendWhatsAppConfirmation(appointmentData) {
            var message = '🏥 *عيادة الدكتور أحمد محمد علي*\n\n' +
                'مرحباً ' + appointmentData.patientName + '،\n\n' +
                '✅ تم تأكيد موعدك:\n' +
                '📅 التاريخ: ' + appointmentData.date + '\n' +
                '🕐 الوقت: ' + appointmentData.time + '\n' +
                '👨‍⚕️ الطبيب: د. أحمد محمد علي\n\n' +
                '📍 العنوان: شارع الملك فهد، الرياض\n' +
                '📞 للاستفسار: ************\n\n' +
                '⚠️ يرجى الحضور قبل 15 دقيقة من الموعد\n' +
                '💡 في حالة عدم التمكن من الحضور، يرجى الاتصال لإعادة الجدولة\n\n' +
                'شكراً لثقتكم بنا 🙏';

            var cleanPhone = appointmentData.phone.replace(/[^\d+]/g, '');
            var encodedMessage = encodeURIComponent(message);
            var whatsappUrl = 'https://wa.me/' + cleanPhone + '?text=' + encodedMessage;

            window.open(whatsappUrl, '_blank');
            alert('✅ تم فتح واتساب لإرسال تأكيد الموعد');
        }

        function sendTestWhatsApp() {
            var phone = document.getElementById('appointment-phone').value;
            if (!phone) {
                alert('⚠️ يرجى إدخال رقم الهاتف أولاً');
                return;
            }

            var testData = {
                patientName: document.getElementById('appointment-patient-name').value || 'مريض تجريبي',
                phone: phone,
                date: document.getElementById('appointment-date').value || 'غير محدد',
                time: document.getElementById('appointment-time').value || 'غير محدد'
            };

            sendWhatsAppConfirmation(testData);
        }

        // دوال الوصفات الطبية
        function showNewPrescriptionForm() {
            hideAllPrescriptionSections();
            document.getElementById('new-prescription-form').classList.remove('hidden');

            // إعادة تعيين قائمة الأدوية المضافة
            addedMedicines = [];
            updateAddedMedicinesDisplay();
            updatePrescriptionMedicationsField();

            // تحميل الأدوية الأكثر استخداماً
            loadCommonMedicines();
        }

        function showPrescriptionsArchive() {
            hideAllPrescriptionSections();
            document.getElementById('prescriptions-archive').classList.remove('hidden');
            loadPrescriptionsArchive();
        }

        function hideAllPrescriptionSections() {
            document.getElementById('new-prescription-form').classList.add('hidden');
            document.getElementById('prescriptions-archive').classList.add('hidden');
        }

        // دوال إدارة الأدوية الأكثر استخداماً
        var addedMedicines = [];

        function loadCommonMedicines() {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');

            // تحديث الشبكة في أعلى الصفحة
            var grid = document.getElementById('commonMedicinesGrid');
            if (commonMedicines.length === 0) {
                grid.innerHTML = '<p style="color: rgba(255,255,255,0.8); text-align: center; grid-column: 1 / -1; margin: 20px 0;">لا توجد أدوية مضافة بعد</p>';
            } else {
                var html = '';
                commonMedicines.slice(0, 6).forEach(function(medicine) { // عرض أول 6 أدوية فقط
                    html += `
                        <div style="background: rgba(255,255,255,0.15); padding: 15px; border-radius: 8px; text-align: center; backdrop-filter: blur(5px);">
                            <div style="font-weight: bold; margin-bottom: 5px;">${medicine.name}</div>
                            <div style="font-size: 12px; opacity: 0.9;">${medicine.defaultDosage}</div>
                        </div>
                    `;
                });
                if (commonMedicines.length > 6) {
                    html += '<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center; display: flex; align-items: center; justify-content: center; font-size: 14px; opacity: 0.8;">+' + (commonMedicines.length - 6) + ' أدوية أخرى</div>';
                }
                grid.innerHTML = html;
            }

            // تحديث القائمة المنسدلة في النموذج
            var select = document.getElementById('commonMedicineSelect');
            if (select) {
                select.innerHTML = '<option value="">-- اختر دواء من القائمة --</option>';
                commonMedicines.forEach(function(medicine) {
                    var option = document.createElement('option');
                    option.value = medicine.id;
                    option.textContent = medicine.name + ' - ' + medicine.defaultDosage;
                    option.setAttribute('data-name', medicine.name);
                    option.setAttribute('data-dosage', medicine.defaultDosage);
                    select.appendChild(option);
                });
            }
        }

        function showAddCommonMedicineModal() {
            var modal = document.createElement('div');
            modal.id = 'addCommonMedicineModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">💊 إضافة دواء جديد للقائمة</h3>

                    <form id="addCommonMedicineForm" onsubmit="saveCommonMedicine(event)">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">اسم الدواء *</label>
                            <input type="text" id="newMedicineName" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" placeholder="مثال: باراسيتامول">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">الجرعة الافتراضية *</label>
                            <input type="text" id="newMedicineDefaultDosage" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" placeholder="مثال: حبة واحدة كل 8 ساعات">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">التركيز/القوة</label>
                            <input type="text" id="newMedicineStrength" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" placeholder="مثال: 500 مجم">
                        </div>

                        <div style="margin-bottom: 25px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">ملاحظات</label>
                            <textarea id="newMedicineNotes" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" placeholder="ملاحظات إضافية عن الدواء..."></textarea>
                        </div>

                        <div style="text-align: center;">
                            <button type="submit" style="background: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 5px;">✅ حفظ الدواء</button>
                            <button type="button" onclick="closeCommonMedicineModal()" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // التركيز على حقل اسم الدواء
            setTimeout(function() {
                document.getElementById('newMedicineName').focus();
            }, 100);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeCommonMedicineModal();
                }
            });
        }

        function saveCommonMedicine(event) {
            event.preventDefault();

            var medicineData = {
                id: 'med_' + Date.now(),
                name: document.getElementById('newMedicineName').value,
                defaultDosage: document.getElementById('newMedicineDefaultDosage').value,
                strength: document.getElementById('newMedicineStrength').value,
                notes: document.getElementById('newMedicineNotes').value,
                createdAt: new Date().toISOString(),
                usageCount: 0
            };

            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');

            // التحقق من عدم تكرار اسم الدواء
            var existingMedicine = commonMedicines.find(function(med) {
                return med.name.toLowerCase() === medicineData.name.toLowerCase();
            });

            if (existingMedicine) {
                alert('⚠️ هذا الدواء موجود مسبقاً في القائمة!\nيرجى اختيار اسم آخر أو تعديل الدواء الموجود.');
                return;
            }

            commonMedicines.push(medicineData);
            localStorage.setItem('commonMedicines', JSON.stringify(commonMedicines));

            alert('✅ تم إضافة الدواء بنجاح!\n\nاسم الدواء: ' + medicineData.name + '\nالجرعة الافتراضية: ' + medicineData.defaultDosage);

            closeCommonMedicineModal();
            loadCommonMedicines();
        }

        function closeCommonMedicineModal() {
            var modal = document.getElementById('addCommonMedicineModal');
            if (modal) {
                modal.remove();
            }
        }

        function addMedicineFromList() {
            var select = document.getElementById('commonMedicineSelect');
            var customDosage = document.getElementById('customDosage').value;

            if (!select.value) {
                alert('⚠️ يرجى اختيار دواء من القائمة');
                return;
            }

            var selectedOption = select.options[select.selectedIndex];
            var medicineName = selectedOption.getAttribute('data-name');
            var defaultDosage = selectedOption.getAttribute('data-dosage');
            var finalDosage = customDosage || defaultDosage;

            addMedicineToList(medicineName, finalDosage);

            // إعادة تعيين الحقول
            select.value = '';
            document.getElementById('customDosage').value = '';

            // تحديث عداد الاستخدام
            updateMedicineUsageCount(select.value);
        }

        function addManualMedicine() {
            var medicineName = document.getElementById('manualMedicineName').value.trim();
            var medicineDosage = document.getElementById('manualMedicineDosage').value.trim();

            if (!medicineName) {
                alert('⚠️ يرجى إدخال اسم الدواء');
                return;
            }

            if (!medicineDosage) {
                alert('⚠️ يرجى إدخال الجرعة والتعليمات');
                return;
            }

            addMedicineToList(medicineName, medicineDosage);

            // إعادة تعيين الحقول
            document.getElementById('manualMedicineName').value = '';
            document.getElementById('manualMedicineDosage').value = '';
        }

        function addMedicineToList(name, dosage) {
            var medicine = {
                id: 'added_' + Date.now(),
                name: name,
                dosage: dosage
            };

            addedMedicines.push(medicine);
            updateAddedMedicinesDisplay();
            updatePrescriptionMedicationsField();
        }

        function updateAddedMedicinesDisplay() {
            var container = document.getElementById('addedMedicinesList');

            if (addedMedicines.length === 0) {
                container.innerHTML = '<p style="color: #6c757d; text-align: center; margin: 40px 0;">لم يتم إضافة أي أدوية بعد</p>';
                return;
            }

            var html = '';
            addedMedicines.forEach(function(medicine, index) {
                html += `
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold; color: #495057; margin-bottom: 5px;">${medicine.name}</div>
                            <div style="color: #6c757d; font-size: 14px;">${medicine.dosage}</div>
                        </div>
                        <button onclick="removeMedicineFromList(${index})" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 12px;">🗑️ حذف</button>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function removeMedicineFromList(index) {
            addedMedicines.splice(index, 1);
            updateAddedMedicinesDisplay();
            updatePrescriptionMedicationsField();
        }

        function updatePrescriptionMedicationsField() {
            var medicationsText = addedMedicines.map(function(medicine) {
                return medicine.name + ' - ' + medicine.dosage;
            }).join('\n');

            document.getElementById('prescription-medications').value = medicationsText;
        }

        function updateMedicineUsageCount(medicineId) {
            if (!medicineId) return;

            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');
            var medicineIndex = commonMedicines.findIndex(function(med) { return med.id === medicineId; });

            if (medicineIndex !== -1) {
                commonMedicines[medicineIndex].usageCount = (commonMedicines[medicineIndex].usageCount || 0) + 1;
                localStorage.setItem('commonMedicines', JSON.stringify(commonMedicines));
            }
        }

        function manageCommonMedicines() {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');

            var modal = document.createElement('div');
            modal.id = 'manageCommonMedicinesModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            `;

            var content = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 800px; width: 90%; max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; border-bottom: 2px solid #e9ecef; padding-bottom: 15px;">
                        <h3 style="color: #2c3e50; margin: 0;">⚙️ إدارة الأدوية الأكثر استخداماً</h3>
                        <button onclick="closeManageCommonMedicinesModal()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 18px;">×</button>
                    </div>

                    <div style="text-align: center; margin-bottom: 25px;">
                        <button onclick="showAddCommonMedicineModal(); closeManageCommonMedicinesModal();" style="background: #28a745; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">➕ إضافة دواء جديد</button>
                        <button onclick="sortMedicinesByUsage()" style="background: #17a2b8; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">📊 ترتيب حسب الاستخدام</button>
                        <button onclick="exportMedicinesList()" style="background: #ffc107; color: #212529; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">📤 تصدير القائمة</button>
                    </div>

                    <div id="medicinesManagementList">
            `;

            if (commonMedicines.length === 0) {
                content += '<p style="text-align: center; color: #6c757d; padding: 40px;">لا توجد أدوية في القائمة</p>';
            } else {
                commonMedicines.forEach(function(medicine, index) {
                    content += `
                        <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                                <div style="flex: 1;">
                                    <h5 style="color: #495057; margin: 0 0 8px 0;">${medicine.name}</h5>
                                    <p style="color: #6c757d; margin: 0 0 5px 0; font-size: 14px;"><strong>الجرعة الافتراضية:</strong> ${medicine.defaultDosage}</p>
                                    ${medicine.strength ? `<p style="color: #6c757d; margin: 0 0 5px 0; font-size: 14px;"><strong>التركيز:</strong> ${medicine.strength}</p>` : ''}
                                    ${medicine.notes ? `<p style="color: #6c757d; margin: 0 0 5px 0; font-size: 14px;"><strong>ملاحظات:</strong> ${medicine.notes}</p>` : ''}
                                    <p style="color: #28a745; margin: 0; font-size: 12px;"><strong>عدد مرات الاستخدام:</strong> ${medicine.usageCount || 0}</p>
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <button onclick="editCommonMedicine('${medicine.id}')" style="background: #ffc107; color: #212529; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 12px;">✏️ تعديل</button>
                                    <button onclick="deleteCommonMedicine('${medicine.id}')" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 12px;">🗑️ حذف</button>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            content += `
                    </div>
                </div>
            `;

            modal.innerHTML = content;
            document.body.appendChild(modal);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeManageCommonMedicinesModal();
                }
            });
        }

        function closeManageCommonMedicinesModal() {
            var modal = document.getElementById('manageCommonMedicinesModal');
            if (modal) {
                modal.remove();
            }
        }

        function editCommonMedicine(medicineId) {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');
            var medicine = commonMedicines.find(function(med) { return med.id === medicineId; });

            if (!medicine) {
                alert('⚠️ لم يتم العثور على الدواء');
                return;
            }

            var modal = document.createElement('div');
            modal.id = 'editCommonMedicineModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10001;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">✏️ تعديل الدواء</h3>

                    <form id="editCommonMedicineForm" onsubmit="saveEditedCommonMedicine(event, '${medicineId}')">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">اسم الدواء *</label>
                            <input type="text" id="editMedicineName" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" value="${medicine.name}">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">الجرعة الافتراضية *</label>
                            <input type="text" id="editMedicineDefaultDosage" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" value="${medicine.defaultDosage}">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">التركيز/القوة</label>
                            <input type="text" id="editMedicineStrength" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" value="${medicine.strength || ''}">
                        </div>

                        <div style="margin-bottom: 25px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #495057;">ملاحظات</label>
                            <textarea id="editMedicineNotes" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">${medicine.notes || ''}</textarea>
                        </div>

                        <div style="text-align: center;">
                            <button type="submit" style="background: #28a745; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 5px;">✅ حفظ التعديلات</button>
                            <button type="button" onclick="closeEditCommonMedicineModal()" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // التركيز على حقل اسم الدواء
            setTimeout(function() {
                document.getElementById('editMedicineName').focus();
            }, 100);
        }

        function saveEditedCommonMedicine(event, medicineId) {
            event.preventDefault();

            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');
            var medicineIndex = commonMedicines.findIndex(function(med) { return med.id === medicineId; });

            if (medicineIndex !== -1) {
                var newName = document.getElementById('editMedicineName').value;

                // التحقق من عدم تكرار اسم الدواء (إذا تم تغييره)
                if (commonMedicines[medicineIndex].name.toLowerCase() !== newName.toLowerCase()) {
                    var existingMedicine = commonMedicines.find(function(med) {
                        return med.name.toLowerCase() === newName.toLowerCase() && med.id !== medicineId;
                    });

                    if (existingMedicine) {
                        alert('⚠️ هذا الدواء موجود مسبقاً في القائمة!\nيرجى اختيار اسم آخر.');
                        return;
                    }
                }

                // تحديث البيانات
                commonMedicines[medicineIndex].name = newName;
                commonMedicines[medicineIndex].defaultDosage = document.getElementById('editMedicineDefaultDosage').value;
                commonMedicines[medicineIndex].strength = document.getElementById('editMedicineStrength').value;
                commonMedicines[medicineIndex].notes = document.getElementById('editMedicineNotes').value;

                localStorage.setItem('commonMedicines', JSON.stringify(commonMedicines));

                alert('✅ تم تحديث الدواء بنجاح!');
                closeEditCommonMedicineModal();
                manageCommonMedicines(); // إعادة تحميل القائمة
                loadCommonMedicines(); // تحديث العرض الرئيسي
            }
        }

        function closeEditCommonMedicineModal() {
            var modal = document.getElementById('editCommonMedicineModal');
            if (modal) {
                modal.remove();
            }
        }

        function deleteCommonMedicine(medicineId) {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');
            var medicine = commonMedicines.find(function(med) { return med.id === medicineId; });

            if (medicine && confirm('⚠️ هل أنت متأكد من حذف الدواء "' + medicine.name + '"؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                var updatedMedicines = commonMedicines.filter(function(med) { return med.id !== medicineId; });
                localStorage.setItem('commonMedicines', JSON.stringify(updatedMedicines));

                alert('✅ تم حذف الدواء بنجاح');
                manageCommonMedicines(); // إعادة تحميل القائمة
                loadCommonMedicines(); // تحديث العرض الرئيسي
            }
        }

        function sortMedicinesByUsage() {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');
            commonMedicines.sort(function(a, b) {
                return (b.usageCount || 0) - (a.usageCount || 0);
            });
            localStorage.setItem('commonMedicines', JSON.stringify(commonMedicines));

            alert('✅ تم ترتيب الأدوية حسب عدد مرات الاستخدام');
            manageCommonMedicines(); // إعادة تحميل القائمة
            loadCommonMedicines(); // تحديث العرض الرئيسي
        }

        function exportMedicinesList() {
            var commonMedicines = JSON.parse(localStorage.getItem('commonMedicines') || '[]');

            if (commonMedicines.length === 0) {
                alert('⚠️ لا توجد أدوية لتصديرها');
                return;
            }

            var csvContent = "اسم الدواء,الجرعة الافتراضية,التركيز,ملاحظات,عدد مرات الاستخدام,تاريخ الإضافة\n";

            commonMedicines.forEach(function(medicine) {
                csvContent += [
                    medicine.name || '',
                    medicine.defaultDosage || '',
                    medicine.strength || '',
                    (medicine.notes || '').replace(/,/g, ';'),
                    medicine.usageCount || 0,
                    formatDateLTR(new Date(medicine.createdAt))
                ].join(',') + '\n';
            });

            var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            var link = document.createElement('a');
            var url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'common_medicines_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير قائمة الأدوية بنجاح');
        }

        // دوال إرسال الوصفات عبر الواتساب
        function sendPrescriptionWhatsApp() {
            // التحقق من ملء الحقول المطلوبة
            var patientName = document.getElementById('prescription-patient-name').value;
            var diagnosis = document.getElementById('prescription-diagnosis').value;
            var medications = document.getElementById('prescription-medications').value;

            if (!patientName || !diagnosis || !medications) {
                alert('⚠️ يرجى ملء الحقول المطلوبة (اسم المريض، التشخيص، الأدوية) قبل الإرسال');
                return;
            }

            // إنشاء نص الوصفة
            var prescriptionText = createPrescriptionText({
                patientName: patientName,
                patientAge: document.getElementById('prescription-patient-age').value,
                diagnosis: diagnosis,
                medications: medications,
                instructions: document.getElementById('prescription-instructions').value,
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            });

            // عرض نافذة اختيار رقم الهاتف
            showWhatsAppSendModal(prescriptionText, patientName);
        }

        function sendSavedPrescriptionWhatsApp(prescriptionId) {
            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            var prescription = prescriptions.find(function(p) { return p.id === prescriptionId; });

            if (!prescription) {
                alert('⚠️ لم يتم العثور على الوصفة');
                return;
            }

            var prescriptionText = createPrescriptionText(prescription);
            showWhatsAppSendModal(prescriptionText, prescription.patientName);
        }

        function createPrescriptionText(prescription) {
            var text = '🏥 *وصفة طبية*\n\n';
            text += '👤 *اسم المريض:* ' + prescription.patientName + '\n';

            if (prescription.patientAge) {
                text += '🎂 *العمر:* ' + prescription.patientAge + ' سنة\n';
            }

            text += '📅 *التاريخ:* ' + prescription.dateCreated + '\n';
            text += '🕒 *الوقت:* ' + prescription.timeCreated + '\n\n';

            text += '🔍 *التشخيص:*\n' + prescription.diagnosis + '\n\n';

            text += '💊 *الأدوية الموصوفة:*\n';
            var medications = prescription.medications.split('\n');
            medications.forEach(function(med, index) {
                if (med.trim()) {
                    text += (index + 1) + '. ' + med.trim() + '\n';
                }
            });

            if (prescription.instructions) {
                text += '\n📝 *تعليمات خاصة:*\n' + prescription.instructions + '\n';
            }

            text += '\n⚠️ *تنبيه مهم:*\n';
            text += '• يرجى اتباع التعليمات بدقة\n';
            text += '• لا تتوقف عن تناول الدواء دون استشارة الطبيب\n';
            text += '• في حالة ظهور أي أعراض جانبية، يرجى التواصل فوراً\n\n';

            text += '🏥 *عيادة طبية متخصصة*\n';
            text += '📞 للاستفسارات والمواعيد\n';

            return text;
        }

        function showWhatsAppSendModal(prescriptionText, patientName) {
            var modal = document.createElement('div');
            modal.id = 'whatsappSendModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Cairo', 'Tajawal', sans-serif;
                overflow-y: auto;
                padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 600px; width: 100%; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; border-bottom: 2px solid #e9ecef; padding-bottom: 15px;">
                        <h3 style="color: #2c3e50; margin: 0; display: flex; align-items: center; gap: 10px;">
                            <span style="background: #25d366; color: white; padding: 8px; border-radius: 8px;">📱</span>
                            إرسال الوصفة عبر الواتساب
                        </h3>
                        <button onclick="closeWhatsAppModal()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 18px;">×</button>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">👤 معلومات المريض</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <p style="margin: 0; font-weight: bold; color: #495057;">اسم المريض: ${patientName}</p>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">📱 رقم الهاتف</h4>
                        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                            <button onclick="usePatientPhone()" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">📞 استخدام رقم المريض</button>
                            <button onclick="clearPhoneNumber()" style="background: #6c757d; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">🗑️ مسح</button>
                        </div>
                        <div style="position: relative;">
                            <span style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #666; font-weight: bold;">+964</span>
                            <input type="tel" id="whatsappPhoneNumber" placeholder="7xxxxxxxxx" style="width: 100%; padding: 12px 12px 12px 60px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; direction: ltr; text-align: left;" maxlength="10">
                        </div>
                        <small style="color: #666; font-size: 14px; display: block; margin-top: 5px;">أدخل رقم الهاتف بدون رمز الدولة (مثال: 7901234567)</small>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">📄 معاينة الوصفة</h4>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6;">
                            <pre style="white-space: pre-wrap; font-family: 'Cairo', 'Tajawal', sans-serif; margin: 0; line-height: 1.6; font-size: 14px;">${prescriptionText}</pre>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="sendToWhatsApp()" style="background: #25d366; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 5px; display: inline-flex; align-items: center; gap: 8px;">
                            <span>📱</span> إرسال عبر الواتساب
                        </button>
                        <button onclick="copyPrescriptionText()" style="background: #17a2b8; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 5px; display: inline-flex; align-items: center; gap: 8px;">
                            <span>📋</span> نسخ النص
                        </button>
                        <button onclick="closeWhatsAppModal()" style="background: #6c757d; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // التركيز على حقل رقم الهاتف
            setTimeout(function() {
                document.getElementById('whatsappPhoneNumber').focus();
            }, 100);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeWhatsAppModal();
                }
            });

            // حفظ نص الوصفة للاستخدام لاحقاً
            window.currentPrescriptionText = prescriptionText;
        }

        function usePatientPhone() {
            // محاولة العثور على رقم هاتف المريض
            var patientName = document.getElementById('prescription-patient-name').value;
            if (patientName) {
                var patients = JSON.parse(localStorage.getItem('patients') || '[]');
                var patient = patients.find(function(p) {
                    return p.fullName.toLowerCase().includes(patientName.toLowerCase());
                });

                if (patient && patient.phone) {
                    // إزالة رمز الدولة إذا كان موجوداً
                    var phoneNumber = patient.phone.replace(/^\+964|^964|^0/, '');
                    document.getElementById('whatsappPhoneNumber').value = phoneNumber;
                    alert('✅ تم تحميل رقم هاتف المريض: ' + phoneNumber);
                } else {
                    alert('⚠️ لم يتم العثور على رقم هاتف للمريض في قاعدة البيانات');
                }
            } else {
                alert('⚠️ يرجى إدخال اسم المريض أولاً');
            }
        }

        function clearPhoneNumber() {
            document.getElementById('whatsappPhoneNumber').value = '';
        }

        function sendToWhatsApp() {
            var phoneNumber = document.getElementById('whatsappPhoneNumber').value.trim();

            if (!phoneNumber) {
                alert('⚠️ يرجى إدخال رقم الهاتف');
                return;
            }

            // التحقق من صحة رقم الهاتف العراقي
            if (!/^7[0-9]{9}$/.test(phoneNumber)) {
                alert('⚠️ رقم الهاتف غير صحيح\nيجب أن يبدأ بـ 7 ويتكون من 10 أرقام\nمثال: 7901234567');
                return;
            }

            // إنشاء رابط الواتساب
            var fullPhoneNumber = '964' + phoneNumber;
            var encodedText = encodeURIComponent(window.currentPrescriptionText);
            var whatsappUrl = 'https://wa.me/' + fullPhoneNumber + '?text=' + encodedText;

            // فتح الواتساب
            window.open(whatsappUrl, '_blank');

            alert('✅ تم فتح الواتساب!\nإذا لم يفتح تلقائياً، يرجى نسخ النص والإرسال يدوياً.');

            closeWhatsAppModal();
        }

        function copyPrescriptionText() {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(window.currentPrescriptionText).then(function() {
                    alert('✅ تم نسخ نص الوصفة!\nيمكنك الآن لصقه في أي تطبيق مراسلة.');
                }).catch(function() {
                    fallbackCopyText();
                });
            } else {
                fallbackCopyText();
            }
        }

        function fallbackCopyText() {
            var textArea = document.createElement('textarea');
            textArea.value = window.currentPrescriptionText;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                alert('✅ تم نسخ نص الوصفة!');
            } catch (err) {
                alert('⚠️ لم يتمكن من نسخ النص تلقائياً\nيرجى نسخه يدوياً من المعاينة أعلاه');
            }
            document.body.removeChild(textArea);
        }

        function closeWhatsAppModal() {
            var modal = document.getElementById('whatsappSendModal');
            if (modal) {
                modal.remove();
            }
            window.currentPrescriptionText = null;
        }

        function savePrescription(event) {
            event.preventDefault();

            var prescriptionData = {
                id: Date.now().toString(),
                patientName: document.getElementById('prescription-patient-name').value,
                patientAge: document.getElementById('prescription-patient-age').value,
                diagnosis: document.getElementById('prescription-diagnosis').value,
                medications: document.getElementById('prescription-medications').value,
                instructions: document.getElementById('prescription-instructions').value,
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            };

            // حفظ الوصفة في localStorage
            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            prescriptions.push(prescriptionData);
            localStorage.setItem('prescriptions', JSON.stringify(prescriptions));

            alert('✅ تم حفظ الوصفة الطبية بنجاح!');

            // إعادة تعيين النموذج
            event.target.reset();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function saveAndPrintPrescription() {
            var patientName = document.getElementById('prescription-patient-name').value;
            var patientAge = document.getElementById('prescription-patient-age').value;
            var diagnosis = document.getElementById('prescription-diagnosis').value;
            var medications = document.getElementById('prescription-medications').value;
            var instructions = document.getElementById('prescription-instructions').value;

            if (!patientName || !diagnosis || !medications) {
                alert('⚠️ يرجى ملء الحقول المطلوبة (اسم المريض، التشخيص، الأدوية)');
                return;
            }

            // حفظ الوصفة أولاً
            var prescriptionData = {
                id: Date.now().toString(),
                patientName: patientName,
                patientAge: patientAge,
                diagnosis: diagnosis,
                medications: medications,
                instructions: instructions,
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            };

            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            prescriptions.push(prescriptionData);
            localStorage.setItem('prescriptions', JSON.stringify(prescriptions));

            // إنشاء نافذة الطباعة
            var printWindow = window.open('', '_blank');
            var printContent = createPrescriptionPrintContent(prescriptionData);

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();

            alert('✅ تم حفظ الوصفة وفتح نافذة الطباعة!');

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function createPrescriptionPrintContent(prescription) {
            var settings = getClinicSettings();

            return '<!DOCTYPE html>' +
                '<html dir="rtl" lang="ar">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>وصفة طبية - ' + prescription.patientName + '</title>' +
                '<style>' +
                'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
                '.header { text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }' +
                '.clinic-name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }' +
                '.doctor-name { font-size: 18px; color: #3498db; margin-bottom: 5px; }' +
                '.contact-info { font-size: 14px; color: #666; }' +
                '.patient-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }' +
                '.prescription-content { margin: 20px 0; }' +
                '.section { margin-bottom: 20px; }' +
                '.section-title { font-weight: bold; color: #2c3e50; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }' +
                '.footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }' +
                '@media print { .no-print { display: none; } }' +
                '</style>' +
                '</head>' +
                '<body>' +
                '<div class="no-print" style="text-align: center; margin-bottom: 20px;">' +
                '<button onclick="window.print()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🖨️ طباعة</button>' +
                '<button onclick="window.close()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">❌ إغلاق</button>' +
                '</div>' +
                '<div class="header">' +
                '<div class="clinic-name">' + settings.clinicName + '</div>' +
                '<div class="doctor-name">' + settings.doctorName + '</div>' +
                '<div class="contact-info">' + settings.specialty + '</div>' +
                '<div class="contact-info">📍 ' + settings.address + ' | 📞 ' + settings.phone + '</div>' +
                '<div class="contact-info">رقم الترخيص: ' + settings.licenseNumber + '</div>' +
                '</div>' +
                '<div class="patient-info">' +
                '<strong>اسم المريض:</strong> ' + prescription.patientName +
                (prescription.patientAge ? ' | <strong>العمر:</strong> ' + prescription.patientAge + ' سنة' : '') +
                ' | <strong>التاريخ:</strong> ' + prescription.dateCreated +
                '</div>' +
                '<div class="prescription-content">' +
                '<div class="section">' +
                '<div class="section-title">📋 التشخيص:</div>' +
                '<div>' + prescription.diagnosis.replace(/\n/g, '<br>') + '</div>' +
                '</div>' +
                '<div class="section">' +
                '<div class="section-title">💊 الأدوية الموصوفة:</div>' +
                '<div>' + prescription.medications.replace(/\n/g, '<br>') + '</div>' +
                '</div>' +
                (prescription.instructions ?
                '<div class="section">' +
                '<div class="section-title">📝 تعليمات خاصة:</div>' +
                '<div>' + prescription.instructions.replace(/\n/g, '<br>') + '</div>' +
                '</div>' : '') +
                '</div>' +
                '<div class="footer">' +
                '<p>تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادات الطبية</p>' +
                '<p>التاريخ: ' + prescription.dateCreated + ' | الوقت: ' + prescription.timeCreated + '</p>' +
                '</div>' +
                '</body>' +
                '</html>';
        }

        function testClinicSettings() {
            var settings = getClinicSettings();
            var message = '🔧 إعدادات العيادة الحالية:\n\n' +
                '👨‍⚕️ اسم الطبيب: ' + settings.doctorName + '\n' +
                '🏥 اسم العيادة: ' + settings.clinicName + '\n' +
                '🎓 التخصص: ' + settings.specialty + '\n' +
                '📞 الهاتف: ' + settings.phone + '\n' +
                '📍 العنوان: ' + settings.address + '\n' +
                '📄 رقم الترخيص: ' + settings.licenseNumber;

            alert(message);
        }

        // دوال الخطط الغذائية
        function showCreateNutritionPlan() {
            hideAllNutritionSections();
            document.getElementById('new-nutrition-plan-form').classList.remove('hidden');
        }

        function showSavedNutritionPlans() {
            hideAllNutritionSections();
            document.getElementById('saved-nutrition-plans').classList.remove('hidden');
            loadSavedNutritionPlans();
        }

        function showNutritionTemplates() {
            hideAllNutritionSections();
            document.getElementById('nutrition-templates').classList.remove('hidden');
        }

        function hideAllNutritionSections() {
            document.getElementById('new-nutrition-plan-form').classList.add('hidden');
            document.getElementById('saved-nutrition-plans').classList.add('hidden');
            document.getElementById('nutrition-templates').classList.add('hidden');
        }

        // قوالب الخطط الغذائية الجاهزة
        var nutritionTemplates = {
            weight_loss: {
                name: "خطة إنقاص الوزن",
                description: "نظام غذائي متوازن لفقدان الوزن الصحي",
                targetCalories: "1200-1500 سعرة حرارية",
                meals: {
                    breakfast: [
                        "شوفان مع الحليب قليل الدسم والتوت",
                        "بيضة مسلوقة مع خبز أسمر",
                        "كوب شاي أخضر بدون سكر"
                    ],
                    snack1: [
                        "تفاحة متوسطة",
                        "10 حبات لوز"
                    ],
                    lunch: [
                        "سلطة خضراء كبيرة مع الخضار",
                        "قطعة دجاج مشوي (100 جرام)",
                        "نصف كوب أرز بني",
                        "كوب ماء مع الليمون"
                    ],
                    snack2: [
                        "زبادي يوناني قليل الدسم",
                        "ملعقة عسل طبيعي"
                    ],
                    dinner: [
                        "سمك مشوي (120 جرام)",
                        "خضار مطبوخة على البخار",
                        "سلطة خيار وطماطم"
                    ]
                },
                tips: [
                    "شرب 8-10 أكواب ماء يومياً",
                    "تجنب المشروبات الغازية والعصائر المحلاة",
                    "ممارسة المشي 30 دقيقة يومياً",
                    "تناول الطعام ببطء ومضغه جيداً"
                ]
            },
            weight_gain: {
                name: "خطة زيادة الوزن",
                description: "نظام غذائي عالي السعرات لزيادة الوزن الصحي",
                targetCalories: "2500-3000 سعرة حرارية",
                meals: {
                    breakfast: [
                        "شوفان مع الحليب كامل الدسم والموز والعسل",
                        "بيضتان مقليتان بزيت الزيتون",
                        "خبز أسمر مع زبدة الفول السوداني",
                        "كوب عصير برتقال طبيعي"
                    ],
                    snack1: [
                        "مزيج من المكسرات والفواكه المجففة",
                        "كوب حليب مع الشوكولاتة"
                    ],
                    lunch: [
                        "أرز أبيض مع اللحم أو الدجاج",
                        "خضار مطبوخة بزيت الزيتون",
                        "سلطة مع الأفوكادو",
                        "كوب عصير طبيعي"
                    ],
                    snack2: [
                        "ساندويش زبدة الفول السوداني والمربى",
                        "كوب حليب كامل الدسم"
                    ],
                    dinner: [
                        "سمك أو لحم مشوي (150 جرام)",
                        "بطاطس مهروسة بالزبدة",
                        "خضار مشكلة",
                        "خبز مع زيت الزيتون"
                    ],
                    snack3: [
                        "كوب حليب مع التمر قبل النوم"
                    ]
                },
                tips: [
                    "تناول وجبات صغيرة ومتكررة (5-6 وجبات)",
                    "إضافة زيت الزيتون والمكسرات للوجبات",
                    "شرب السوائل بين الوجبات وليس معها",
                    "ممارسة تمارين المقاومة لبناء العضلات"
                ]
            },
            muscle_gain: {
                name: "خطة زيادة الكتلة العضلية",
                description: "نظام غذائي عالي البروتين لبناء العضلات",
                targetCalories: "2200-2800 سعرة حرارية",
                meals: {
                    breakfast: [
                        "3 بيضات كاملة + 2 بياض بيض",
                        "شوفان مع الموز والعسل",
                        "كوب حليب بروتين",
                        "حفنة من اللوز"
                    ],
                    snack1: [
                        "مشروب البروتين مع الموز",
                        "ملعقة زبدة الفول السوداني"
                    ],
                    lunch: [
                        "صدر دجاج مشوي (150 جرام)",
                        "أرز بني أو كينوا",
                        "خضار مشكلة",
                        "سلطة مع زيت الزيتون"
                    ],
                    preworkout: [
                        "موزة مع ملعقة عسل",
                        "كوب قهوة (اختياري)"
                    ],
                    postworkout: [
                        "مشروب البروتين مع الحليب",
                        "حفنة من التمر"
                    ],
                    dinner: [
                        "سمك السلمون أو التونا (120 جرام)",
                        "بطاطا حلوة مشوية",
                        "بروكلي مطبوخ على البخار",
                        "سلطة خضراء"
                    ],
                    snack2: [
                        "جبن قريش مع المكسرات",
                        "كوب حليب قبل النوم"
                    ]
                },
                tips: [
                    "تناول 1.6-2.2 جرام بروتين لكل كيلو من وزن الجسم",
                    "شرب الماء بكثرة (3-4 لتر يومياً)",
                    "تناول وجبة بروتين خلال 30 دقيقة بعد التمرين",
                    "النوم 7-9 ساعات يومياً للتعافي"
                ]
            },
            diabetes: {
                name: "خطة مريض السكري",
                description: "نظام غذائي متوازن للتحكم في السكري",
                targetCalories: "1800-2200 سعرة حرارية",
                meals: {
                    breakfast: [
                        "شوفان مع القرفة والجوز",
                        "بيضة مسلوقة",
                        "خبز أسمر (شريحة واحدة)",
                        "شاي أخضر بدون سكر"
                    ],
                    snack1: [
                        "تفاحة صغيرة مع ملعقة زبدة لوز",
                        "أو حفنة من المكسرات النيئة"
                    ],
                    lunch: [
                        "سلطة خضراء كبيرة مع الخضار الورقية",
                        "قطعة سمك أو دجاج مشوي (100 جرام)",
                        "ربع كوب أرز بني أو برغل",
                        "خضار مطبوخة بدون زيت كثير"
                    ],
                    snack2: [
                        "زبادي يوناني قليل الدسم بدون سكر",
                        "ملعقة بذور الشيا أو الكتان"
                    ],
                    dinner: [
                        "شوربة خضار بدون كريمة",
                        "قطعة لحم قليل الدهن (80 جرام)",
                        "خضار مشكلة مطبوخة على البخار",
                        "سلطة خيار وطماطم"
                    ],
                    snack3: [
                        "كوب حليب قليل الدسم (إذا لزم الأمر)"
                    ]
                },
                tips: [
                    "تناول وجبات صغيرة كل 3-4 ساعات",
                    "تجنب السكريات المكررة والحلويات",
                    "اختيار الحبوب الكاملة بدلاً من المكررة",
                    "مراقبة مستوى السكر بانتظام",
                    "شرب الماء بكثرة وتجنب العصائر المحلاة",
                    "ممارسة المشي بعد الوجبات"
                ]
            }
        };

        function useNutritionTemplate(templateType) {
            var template = nutritionTemplates[templateType];
            if (!template) {
                alert('⚠️ لم يتم العثور على القالب المطلوب');
                return;
            }

            // إظهار نموذج إنشاء خطة جديدة
            showCreateNutritionPlan();

            // ملء البيانات من القالب
            document.getElementById('nutrition-patient-name').value = '';
            document.getElementById('nutrition-patient-age').value = '';

            // ملء الوجبات
            fillMealFromTemplate('breakfast', template.meals.breakfast);
            fillMealFromTemplate('snack1', template.meals.snack1);
            fillMealFromTemplate('lunch', template.meals.lunch);
            fillMealFromTemplate('snack2', template.meals.snack2);
            fillMealFromTemplate('dinner', template.meals.dinner);

            // إضافة الوجبات الإضافية للنصائح
            var additionalMeals = '';
            if (template.meals.snack3) {
                additionalMeals += '\n\n🌙 وجبة خفيفة قبل النوم:\n' + template.meals.snack3.join('\n');
            }
            if (template.meals.preworkout) {
                additionalMeals += '\n\n🏃‍♂️ وجبة ما قبل التمرين:\n' + template.meals.preworkout.join('\n');
            }
            if (template.meals.postworkout) {
                additionalMeals += '\n\n💪 وجبة ما بعد التمرين:\n' + template.meals.postworkout.join('\n');
            }

            // إضافة الوجبات الإضافية للنصائح
            if (additionalMeals) {
                var currentNotes = document.getElementById('nutrition-notes').value;
                document.getElementById('nutrition-notes').value = currentNotes + additionalMeals;
            }

            // ملء النصائح
            var notesContent = '📋 ' + template.name + '\n';
            notesContent += '📝 ' + template.description + '\n';
            notesContent += '🔥 السعرات المستهدفة: ' + template.targetCalories + '\n\n';
            notesContent += '💡 نصائح مهمة:\n';

            var tipsText = template.tips.map(function(tip, index) {
                return (index + 1) + '. ' + tip;
            }).join('\n');

            document.getElementById('nutrition-notes').value = notesContent + tipsText;

            alert('✅ تم تحميل القالب بنجاح!\n\n' +
                  'القالب: ' + template.name + '\n' +
                  'الوصف: ' + template.description + '\n\n' +
                  'يمكنك الآن تعديل الخطة حسب احتياجات المريض وحفظها.');
        }

        function fillMealFromTemplate(mealType, mealItems) {
            if (!mealItems || mealItems.length === 0) return;

            var mealText = mealItems.join('\n');
            var fieldMapping = {
                'breakfast': 'nutrition-breakfast',
                'snack1': 'nutrition-morning-snack',
                'lunch': 'nutrition-lunch',
                'snack2': 'nutrition-evening-snack',
                'dinner': 'nutrition-dinner'
            };

            var fieldId = fieldMapping[mealType];
            var mealField = document.getElementById(fieldId);

            if (mealField) {
                mealField.value = mealText;
            }
        }

        function saveNutritionPlan(event) {
            event.preventDefault();

            var planData = {
                id: Date.now().toString(),
                patientName: document.getElementById('nutrition-patient-name').value,
                patientAge: document.getElementById('nutrition-patient-age').value,
                breakfast: document.getElementById('nutrition-breakfast').value,
                morningSnack: document.getElementById('nutrition-morning-snack').value,
                lunch: document.getElementById('nutrition-lunch').value,
                eveningSnack: document.getElementById('nutrition-evening-snack').value,
                dinner: document.getElementById('nutrition-dinner').value,
                notes: document.getElementById('nutrition-notes').value,
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            };

            // حفظ الخطة في localStorage
            var nutritionPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            nutritionPlans.push(planData);
            localStorage.setItem('nutritionPlans', JSON.stringify(nutritionPlans));

            alert('✅ تم حفظ الخطة الغذائية بنجاح!');

            // إعادة تعيين النموذج
            event.target.reset();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function saveAndPrintNutritionPlan() {
            var patientName = document.getElementById('nutrition-patient-name').value;
            var patientAge = document.getElementById('nutrition-patient-age').value;
            var breakfast = document.getElementById('nutrition-breakfast').value;
            var lunch = document.getElementById('nutrition-lunch').value;
            var dinner = document.getElementById('nutrition-dinner').value;

            if (!patientName || !patientAge) {
                alert('⚠️ يرجى ملء الحقول المطلوبة (اسم المريض والعمر)');
                return;
            }

            // حفظ الخطة أولاً
            var planData = {
                id: Date.now().toString(),
                patientName: patientName,
                patientAge: patientAge,
                breakfast: breakfast,
                morningSnack: document.getElementById('nutrition-morning-snack').value,
                lunch: lunch,
                eveningSnack: document.getElementById('nutrition-evening-snack').value,
                dinner: dinner,
                notes: document.getElementById('nutrition-notes').value,
                dateCreated: formatDateLTR(new Date()),
                timeCreated: formatTimeLTR(new Date())
            };

            var nutritionPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            nutritionPlans.push(planData);
            localStorage.setItem('nutritionPlans', JSON.stringify(nutritionPlans));

            // إنشاء نافذة الطباعة
            var printWindow = window.open('', '_blank');
            var printContent = createNutritionPrintContent(planData);

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();

            alert('✅ تم حفظ الخطة الغذائية وفتح نافذة الطباعة!');

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function createNutritionPrintContent(plan) {
            var settings = getClinicSettings();

            return '<!DOCTYPE html>' +
                '<html dir="rtl" lang="ar">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>خطة غذائية - ' + plan.patientName + '</title>' +
                '<style>' +
                'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
                '.header { text-align: center; border-bottom: 3px solid #27ae60; padding-bottom: 20px; margin-bottom: 30px; }' +
                '.clinic-name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }' +
                '.doctor-name { font-size: 18px; color: #27ae60; margin-bottom: 5px; }' +
                '.contact-info { font-size: 14px; color: #666; }' +
                '.patient-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }' +
                '.meal-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }' +
                '.meal-title { font-weight: bold; color: #27ae60; margin-bottom: 10px; font-size: 16px; }' +
                '.notes-section { background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px; }' +
                '.footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }' +
                '@media print { .no-print { display: none; } }' +
                '</style>' +
                '</head>' +
                '<body>' +
                '<div class="no-print" style="text-align: center; margin-bottom: 20px;">' +
                '<button onclick="window.print()" style="background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🖨️ طباعة</button>' +
                '<button onclick="window.close()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">❌ إغلاق</button>' +
                '</div>' +
                '<div class="header">' +
                '<div class="clinic-name">' + settings.clinicName + '</div>' +
                '<div class="doctor-name">' + settings.doctorName + '</div>' +
                '<div class="contact-info">' + settings.specialty + '</div>' +
                '<div class="contact-info">📍 ' + settings.address + ' | 📞 ' + settings.phone + '</div>' +
                '</div>' +
                '<div class="patient-info">' +
                '<strong>اسم المريض:</strong> ' + plan.patientName +
                ' | <strong>العمر:</strong> ' + plan.patientAge + ' سنة' +
                ' | <strong>التاريخ:</strong> ' + plan.dateCreated +
                '</div>' +
                '<h2 style="text-align: center; color: #27ae60; margin-bottom: 30px;">🍽️ الخطة الغذائية اليومية</h2>' +
                (plan.breakfast ? '<div class="meal-section"><div class="meal-title">🌅 الإفطار</div><div>' + plan.breakfast.replace(/\n/g, '<br>') + '</div></div>' : '') +
                (plan.morningSnack ? '<div class="meal-section"><div class="meal-title">☕ وجبة خفيفة صباحية</div><div>' + plan.morningSnack.replace(/\n/g, '<br>') + '</div></div>' : '') +
                (plan.lunch ? '<div class="meal-section"><div class="meal-title">🍽️ الغداء</div><div>' + plan.lunch.replace(/\n/g, '<br>') + '</div></div>' : '') +
                (plan.eveningSnack ? '<div class="meal-section"><div class="meal-title">🍎 وجبة خفيفة مسائية</div><div>' + plan.eveningSnack.replace(/\n/g, '<br>') + '</div></div>' : '') +
                (plan.dinner ? '<div class="meal-section"><div class="meal-title">🌙 العشاء</div><div>' + plan.dinner.replace(/\n/g, '<br>') + '</div></div>' : '') +
                (plan.notes ? '<div class="notes-section"><h4>💡 نصائح وتعليمات:</h4><div>' + plan.notes.replace(/\n/g, '<br>') + '</div></div>' : '') +
                '<div class="footer">' +
                '<p>تم إنشاء هذه الخطة بواسطة نظام إدارة العيادات الطبية</p>' +
                '<p>التاريخ: ' + plan.dateCreated + ' | الوقت: ' + plan.timeCreated + '</p>' +
                '</div>' +
                '</body>' +
                '</html>';
        }

        // دوال إدارة المحاسبة
        function showAddIncomeForm() {
            hideAllAccountingSections();
            document.getElementById('add-income-form').classList.remove('hidden');

            // تعيين التاريخ الافتراضي لليوم
            var today = new Date().toISOString().split('T')[0];
            document.getElementById('income-date').value = today;
        }

        function showAddExpenseForm() {
            hideAllAccountingSections();
            document.getElementById('add-expense-form').classList.remove('hidden');

            // تعيين التاريخ الافتراضي لليوم
            var today = new Date().toISOString().split('T')[0];
            document.getElementById('expense-date').value = today;
        }

        function showFinancialReport() {
            hideAllAccountingSections();
            document.getElementById('financial-report').classList.remove('hidden');
            updateFinancialReport();
        }

        function showAccountsArchive() {
            hideAllAccountingSections();
            document.getElementById('accounts-archive').classList.remove('hidden');
        }

        function hideAllAccountingSections() {
            document.getElementById('add-income-form').classList.add('hidden');
            document.getElementById('add-expense-form').classList.add('hidden');
            document.getElementById('financial-report').classList.add('hidden');
            document.getElementById('accounts-archive').classList.add('hidden');
        }

        function addNewIncome(event) {
            event.preventDefault();

            var incomeData = {
                id: Date.now().toString(),
                type: document.getElementById('income-type').value,
                amount: parseInt(document.getElementById('income-amount').value),
                patientName: document.getElementById('income-patient-name').value,
                date: document.getElementById('income-date').value,
                notes: document.getElementById('income-notes').value,
                dateCreated: new Date().toISOString()
            };

            // حفظ الإيراد في localStorage
            var incomes = JSON.parse(localStorage.getItem('incomes') || '[]');
            incomes.push(incomeData);
            localStorage.setItem('incomes', JSON.stringify(incomes));

            alert('✅ تم إضافة الإيراد بنجاح!\n\nالمبلغ: ' + formatNumberLTR(incomeData.amount) + ' د.ع');

            // إعادة تعيين النموذج
            event.target.reset();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function addNewExpense(event) {
            event.preventDefault();

            var expenseData = {
                id: Date.now().toString(),
                type: document.getElementById('expense-type').value,
                amount: parseInt(document.getElementById('expense-amount').value),
                vendor: document.getElementById('expense-vendor').value,
                date: document.getElementById('expense-date').value,
                details: document.getElementById('expense-details').value,
                dateCreated: new Date().toISOString()
            };

            // حفظ المصروف في localStorage
            var expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseData);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            alert('✅ تم إضافة المصروف بنجاح!\n\nالمبلغ: ' + formatNumberLTR(expenseData.amount) + ' د.ع');

            // إعادة تعيين النموذج
            event.target.reset();

            // تحديث الإحصائيات
            updateDashboardStats();
        }

        function updateFinancialReport() {
            var incomes = JSON.parse(localStorage.getItem('incomes') || '[]');
            var expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            var selectedMonth = document.getElementById('month-filter').value;

            // فلترة حسب الشهر إذا تم اختياره
            if (selectedMonth) {
                incomes = incomes.filter(function(income) {
                    return income.date.substring(5, 7) === selectedMonth;
                });
                expenses = expenses.filter(function(expense) {
                    return expense.date.substring(5, 7) === selectedMonth;
                });
            }

            // حساب الإجماليات
            var totalIncome = incomes.reduce(function(sum, income) {
                return sum + income.amount;
            }, 0);

            var totalExpenses = expenses.reduce(function(sum, expense) {
                return sum + expense.amount;
            }, 0);

            var netProfit = totalIncome - totalExpenses;

            // تحديث البطاقات
            document.getElementById('total-income').textContent = formatNumberLTR(totalIncome);
            document.getElementById('total-expenses').textContent = formatNumberLTR(totalExpenses);
            document.getElementById('net-profit').textContent = formatNumberLTR(netProfit);

            // تغيير لون صافي الربح حسب القيمة
            var netProfitElement = document.getElementById('net-profit').parentElement;
            if (netProfit >= 0) {
                netProfitElement.style.background = 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)';
            } else {
                netProfitElement.style.background = 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)';
            }

            // عرض المعاملات الأخيرة
            showRecentTransactions(incomes, expenses);
        }

        function showRecentTransactions(incomes, expenses) {
            var allTransactions = [];

            // إضافة الإيرادات
            for (var i = 0; i < incomes.length; i++) {
                allTransactions.push({
                    type: 'income',
                    data: incomes[i]
                });
            }

            // إضافة المصروفات
            for (var i = 0; i < expenses.length; i++) {
                allTransactions.push({
                    type: 'expense',
                    data: expenses[i]
                });
            }

            // ترتيب حسب التاريخ
            allTransactions.sort(function(a, b) {
                return new Date(b.data.date) - new Date(a.data.date);
            });

            // عرض آخر 10 معاملات
            var recentTransactions = allTransactions.slice(0, 10);
            var container = document.getElementById('recent-transactions');

            if (recentTransactions.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد معاملات مالية</p>';
                return;
            }

            var html = '<h4 style="color: #2c3e50; margin-bottom: 20px;">📋 المعاملات الأخيرة</h4>';
            html += '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>التاريخ</th>' +
                '<th>النوع</th>' +
                '<th>التفاصيل</th>' +
                '<th>المبلغ</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < recentTransactions.length; i++) {
                var transaction = recentTransactions[i];
                var isIncome = transaction.type === 'income';
                var data = transaction.data;

                html += '<tr>' +
                    '<td>' + data.date + '</td>' +
                    '<td>' +
                        '<span style="color: ' + (isIncome ? '#27ae60' : '#e74c3c') + '; font-weight: bold;">' +
                        (isIncome ? '💰 إيراد' : '💸 مصروف') +
                        '</span>' +
                    '</td>' +
                    '<td>' + (isIncome ? data.type : data.type) + '</td>' +
                    '<td style="font-weight: bold; color: ' + (isIncome ? '#27ae60' : '#e74c3c') + ';">' +
                        (isIncome ? '+' : '-') + formatNumberLTR(data.amount) + ' د.ع' +
                    '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function printFinancialReport() {
            var selectedMonth = document.getElementById('month-filter').value;
            var monthName = selectedMonth ? document.querySelector('#month-filter option[value="' + selectedMonth + '"]').textContent : 'جميع الأشهر';

            var totalIncome = document.getElementById('total-income').textContent;
            var totalExpenses = document.getElementById('total-expenses').textContent;
            var netProfit = document.getElementById('net-profit').textContent;

            var printWindow = window.open('', '_blank');
            var settings = getClinicSettings();

            var printContent = '<!DOCTYPE html>' +
                '<html dir="rtl" lang="ar">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>التقرير المالي - ' + monthName + '</title>' +
                '<style>' +
                'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
                '.header { text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }' +
                '.clinic-name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }' +
                '.summary-card { display: inline-block; margin: 10px; padding: 20px; border-radius: 10px; text-align: center; min-width: 200px; }' +
                '.income-card { background: #27ae60; color: white; }' +
                '.expense-card { background: #e74c3c; color: white; }' +
                '.profit-card { background: #3498db; color: white; }' +
                '.amount { font-size: 24px; font-weight: bold; margin: 10px 0; }' +
                '@media print { .no-print { display: none; } }' +
                '</style>' +
                '</head>' +
                '<body>' +
                '<div class="no-print" style="text-align: center; margin-bottom: 20px;">' +
                '<button onclick="window.print()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🖨️ طباعة</button>' +
                '<button onclick="window.close()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">❌ إغلاق</button>' +
                '</div>' +
                '<div class="header">' +
                '<div class="clinic-name">' + settings.clinicName + '</div>' +
                '<div>' + settings.doctorName + '</div>' +
                '<div>📊 التقرير المالي - ' + monthName + '</div>' +
                '<div>التاريخ: ' + formatDateLTR(new Date()) + '</div>' +
                '</div>' +
                '<div style="text-align: center; margin: 40px 0;">' +
                '<div class="summary-card income-card">' +
                '<h3>💰 إجمالي الإيرادات</h3>' +
                '<div class="amount">' + totalIncome + ' د.ع</div>' +
                '</div>' +
                '<div class="summary-card expense-card">' +
                '<h3>💸 إجمالي المصروفات</h3>' +
                '<div class="amount">' + totalExpenses + ' د.ع</div>' +
                '</div>' +
                '<div class="summary-card profit-card">' +
                '<h3>📈 صافي الربح</h3>' +
                '<div class="amount">' + netProfit + ' د.ع</div>' +
                '</div>' +
                '</div>' +
                '<div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">' +
                '<p>تم إنشاء هذا التقرير بواسطة نظام إدارة العيادات الطبية</p>' +
                '</div>' +
                '</body>' +
                '</html>';

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();

            alert('✅ تم فتح نافذة طباعة التقرير المالي!');
        }

        function showIncomeArchive() {
            var incomes = JSON.parse(localStorage.getItem('incomes') || '[]');
            var container = document.getElementById('archive-content');

            if (incomes.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد إيرادات محفوظة</p>';
                return;
            }

            // ترتيب حسب التاريخ
            incomes.sort(function(a, b) {
                return new Date(b.date) - new Date(a.date);
            });

            var html = '<h4 style="color: #27ae60; margin-bottom: 20px;">💰 أرشيف الإيرادات</h4>';
            html += '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>التاريخ</th>' +
                '<th>النوع</th>' +
                '<th>اسم المريض</th>' +
                '<th>المبلغ</th>' +
                '<th>الملاحظات</th>' +
                '<th>الإجراءات</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < incomes.length; i++) {
                var income = incomes[i];
                html += '<tr>' +
                    '<td>' + income.date + '</td>' +
                    '<td>' + income.type + '</td>' +
                    '<td>' + (income.patientName || 'غير محدد') + '</td>' +
                    '<td style="color: #27ae60; font-weight: bold;">' + formatNumberLTR(income.amount) + ' د.ع</td>' +
                    '<td>' + (income.notes || 'لا توجد') + '</td>' +
                    '<td>' +
                        '<button onclick="deleteIncome(\'' + income.id + '\')" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">🗑️ حذف</button>' +
                    '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function showExpenseArchive() {
            var expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            var container = document.getElementById('archive-content');

            if (expenses.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد مصروفات محفوظة</p>';
                return;
            }

            // ترتيب حسب التاريخ
            expenses.sort(function(a, b) {
                return new Date(b.date) - new Date(a.date);
            });

            var html = '<h4 style="color: #e74c3c; margin-bottom: 20px;">💸 أرشيف المصروفات</h4>';
            html += '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>التاريخ</th>' +
                '<th>النوع</th>' +
                '<th>المورد/الجهة</th>' +
                '<th>المبلغ</th>' +
                '<th>التفاصيل</th>' +
                '<th>الإجراءات</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < expenses.length; i++) {
                var expense = expenses[i];
                html += '<tr>' +
                    '<td>' + expense.date + '</td>' +
                    '<td>' + expense.type + '</td>' +
                    '<td>' + (expense.vendor || 'غير محدد') + '</td>' +
                    '<td style="color: #e74c3c; font-weight: bold;">' + formatNumberLTR(expense.amount) + ' د.ع</td>' +
                    '<td>' + (expense.details || 'لا توجد') + '</td>' +
                    '<td>' +
                        '<button onclick="deleteExpense(\'' + expense.id + '\')" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">🗑️ حذف</button>' +
                    '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function deleteIncome(incomeId) {
            if (confirm('هل أنت متأكد من حذف هذا الإيراد؟')) {
                var incomes = JSON.parse(localStorage.getItem('incomes') || '[]');
                incomes = incomes.filter(function(income) {
                    return income.id !== incomeId;
                });
                localStorage.setItem('incomes', JSON.stringify(incomes));

                showIncomeArchive();
                updateDashboardStats();

                alert('✅ تم حذف الإيراد بنجاح!');
            }
        }

        function deleteExpense(expenseId) {
            if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                var expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                expenses = expenses.filter(function(expense) {
                    return expense.id !== expenseId;
                });
                localStorage.setItem('expenses', JSON.stringify(expenses));

                showExpenseArchive();
                updateDashboardStats();

                alert('✅ تم حذف المصروف بنجاح!');
            }
        }





        function updateAdsBanner() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var today = new Date().toISOString().split('T')[0];

            // فلترة الإعلانات النشطة والصالحة
            var activeAds = ads.filter(function(ad) {
                return ad.active && ad.startDate <= today && ad.endDate >= today;
            });

            var banner = document.getElementById('ads-banner');
            var content = document.getElementById('ads-content');

            if (activeAds.length === 0) {
                banner.classList.add('hidden');
                return;
            }

            // عرض إعلان عشوائي من الإعلانات النشطة
            var randomAd = activeAds[Math.floor(Math.random() * activeAds.length)];

            var bannerContent = '';
            if (randomAd.imageUrl) {
                bannerContent += '<img src="' + randomAd.imageUrl + '" class="ads-image" onerror="this.style.display=\'none\'">';
            }

            bannerContent += '<div class="ads-text">' +
                '<div class="ads-title">💊 ' + randomAd.medicineName + '</div>' +
                '<div class="ads-description">';

            // إضافة اسم الشركة
            bannerContent += '<span>🏢 ' + randomAd.company + '</span>';

            // إضافة الجرعة إذا كانت متوفرة في الوصف
            var dosageMatch = randomAd.description.match(/(\d+\s*(مجم|ملجم|جرام|مل|وحدة|كبسولة|قرص))/i);
            if (dosageMatch) {
                bannerContent += '<span>💉 ' + dosageMatch[0] + '</span>';
            }

            // إضافة السعر
            if (randomAd.price) {
                bannerContent += '<span>💰 ' + formatNumberLTR(parseInt(randomAd.price)) + ' د.ع</span>';
            }

            bannerContent += '</div></div>';

            content.innerHTML = bannerContent;
            banner.classList.remove('hidden');

            // إضافة وظيفة النقر لعرض التفاصيل
            banner.onclick = function() {
                showAdDetails(randomAd.id);
            };

            // تحديث عدد المشاهدات
            randomAd.views = (randomAd.views || 0) + 1;
            localStorage.setItem('medicineAds', JSON.stringify(ads));
        }

        function closeAdsBanner(event) {
            if (event) {
                event.stopPropagation();
            }
            document.getElementById('ads-banner').classList.add('hidden');
        }

        function toggleAdsBanner() {
            var banner = document.getElementById('ads-banner');
            if (banner.classList.contains('hidden')) {
                updateAdsBanner();
                alert('✅ تم تفعيل بانر الإعلانات!');
            } else {
                banner.classList.add('hidden');
                alert('⏸️ تم إيقاف بانر الإعلانات!');
            }
        }





        // دوال الإعدادات
        function getClinicSettings() {
            var defaultSettings = {
                doctorName: 'د. أحمد محمد علي',
                clinicName: 'عيادة الدكتور أحمد محمد علي',
                specialty: 'أخصائي الطب الباطني والتغذية العلاجية',
                licenseNumber: '12345',
                phone: '************',
                email: '<EMAIL>',
                address: 'شارع الملك فهد، الرياض',
                logo: '🏥'
            };

            var savedSettings = localStorage.getItem('clinicSettings');
            if (savedSettings) {
                return JSON.parse(savedSettings);
            }
            return defaultSettings;
        }

        function saveClinicSettings(event) {
            event.preventDefault();

            var settings = {
                doctorName: document.getElementById('settings-doctor-name').value,
                clinicName: document.getElementById('settings-clinic-name').value,
                specialty: document.getElementById('settings-specialty').value,
                licenseNumber: document.getElementById('settings-license').value,
                phone: document.getElementById('settings-phone').value,
                email: document.getElementById('settings-email').value,
                address: document.getElementById('settings-address').value,
                logo: document.getElementById('settings-logo').value
            };

            localStorage.setItem('clinicSettings', JSON.stringify(settings));

            // تطبيق الإعدادات على الواجهة
            applyClinicSettings();

            alert('✅ تم حفظ إعدادات العيادة بنجاح!');
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                localStorage.removeItem('clinicSettings');
                loadClinicSettings();
                applyClinicSettings();
                alert('✅ تم إعادة تعيين الإعدادات بنجاح!');
            }
        }

        function loadClinicSettings() {
            var settings = getClinicSettings();

            document.getElementById('settings-doctor-name').value = settings.doctorName;
            document.getElementById('settings-clinic-name').value = settings.clinicName;
            document.getElementById('settings-specialty').value = settings.specialty;
            document.getElementById('settings-license').value = settings.licenseNumber;
            document.getElementById('settings-phone').value = settings.phone;
            document.getElementById('settings-email').value = settings.email;
            document.getElementById('settings-address').value = settings.address;
            document.getElementById('settings-logo').value = settings.logo;
        }

        function applyClinicSettings() {
            var settings = getClinicSettings();

            // تحديث الـ sidebar
            var sidebarLogo = document.querySelector('.sidebar-logo');
            var sidebarClinicName = document.querySelector('.sidebar-clinic-name');
            var sidebarDoctorName = document.querySelector('.sidebar-doctor-name');

            if (sidebarLogo) sidebarLogo.textContent = settings.logo;
            if (sidebarClinicName) sidebarClinicName.textContent = settings.clinicName;
            if (sidebarDoctorName) sidebarDoctorName.textContent = '👨‍⚕️ ' + settings.doctorName;

            // تحديث الـ header
            var headerDoctorName = document.querySelector('.doctor-name');
            var headerLicense = document.querySelector('.license-number');

            if (headerDoctorName) headerDoctorName.textContent = '👨‍⚕️ ' + settings.doctorName;
            if (headerLicense) headerLicense.textContent = 'رقم الترخيص: ' + settings.licenseNumber;
        }

        // دوال عرض الإعلانات الدوائية
        function displayMedicineAds() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var container = document.getElementById('medicine-ads-container');
            var adsCount = document.getElementById('ads-count');

            if (!container) return;

            // فلترة الإعلانات النشطة فقط
            var today = new Date().toISOString().split('T')[0];
            var activeAds = ads.filter(function(ad) {
                return ad.active && ad.startDate <= today && ad.endDate >= today;
            });

            // تحديث عداد الإعلانات
            if (adsCount) {
                adsCount.textContent = activeAds.length;
            }

            if (activeAds.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 40px; background: #f8f9fa; border-radius: 10px; border: 2px dashed #dee2e6;">' +
                    '<div style="font-size: 48px; margin-bottom: 15px;">📢</div>' +
                    '<h4 style="color: #6c757d; margin-bottom: 10px;">لا توجد إعلانات دوائية حالياً</h4>' +
                    '<p style="color: #6c757d; margin: 0;">سيتم عرض الإعلانات الدوائية من الإدارة العامة هنا</p>' +
                    '</div>';
                return;
            }

            // ترتيب الإعلانات حسب الأولوية
            activeAds.sort(function(a, b) {
                var priorityOrder = { 'مميز': 4, 'عاجل': 3, 'مهم': 2, 'عادي': 1 };
                var aPriority = priorityOrder[a.priority] || 1;
                var bPriority = priorityOrder[b.priority] || 1;

                if (aPriority !== bPriority) return bPriority - aPriority;
                return new Date(b.dateCreated) - new Date(a.dateCreated);
            });

            var html = '';

            // عرض أول 3 إعلانات فقط في لوحة التحكم
            var displayAds = activeAds.slice(0, 3);

            for (var i = 0; i < displayAds.length; i++) {
                var ad = displayAds[i];

                // تحديد لون الأولوية
                var priorityColor = '#95a5a6';
                var priorityBg = '#ecf0f1';
                if (ad.priority === 'مميز') {
                    priorityColor = '#e74c3c';
                    priorityBg = '#ffebee';
                } else if (ad.priority === 'عاجل') {
                    priorityColor = '#f39c12';
                    priorityBg = '#fff8e1';
                } else if (ad.priority === 'مهم') {
                    priorityColor = '#3498db';
                    priorityBg = '#e3f2fd';
                }

                // تحديد نوع الإعلان
                var adTypeIcon = ad.isAdminAd ? '🛡️' : '🏥';
                var adTypeText = ad.isAdminAd ? 'إداري' : 'عيادة';
                var adTypeColor = ad.isAdminAd ? '#3498db' : '#95a5a6';

                html += '<div style="background: linear-gradient(135deg, #fff 0%, ' + priorityBg + ' 100%); border: 2px solid ' + priorityColor + '; border-radius: 12px; padding: 15px; margin-bottom: 12px; position: relative; transition: all 0.3s ease; cursor: pointer;" onclick="showAdDetails(\'' + ad.id + '\')" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 6px 20px rgba(0,0,0,0.12)\'" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 3px 10px rgba(0,0,0,0.08)\'">' +

                    // شارة الأولوية
                    '<div style="position: absolute; top: -6px; right: 12px; background: ' + priorityColor + '; color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px; font-weight: bold; box-shadow: 0 2px 6px rgba(0,0,0,0.15);">' +
                    (ad.priority || 'عادي') + '</div>' +

                    // شارة نوع الإعلان
                    '<div style="position: absolute; top: -6px; left: 12px; background: ' + adTypeColor + '; color: white; padding: 3px 6px; border-radius: 12px; font-size: 9px; font-weight: bold; display: flex; align-items: center; gap: 3px;">' +
                    '<span>' + adTypeIcon + '</span><span>' + adTypeText + '</span></div>' +

                    '<div style="display: flex; align-items: center; gap: 15px; margin-top: 8px;">';

                // صورة الدواء
                if (ad.imageUrl) {
                    html += '<div style="flex-shrink: 0;">' +
                        '<img src="' + ad.imageUrl + '" style="width: 60px; height: 60px; border-radius: 8px; object-fit: cover; border: 2px solid ' + priorityColor + ';" onerror="this.style.display=\'none\'">' +
                        '</div>';
                }

                html += '<div style="flex: 1;">' +
                    '<h4 style="color: #2c3e50; margin: 0 0 6px 0; font-size: 16px; font-weight: bold;">' + ad.medicineName + '</h4>' +
                    '<p style="color: #3498db; margin: 0 0 6px 0; font-weight: bold; font-size: 13px;">🏢 ' + ad.company + '</p>' +
                    '<p style="color: #666; margin: 0 0 8px 0; font-size: 13px; line-height: 1.3;">' +
                    (ad.description.length > 80 ? ad.description.substring(0, 80) + '...' : ad.description) + '</p>';

                // معلومات إضافية
                html += '<div style="display: flex; gap: 10px; flex-wrap: wrap; font-size: 11px;">';

                if (ad.category) {
                    html += '<span style="background: #e8f5e8; color: #27ae60; padding: 2px 6px; border-radius: 8px; font-weight: bold;">📂 ' + ad.category + '</span>';
                }

                if (ad.price) {
                    html += '<span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 8px; font-weight: bold;">💰 ' + formatNumberLTR(parseInt(ad.price)) + ' د.ع</span>';
                }

                if (ad.availability) {
                    var availabilityColor = ad.availability === 'متوفر' ? '#d4edda' : '#f8d7da';
                    var availabilityTextColor = ad.availability === 'متوفر' ? '#155724' : '#721c24';
                    html += '<span style="background: ' + availabilityColor + '; color: ' + availabilityTextColor + '; padding: 2px 6px; border-radius: 8px; font-weight: bold;">📦 ' + ad.availability + '</span>';
                }

                html += '</div></div>';

                // زر التفاصيل
                html += '<div style="flex-shrink: 0; text-align: center;">' +
                    '<button onclick="event.stopPropagation(); showAdDetails(\'' + ad.id + '\')" style="background: ' + priorityColor + '; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-weight: bold; font-size: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform=\'scale(1.05)\'" onmouseout="this.style.transform=\'scale(1)\'">👁️ التفاصيل</button>' +
                    '</div>';

                html += '</div></div>';
            }

            // إضافة زر عرض المزيد إذا كان هناك إعلانات أكثر
            if (activeAds.length > 3) {
                html += '<div style="text-align: center; margin-top: 15px;">' +
                    '<button onclick="showAllAds()" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform=\'translateY(-1px)\'" onmouseout="this.style.transform=\'translateY(0)\'">📋 عرض جميع الإعلانات (' + activeAds.length + ')</button>' +
                    '</div>';
            }

            container.innerHTML = html;

            // تحديث عدد المشاهدات للإعلانات المعروضة
            displayAds.forEach(function(ad) {
                ad.views = (ad.views || 0) + 1;
            });
            localStorage.setItem('medicineAds', JSON.stringify(ads));
        }

        function showAdDetails(adId) {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var ad = ads.find(function(a) { return a.id === adId; });

            if (!ad) {
                alert('❌ لم يتم العثور على الإعلان!');
                return;
            }

            // تحديث عدد النقرات
            ad.clicks = (ad.clicks || 0) + 1;
            localStorage.setItem('medicineAds', JSON.stringify(ads));

            // إنشاء نافذة منبثقة لعرض تفاصيل الإعلان
            var modal = document.createElement('div');
            modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;';

            var content = '<div style="background: white; border-radius: 20px; padding: 30px; max-width: 600px; max-height: 80vh; overflow-y: auto; margin: 20px; direction: rtl;">' +
                '<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">' +
                '<h2 style="color: #2c3e50; margin: 0;">📢 تفاصيل الإعلان الدوائي</h2>' +
                '<button onclick="this.closest(\'.modal\').remove()" style="background: #e74c3c; color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer; font-size: 16px;">×</button>' +
                '</div>';

            // معلومات الدواء الأساسية
            content += '<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">' +
                '<h3 style="color: #2c3e50; margin-bottom: 15px;">💊 معلومات الدواء</h3>' +
                '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">' +
                '<div><strong>اسم الدواء:</strong><br>' + ad.medicineName + '</div>' +
                '<div><strong>الشركة المصنعة:</strong><br>' + ad.company + '</div>';

            if (ad.scientificName) {
                content += '<div><strong>الاسم العلمي:</strong><br>' + ad.scientificName + '</div>';
            }
            if (ad.category) {
                content += '<div><strong>الفئة الدوائية:</strong><br>' + ad.category + '</div>';
            }
            if (ad.form) {
                content += '<div><strong>شكل الدواء:</strong><br>' + ad.form + '</div>';
            }
            if (ad.dosage) {
                content += '<div><strong>التركيز/الجرعة:</strong><br>' + ad.dosage + '</div>';
            }

            content += '</div></div>';

            // معلومات السعر والتوفر
            if (ad.price || ad.availability) {
                content += '<div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 20px;">' +
                    '<h3 style="color: #2c3e50; margin-bottom: 15px;">💰 السعر والتوفر</h3>';

                if (ad.price) {
                    content += '<div style="margin-bottom: 10px;"><strong>السعر:</strong> ' + formatNumberLTR(parseInt(ad.price)) + ' د.ع</div>';
                }
                if (ad.availability) {
                    content += '<div style="margin-bottom: 10px;"><strong>حالة التوفر:</strong> ' + ad.availability + '</div>';
                }
                if (ad.prescriptionRequired) {
                    content += '<div><strong>يحتاج وصفة طبية:</strong> ' + ad.prescriptionRequired + '</div>';
                }

                content += '</div>';
            }

            // الوصف والمعلومات الطبية
            if (ad.description) {
                content += '<div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin-bottom: 20px;">' +
                    '<h3 style="color: #2c3e50; margin-bottom: 15px;">📝 وصف الدواء</h3>' +
                    '<p style="line-height: 1.6; margin: 0;">' + ad.description + '</p>' +
                    '</div>';
            }

            // معلومات الإعلان
            content += '<div style="background: #e1f5fe; padding: 20px; border-radius: 10px;">' +
                '<h3 style="color: #2c3e50; margin-bottom: 15px;">📊 معلومات الإعلان</h3>' +
                '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 14px;">' +
                '<div><strong>نوع الإعلان:</strong><br>' + (ad.isAdminAd ? 'إداري' : 'عيادة') + '</div>' +
                '<div><strong>الأولوية:</strong><br>' + (ad.priority || 'عادي') + '</div>' +
                '<div><strong>المشاهدات:</strong><br>' + (ad.views || 0) + '</div>' +
                '<div><strong>النقرات:</strong><br>' + (ad.clicks || 0) + '</div>' +
                '</div></div>';

            content += '</div>';

            modal.innerHTML = content;
            modal.className = 'modal';
            document.body.appendChild(modal);

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function showAllAds() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var today = new Date().toISOString().split('T')[0];
            var activeAds = ads.filter(function(ad) {
                return ad.active && ad.startDate <= today && ad.endDate >= today;
            });

            // إنشاء نافذة منبثقة لعرض جميع الإعلانات
            var modal = document.createElement('div');
            modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;';

            var content = '<div style="background: white; border-radius: 20px; padding: 30px; max-width: 900px; max-height: 80vh; overflow-y: auto; margin: 20px; direction: rtl;">' +
                '<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">' +
                '<h2 style="color: #2c3e50; margin: 0;">📢 جميع الإعلانات الدوائية النشطة (' + activeAds.length + ')</h2>' +
                '<button onclick="this.closest(\'.modal\').remove()" style="background: #e74c3c; color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer; font-size: 16px;">×</button>' +
                '</div>';

            if (activeAds.length === 0) {
                content += '<div style="text-align: center; color: #666; padding: 40px;">لا توجد إعلانات نشطة حالياً</div>';
            } else {
                content += '<div style="display: grid; gap: 20px;">';

                activeAds.forEach(function(ad) {
                    var priorityColor = '#95a5a6';
                    if (ad.priority === 'مميز') priorityColor = '#e74c3c';
                    else if (ad.priority === 'عاجل') priorityColor = '#f39c12';
                    else if (ad.priority === 'مهم') priorityColor = '#3498db';

                    content += '<div style="border: 2px solid ' + priorityColor + '; border-radius: 10px; padding: 20px; background: #f8f9fa;">' +
                        '<div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">' +
                        '<div>' +
                        '<h4 style="color: #2c3e50; margin: 0 0 5px 0;">' + ad.medicineName + '</h4>' +
                        '<p style="color: #3498db; margin: 0 0 10px 0; font-weight: bold;">' + ad.company + '</p>' +
                        '</div>' +
                        '<span style="background: ' + priorityColor + '; color: white; padding: 5px 10px; border-radius: 10px; font-size: 12px; font-weight: bold;">' + (ad.priority || 'عادي') + '</span>' +
                        '</div>' +
                        '<p style="color: #666; margin-bottom: 15px; line-height: 1.4;">' + ad.description + '</p>' +
                        '<div style="display: flex; justify-content: space-between; align-items: center;">' +
                        '<div style="display: flex; gap: 10px; flex-wrap: wrap; font-size: 12px;">';

                    if (ad.category) {
                        content += '<span style="background: #e8f5e8; color: #27ae60; padding: 3px 8px; border-radius: 8px;">' + ad.category + '</span>';
                    }
                    if (ad.price) {
                        content += '<span style="background: #fff3cd; color: #856404; padding: 3px 8px; border-radius: 8px;">' + formatNumberLTR(parseInt(ad.price)) + ' د.ع</span>';
                    }

                    content += '</div>' +
                        '<button onclick="event.stopPropagation(); showAdDetails(\'' + ad.id + '\')" style="background: ' + priorityColor + '; color: white; border: none; padding: 8px 15px; border-radius: 8px; cursor: pointer; font-size: 12px;">التفاصيل</button>' +
                        '</div></div>';
                });

                content += '</div>';
            }

            content += '</div>';

            modal.innerHTML = content;
            modal.className = 'modal';
            document.body.appendChild(modal);

            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        // دوال تحديث الإحصائيات
        function updateDashboardStats() {
            var patients = JSON.parse(localStorage.getItem('patients') || '[]');
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            var nutritionPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            var incomes = JSON.parse(localStorage.getItem('incomes') || '[]');
            var expenses = JSON.parse(localStorage.getItem('expenses') || '[]');

            // تحديث إجمالي المرضى
            document.getElementById('total-patients').textContent = patients.length;

            // تحديث مواعيد اليوم
            var today = new Date().toISOString().split('T')[0];
            var todayAppointments = appointments.filter(function(apt) {
                return apt.date === today;
            });
            document.getElementById('today-appointments').textContent = todayAppointments.length;

            // تحديث الوصفات الطبية
            document.getElementById('total-prescriptions').textContent = prescriptions.length;

            // تحديث الخطط الغذائية
            document.getElementById('total-nutrition-plans').textContent = nutritionPlans.length;

            // حساب الإيرادات الشهرية الفعلية
            var currentMonth = new Date().getMonth() + 1;
            var currentYear = new Date().getFullYear();
            var monthlyIncomes = incomes.filter(function(income) {
                var incomeDate = new Date(income.date);
                return incomeDate.getMonth() + 1 === currentMonth && incomeDate.getFullYear() === currentYear;
            });

            var monthlyRevenue = monthlyIncomes.reduce(function(sum, income) {
                return sum + income.amount;
            }, 0);

            document.getElementById('monthly-revenue').textContent = formatNumberLTR(monthlyRevenue);

            // حساب متوسط الزيارات اليومية
            var dailyAverage = Math.round(appointments.length / 30);
            document.getElementById('daily-average').textContent = dailyAverage;

            // تحديث عرض الإعلانات الدوائية
            displayMedicineAds();
        }

        function loadTodayAppointments() {
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            var today = new Date().toISOString().split('T')[0];
            var todayAppointments = appointments.filter(function(apt) {
                return apt.date === today;
            });

            var container = document.getElementById('today-appointments-container');

            if (todayAppointments.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد مواعيد لليوم</p>';
                return;
            }

            var html = '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>الوقت</th>' +
                '<th>اسم المريض</th>' +
                '<th>نوع الزيارة</th>' +
                '<th>الهاتف</th>' +
                '<th>الملاحظات</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < todayAppointments.length; i++) {
                var apt = todayAppointments[i];
                var patientNameDisplay = apt.patientName;
                if (apt.isNewPatient) {
                    patientNameDisplay += ' <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px;">جديد</span>';
                }

                html += '<tr>' +
                    '<td>' + apt.time + '</td>' +
                    '<td>' + patientNameDisplay + '</td>' +
                    '<td>' + apt.type + '</td>' +
                    '<td>' + apt.phone + '</td>' +
                    '<td>' + (apt.notes || 'لا توجد') + '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function loadAllAppointments() {
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            var container = document.getElementById('all-appointments-container');

            if (appointments.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد مواعيد محفوظة</p>';
                return;
            }

            // ترتيب المواعيد حسب التاريخ والوقت
            appointments.sort(function(a, b) {
                var dateA = new Date(a.date + ' ' + a.time);
                var dateB = new Date(b.date + ' ' + b.time);
                return dateB - dateA;
            });

            var html = '<table class="table">' +
                '<thead>' +
                '<tr>' +
                '<th>التاريخ</th>' +
                '<th>الوقت</th>' +
                '<th>اسم المريض</th>' +
                '<th>نوع الزيارة</th>' +
                '<th>الهاتف</th>' +
                '<th>الإجراءات</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < appointments.length; i++) {
                var apt = appointments[i];
                var patientNameDisplay = apt.patientName;
                if (apt.isNewPatient) {
                    patientNameDisplay += ' <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px;">جديد</span>';
                }

                html += '<tr>' +
                    '<td>' + apt.date + '</td>' +
                    '<td>' + apt.time + '</td>' +
                    '<td>' + patientNameDisplay + '</td>' +
                    '<td>' + apt.type + '</td>' +
                    '<td>' + apt.phone + '</td>' +
                    '<td>' +
                        '<button onclick="deleteAppointment(\'' + apt.id + '\')" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">🗑️ حذف</button>' +
                    '</td>' +
                '</tr>';
            }

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function deleteAppointment(appointmentId) {
            if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
                var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
                appointments = appointments.filter(function(apt) {
                    return apt.id !== appointmentId;
                });
                localStorage.setItem('appointments', JSON.stringify(appointments));

                // إعادة تحميل القائمة
                loadAllAppointments();
                updateDashboardStats();

                alert('✅ تم حذف الموعد بنجاح!');
            }
        }

        function loadPrescriptionsArchive() {
            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            var container = document.getElementById('prescriptions-list');

            if (prescriptions.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد وصفات محفوظة</p>';
                return;
            }

            var html = '';
            for (var i = prescriptions.length - 1; i >= 0; i--) {
                var prescription = prescriptions[i];
                html += '<div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: #f8f9fa;">' +
                    '<h4 style="color: #2c3e50; margin-bottom: 15px;">📝 ' + prescription.patientName +
                    (prescription.patientAge ? ' (' + prescription.patientAge + ' سنة)' : '') + '</h4>' +
                    '<p><strong>التاريخ:</strong> ' + prescription.dateCreated + '</p>' +
                    '<p><strong>التشخيص:</strong> ' + prescription.diagnosis + '</p>' +
                    '<p><strong>الأدوية:</strong> ' + prescription.medications.substring(0, 100) + '...</p>' +
                    '<div style="text-align: center; margin-top: 15px;">' +
                    '<button onclick="printPrescription(\'' + prescription.id + '\')" class="btn" style="background: #17a2b8; margin: 5px;">🖨️ طباعة</button>' +
                    '<button onclick="sendSavedPrescriptionWhatsApp(\'' + prescription.id + '\')" class="btn" style="background: #25d366; margin: 5px;">📱 إرسال بالواتساب</button>' +
                    '<button onclick="deletePrescription(\'' + prescription.id + '\')" class="btn btn-danger" style="margin: 5px;">🗑️ حذف</button>' +
                    '</div>' +
                    '</div>';
            }

            container.innerHTML = html;
        }

        function loadSavedNutritionPlans() {
            var nutritionPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            var container = document.getElementById('saved-plans-container');

            if (nutritionPlans.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">لا توجد خطط غذائية محفوظة</p>';
                return;
            }

            var html = '';
            for (var i = nutritionPlans.length - 1; i >= 0; i--) {
                var plan = nutritionPlans[i];
                html += '<div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: #f8f9fa;">' +
                    '<h4 style="color: #2c3e50; margin-bottom: 15px;">🍽️ ' + plan.patientName + ' (' + plan.patientAge + ' سنة)</h4>' +
                    '<p><strong>التاريخ:</strong> ' + plan.dateCreated + '</p>' +
                    '<div style="text-align: center; margin-top: 15px;">' +
                    '<button onclick="printNutritionPlan(\'' + plan.id + '\')" class="btn" style="background: #17a2b8; margin: 5px;">🖨️ طباعة</button>' +
                    '<button onclick="deleteNutritionPlan(\'' + plan.id + '\')" class="btn btn-danger" style="margin: 5px;">🗑️ حذف</button>' +
                    '</div>' +
                    '</div>';
            }

            container.innerHTML = html;
        }

        // دوال التهيئة
        function loadProgramSettings() {
            var settings = JSON.parse(localStorage.getItem('programSettings') || '{}');

            // تحديث الشريط الجانبي فقط
            if (settings.name) {
                // تحديث الشريط الجانبي
                document.getElementById('sidebar-program-name').textContent = settings.name;
                // تحديث صفحة الإعدادات
                var currentNameElement = document.getElementById('current-program-name');
                if (currentNameElement) {
                    currentNameElement.textContent = settings.name;
                }
            }

            // تحديث اللوجو في الشريط الجانبي فقط
            if (settings.logo) {
                // تحديث الشريط الجانبي
                document.getElementById('sidebar-logo').src = settings.logo;
                document.getElementById('sidebar-logo-container').style.display = 'block';
                document.getElementById('sidebar-default-logo').style.display = 'none';

                // تحديث صفحة الإعدادات
                var currentLogoElement = document.getElementById('current-program-logo');
                if (currentLogoElement) {
                    currentLogoElement.innerHTML = '<img src="' + settings.logo + '" style="max-height: 80px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.2);">';
                }
            }

            if (settings.description) {
                // تحديث صفحة الإعدادات فقط
                var currentDescElement = document.getElementById('current-program-description');
                if (currentDescElement) {
                    currentDescElement.textContent = settings.description;
                    currentDescElement.style.display = 'block';
                }
            }

            if (settings.version) {
                // تحديث الشريط الجانبي
                document.getElementById('sidebar-program-version').textContent = '📱 الإصدار: ' + settings.version;
                document.getElementById('sidebar-program-version').style.display = 'block';

                // تحديث صفحة الإعدادات
                var currentVersionElement = document.getElementById('current-program-version');
                if (currentVersionElement) {
                    currentVersionElement.textContent = 'الإصدار: ' + settings.version;
                    currentVersionElement.style.display = 'block';
                }
            }
        }

        function refreshProgramSettings() {
            loadProgramSettings();
            alert('✅ تم تحديث إعدادات البرنامج بنجاح!\n\nتم تحميل أحدث الإعدادات من لوحة الإدارة العامة.');
        }

        // دوال إدارة المستخدمين
        function loadUsersManagement() {
            loadUsersList();
            updateUsersLimit();
        }

        function loadUsersList() {
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var container = document.getElementById('users-list');

            if (users.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">لا توجد مستخدمين مضافين</p>';
                return;
            }

            var html = '';
            users.forEach(function(user, index) {
                var permissionsText = user.permissions.map(function(perm) {
                    var permissionNames = {
                        'patients': '👥 المرضى',
                        'appointments': '📅 المواعيد',
                        'prescriptions': '📝 الوصفات',
                        'nutrition': '🍎 التغذية',
                        'accounting': '💰 المحاسبة',
                        'reports': '📊 التقارير'
                    };
                    return permissionNames[perm] || perm;
                }).join(', ');

                html += `
                    <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div style="flex: 1;">
                                <h6 style="margin: 0 0 5px 0; color: #2c3e50;">👤 ${user.fullName}</h6>
                                <p style="margin: 0 0 5px 0; color: #6c757d; font-size: 14px;">
                                    <strong>اسم المستخدم:</strong> ${user.username}
                                </p>
                                ${user.jobTitle ? `<p style="margin: 0 0 5px 0; color: #6c757d; font-size: 14px;">
                                    <strong>المسمى الوظيفي:</strong> ${user.jobTitle}
                                </p>` : ''}
                                <p style="margin: 0 0 5px 0; color: #6c757d; font-size: 14px;">
                                    <strong>تاريخ الإضافة:</strong> ${user.dateAdded}
                                </p>
                                <p style="margin: 0; color: #495057; font-size: 13px;">
                                    <strong>الصلاحيات:</strong> ${permissionsText}
                                </p>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button onclick="editUser(${index})" style="background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">✏️ تعديل</button>
                                <button onclick="deleteUser(${index})" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">🗑️ حذف</button>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // تحديث عداد المستخدمين
            document.getElementById('current-users-count').textContent = users.length;
        }

        function updateUsersLimit() {
            // محاولة الحصول على الحد الأقصى من إعدادات العيادة المحفوظة
            var clinicSettings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');
            var maxUsers = clinicSettings.maxUsers || 'غير محدد';

            document.getElementById('max-users-limit').textContent = maxUsers;

            // تحديث حالة زر الإضافة
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var addButton = document.getElementById('add-user-btn');

            if (maxUsers !== 'غير محدد' && users.length >= maxUsers) {
                addButton.disabled = true;
                addButton.style.background = '#6c757d';
                addButton.innerHTML = '🚫 تم الوصول للحد الأقصى';
            } else {
                addButton.disabled = false;
                addButton.style.background = '#28a745';
                addButton.innerHTML = '➕ إضافة مستخدم';
            }
        }

        function refreshUsersLimit() {
            // محاولة تحديث الحد من الإدارة العامة
            var clinics = JSON.parse(localStorage.getItem('clinics') || '[]');
            var registeredClinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            // البحث في كلا المصدرين
            var allClinics = clinics.concat(registeredClinics);
            var currentClinicId = localStorage.getItem('currentClinicId');

            if (currentClinicId) {
                var clinic = allClinics.find(function(c) { return c.id === currentClinicId; });
                if (clinic && clinic.maxUsers) {
                    var clinicSettings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');
                    clinicSettings.maxUsers = clinic.maxUsers;
                    localStorage.setItem('clinicSettings', JSON.stringify(clinicSettings));

                    updateUsersLimit();
                    alert('✅ تم تحديث الحد الأقصى للمستخدمين من الإدارة العامة\n\nالحد الجديد: ' + clinic.maxUsers + ' مستخدم');
                    return;
                }
            }

            // محاولة استخدام حد افتراضي
            var defaultLimit = 5;
            var clinicSettings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');
            if (!clinicSettings.maxUsers) {
                clinicSettings.maxUsers = defaultLimit;
                localStorage.setItem('clinicSettings', JSON.stringify(clinicSettings));
                updateUsersLimit();
                alert('⚠️ لم يتم العثور على إعدادات من الإدارة العامة\n\nتم تطبيق الحد الافتراضي: ' + defaultLimit + ' مستخدمين\n\nيرجى التواصل مع الإدارة العامة لتحديد الحد الصحيح');
            } else {
                updateUsersLimit();
                alert('ℹ️ الحد الحالي: ' + clinicSettings.maxUsers + ' مستخدم\n\nلتغيير الحد، يرجى التواصل مع الإدارة العامة');
            }
        }

        function addNewUser() {
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var clinicSettings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');
            var maxUsers = clinicSettings.maxUsers;

            if (maxUsers && users.length >= maxUsers) {
                alert('⚠️ تم الوصول للحد الأقصى للمستخدمين (' + maxUsers + ')\nلا يمكن إضافة مستخدمين جدد');
                return;
            }

            document.getElementById('add-user-form').style.display = 'block';
            document.getElementById('new-user-form').reset();
            document.getElementById('new-username').focus();
        }

        function cancelAddUser() {
            document.getElementById('add-user-form').style.display = 'none';
            document.getElementById('new-user-form').reset();
        }

        function saveNewUser(event) {
            event.preventDefault();

            var username = document.getElementById('new-username').value;
            var password = document.getElementById('new-password').value;
            var fullName = document.getElementById('new-fullname').value;
            var jobTitle = document.getElementById('new-job-title').value;

            // جمع الصلاحيات المختارة
            var permissions = [];
            var checkboxes = document.querySelectorAll('input[name="permissions"]:checked');
            checkboxes.forEach(function(checkbox) {
                permissions.push(checkbox.value);
            });

            if (permissions.length === 0) {
                alert('⚠️ يرجى اختيار صلاحية واحدة على الأقل');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var existingUser = users.find(function(user) {
                return user.username.toLowerCase() === username.toLowerCase();
            });

            if (existingUser) {
                alert('⚠️ اسم المستخدم موجود بالفعل\nيرجى اختيار اسم مستخدم آخر');
                return;
            }

            // إنشاء المستخدم الجديد
            var newUser = {
                id: Date.now().toString(),
                username: username,
                password: password, // في التطبيق الحقيقي يجب تشفير كلمة المرور
                fullName: fullName,
                jobTitle: jobTitle,
                permissions: permissions,
                dateAdded: formatDateLTR(new Date()),
                isActive: true
            };

            users.push(newUser);
            localStorage.setItem('clinicUsers', JSON.stringify(users));

            alert('✅ تم إضافة المستخدم بنجاح!\n\nاسم المستخدم: ' + username + '\nالصلاحيات: ' + permissions.length + ' صلاحية');

            cancelAddUser();
            loadUsersList();
            updateUsersLimit();
        }

        function editUser(index) {
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var user = users[index];

            if (!user) {
                alert('⚠️ لم يتم العثور على المستخدم');
                return;
            }

            // ملء النموذج ببيانات المستخدم
            document.getElementById('new-username').value = user.username;
            document.getElementById('new-password').value = user.password;
            document.getElementById('new-fullname').value = user.fullName;
            document.getElementById('new-job-title').value = user.jobTitle || '';

            // تحديد الصلاحيات
            var checkboxes = document.querySelectorAll('input[name="permissions"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = user.permissions.includes(checkbox.value);
            });

            // إظهار النموذج مع تغيير العنوان
            document.getElementById('add-user-form').style.display = 'block';
            document.querySelector('#add-user-form h5').textContent = '✏️ تعديل المستخدم';

            // تغيير دالة الحفظ مؤقتاً
            document.getElementById('new-user-form').onsubmit = function(event) {
                updateUser(event, index);
            };
        }

        function updateUser(event, index) {
            event.preventDefault();

            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var username = document.getElementById('new-username').value;
            var password = document.getElementById('new-password').value;
            var fullName = document.getElementById('new-fullname').value;
            var jobTitle = document.getElementById('new-job-title').value;

            // جمع الصلاحيات المختارة
            var permissions = [];
            var checkboxes = document.querySelectorAll('input[name="permissions"]:checked');
            checkboxes.forEach(function(checkbox) {
                permissions.push(checkbox.value);
            });

            if (permissions.length === 0) {
                alert('⚠️ يرجى اختيار صلاحية واحدة على الأقل');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم (باستثناء المستخدم الحالي)
            var existingUser = users.find(function(user, i) {
                return i !== index && user.username.toLowerCase() === username.toLowerCase();
            });

            if (existingUser) {
                alert('⚠️ اسم المستخدم موجود بالفعل\nيرجى اختيار اسم مستخدم آخر');
                return;
            }

            // تحديث بيانات المستخدم
            users[index].username = username;
            users[index].password = password;
            users[index].fullName = fullName;
            users[index].jobTitle = jobTitle;
            users[index].permissions = permissions;

            localStorage.setItem('clinicUsers', JSON.stringify(users));

            alert('✅ تم تحديث بيانات المستخدم بنجاح!');

            // إعادة تعيين النموذج
            cancelAddUser();
            document.querySelector('#add-user-form h5').textContent = '➕ إضافة مستخدم جديد';
            document.getElementById('new-user-form').onsubmit = function(event) {
                saveNewUser(event);
            };

            loadUsersList();
        }

        function deleteUser(index) {
            var users = JSON.parse(localStorage.getItem('clinicUsers') || '[]');
            var user = users[index];

            if (!user) {
                alert('⚠️ لم يتم العثور على المستخدم');
                return;
            }

            if (confirm('⚠️ هل أنت متأكد من حذف المستخدم؟\n\nاسم المستخدم: ' + user.username + '\nالاسم: ' + user.fullName + '\n\nلا يمكن التراجع عن هذا الإجراء!')) {
                users.splice(index, 1);
                localStorage.setItem('clinicUsers', JSON.stringify(users));

                alert('✅ تم حذف المستخدم بنجاح!');
                loadUsersList();
                updateUsersLimit();
            }
        }

        function togglePasswordVisibility(fieldId) {
            var field = document.getElementById(fieldId);
            var button = field.nextElementSibling;

            if (field.type === 'password') {
                field.type = 'text';
                button.textContent = '🙈';
            } else {
                field.type = 'password';
                button.textContent = '👁️';
            }
        }

        function formatDateLTR(date) {
            // تنسيق التاريخ بالإنجليزية من اليسار إلى اليمين (MM/DD/YYYY)
            var dateObj = typeof date === 'string' ? new Date(date) : date;
            return dateObj.toLocaleDateString('en-US');
        }

        function formatTimeLTR(date) {
            // تنسيق الوقت بالإنجليزية من اليسار إلى اليمين (24 ساعة)
            var dateObj = typeof date === 'string' ? new Date(date) : date;
            return dateObj.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatDateTimeLTR(date) {
            // تنسيق التاريخ والوقت معاً بالإنجليزية من اليسار إلى اليمين
            return formatDateLTR(date) + ' - ' + formatTimeLTR(date);
        }

        function formatNumberLTR(number) {
            // تنسيق الأرقام بالإنجليزية من اليسار إلى اليمين
            return number.toLocaleString('en-US');
        }

        function formatCurrencyLTR(amount) {
            // تنسيق العملة بالإنجليزية من اليسار إلى اليمين
            return formatNumberLTR(amount) + ' IQD';
        }

        function updateDateTime() {
            var now = new Date();
            var dateStr = formatDateLTR(now);
            var timeStr = formatTimeLTR(now);

            var dateElement = document.getElementById('current-date');
            var timeElement = document.getElementById('current-time');

            if (dateElement) dateElement.textContent = dateStr;
            if (timeElement) timeElement.textContent = timeStr;
        }

        // تشغيل الدوال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل نظام إدارة العيادات');

            // تحديث التاريخ والوقت
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // تحميل الإعدادات
            loadClinicSettings();
            applyClinicSettings();

            // تحديث الإحصائيات
            updateDashboardStats();

            // تحميل إعدادات البرنامج
            loadProgramSettings();

            // تحديث بانر الإعلانات
            updateAdsBanner();

            // تحديث البانر كل 30 ثانية
            setInterval(updateAdsBanner, 30000);

            console.log('✅ تم تهيئة النظام بنجاح');
        });

        // دوال إدارة الإعلانات الدوائية
        var uploadedImageData = null;

        function showAddMedicineAdForm() {
            document.getElementById('add-medicine-ad-form').classList.remove('hidden');
            document.getElementById('ads-list-container').style.display = 'none';

            // إعادة تعيين النموذج
            document.getElementById('medicine-ad-form').reset();
            hideImagePreview();
            document.getElementById('adImageFile').value = '';
        }

        function cancelAddMedicineAd() {
            document.getElementById('add-medicine-ad-form').classList.add('hidden');
            document.getElementById('ads-list-container').style.display = 'block';

            // إعادة تعيين النموذج
            document.getElementById('medicine-ad-form').reset();
            hideImagePreview();
            uploadedImageData = null;
        }

        function handleImageUpload(input) {
            var file = input.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('⚠️ نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP');
                input.value = '';
                return;
            }

            // التحقق من حجم الملف (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('⚠️ حجم الملف كبير جداً. الحد الأقصى المسموح 5 ميجابايت');
                input.value = '';
                return;
            }

            var reader = new FileReader();
            reader.onload = function(e) {
                var img = new Image();
                img.onload = function() {
                    // ضغط الصورة
                    var canvas = document.createElement('canvas');
                    var ctx = canvas.getContext('2d');

                    // تحديد الأبعاد الجديدة (حد أقصى 800x600)
                    var maxWidth = 800;
                    var maxHeight = 600;
                    var width = img.width;
                    var height = img.height;

                    if (width > height) {
                        if (width > maxWidth) {
                            height *= maxWidth / width;
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width *= maxHeight / height;
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // رسم الصورة المضغوطة
                    ctx.drawImage(img, 0, 0, width, height);

                    // تحويل إلى Base64 مع ضغط الجودة
                    uploadedImageData = canvas.toDataURL('image/jpeg', 0.8);

                    // عرض المعاينة
                    showImagePreview(uploadedImageData, file, width, height);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function showImagePreview(imageSrc, originalFile, newWidth, newHeight) {
            var container = document.getElementById('imagePreviewContainer');
            var preview = document.getElementById('imagePreview');
            var info = document.getElementById('imageInfo');

            preview.src = imageSrc;
            container.style.display = 'block';

            // حساب نسبة الضغط
            var originalSize = originalFile.size;
            var compressedSize = Math.round((imageSrc.length * 3) / 4); // تقدير حجم Base64
            var compressionRatio = Math.round((1 - compressedSize / originalSize) * 100);

            info.innerHTML =
                '<strong>معلومات الصورة:</strong><br>' +
                'الحجم الأصلي: ' + (originalSize / 1024).toFixed(1) + ' KB<br>' +
                'الحجم المضغوط: ' + (compressedSize / 1024).toFixed(1) + ' KB<br>' +
                'الأبعاد: ' + newWidth + '×' + newHeight + ' بكسل<br>' +
                'نسبة الضغط: ' + compressionRatio + '%';
        }

        function hideImagePreview() {
            var container = document.getElementById('imagePreviewContainer');
            container.style.display = 'none';
            uploadedImageData = null;
        }

        function removeImage() {
            var input = document.getElementById('adImageFile');
            input.value = '';
            hideImagePreview();
            resetUploadArea();
        }

        function resetUploadArea() {
            var uploadArea = document.getElementById('uploadArea');
            uploadArea.style.borderColor = '#cbd5e1';
            uploadArea.style.background = '#f8fafc';
        }

        function handleDragOver(event) {
            event.preventDefault();
            var uploadArea = document.getElementById('uploadArea');
            uploadArea.style.borderColor = '#10b981';
            uploadArea.style.background = '#d1fae5';
        }

        function handleDrop(event) {
            event.preventDefault();
            var uploadArea = document.getElementById('uploadArea');
            uploadArea.style.borderColor = '#cbd5e1';
            uploadArea.style.background = '#f8fafc';

            var files = event.dataTransfer.files;
            if (files.length > 0) {
                var input = document.getElementById('adImageFile');
                input.files = files;
                handleImageUpload(input);
            }
        }

        function saveMedicineAd(event) {
            event.preventDefault();

            var adData = {
                id: 'clinic_' + Date.now().toString(),
                medicineName: document.getElementById('adMedicineName').value,
                company: document.getElementById('adCompany').value,
                category: document.getElementById('adCategory').value,
                price: document.getElementById('adPrice').value,
                description: document.getElementById('adDescription').value,
                availability: document.getElementById('adAvailability').value,
                priority: document.getElementById('adPriority').value,
                active: document.getElementById('adActive').value === 'true',
                imageUrl: uploadedImageData || '',
                dateCreated: new Date().toISOString(),
                createdBy: 'clinic',
                views: 0,
                clicks: 0,
                isClinicAd: true
            };

            // حفظ الإعلان في localStorage
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            ads.push(adData);
            localStorage.setItem('medicineAds', JSON.stringify(ads));

            alert('✅ تم إضافة الإعلان الدوائي بنجاح!\n\n' +
                'الدواء: ' + adData.medicineName + '\n' +
                'الشركة: ' + adData.company + '\n' +
                'سيظهر الإعلان في البانر العلوي للنظام');

            // إعادة تعيين النموذج
            cancelAddMedicineAd();

            // تحديث الإحصائيات وقائمة الإعلانات
            updateMedicineAdsStats();
            loadMedicineAdsList();
        }

        function previewMedicineAd() {
            var medicineName = document.getElementById('adMedicineName').value;
            var company = document.getElementById('adCompany').value;
            var description = document.getElementById('adDescription').value;

            if (!medicineName || !company || !description) {
                alert('⚠️ يرجى ملء الحقول المطلوبة للمعاينة (اسم الدواء، الشركة، الوصف)');
                return;
            }

            var price = document.getElementById('adPrice').value;
            var category = document.getElementById('adCategory').value;
            var availability = document.getElementById('adAvailability').value;
            var priority = document.getElementById('adPriority').value;
            var imageUrl = uploadedImageData;

            var previewHtml = '<div style="background: white; padding: 20px; border-radius: 10px; border: 2px solid #e2e8f0; max-width: 500px; margin: 0 auto;">' +
                '<h3 style="color: #1e293b; text-align: center; margin-bottom: 15px;">👁️ معاينة الإعلان</h3>' +
                '<div style="display: flex; align-items: center; gap: 15px;">';

            if (imageUrl) {
                previewHtml += '<img src="' + imageUrl + '" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; border: 2px solid #e2e8f0;">';
            }

            previewHtml += '<div style="flex: 1;">' +
                '<h4 style="color: #1e293b; margin: 0 0 5px 0;">💊 ' + medicineName + '</h4>' +
                '<p style="color: #059669; margin: 0 0 5px 0; font-weight: bold;">🏢 ' + company + '</p>' +
                '<p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">' + description + '</p>' +
                '<div style="display: flex; gap: 10px; flex-wrap: wrap; font-size: 12px;">';

            if (category) {
                previewHtml += '<span style="background: #e8f5e8; color: #059669; padding: 2px 6px; border-radius: 8px;">📂 ' + category + '</span>';
            }
            if (price) {
                previewHtml += '<span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 8px;">💰 ' + formatNumberLTR(parseInt(price)) + ' د.ع</span>';
            }
            if (availability) {
                var availabilityColor = availability === 'متوفر' ? '#d4edda' : '#f8d7da';
                var availabilityTextColor = availability === 'متوفر' ? '#155724' : '#721c24';
                previewHtml += '<span style="background: ' + availabilityColor + '; color: ' + availabilityTextColor + '; padding: 2px 6px; border-radius: 8px;">📦 ' + availability + '</span>';
            }
            if (priority && priority !== 'عادي') {
                previewHtml += '<span style="background: #f8d7da; color: #721c24; padding: 2px 6px; border-radius: 8px;">⚡ ' + priority + '</span>';
            }

            previewHtml += '</div></div></div></div>';

            // عرض المعاينة في نافذة منبثقة
            var previewWindow = window.open('', '_blank', 'width=600,height=400,scrollbars=yes');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>معاينة الإعلان الدوائي</title>
                    <style>
                        body { font-family: 'Cairo', Arial, sans-serif; padding: 20px; background: #f8fafc; }
                    </style>
                </head>
                <body>
                    ${previewHtml}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">إغلاق المعاينة</button>
                    </div>
                </body>
                </html>
            `);
        }

        function showAllMedicineAds() {
            document.getElementById('add-medicine-ad-form').classList.add('hidden');
            document.getElementById('ads-list-container').style.display = 'block';
            loadMedicineAdsList();
        }

        function loadMedicineAdsList() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var container = document.getElementById('medicine-ads-list');

            if (ads.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #64748b; font-size: 16px; padding: 40px;">لا توجد إعلانات دوائية محفوظة</p>';
                return;
            }

            var html = '';
            ads.forEach(function(ad, index) {
                var priorityColor = ad.priority === 'عاجل' ? '#dc2626' : ad.priority === 'مهم' ? '#d97706' : '#059669';
                var statusColor = ad.active ? '#059669' : '#6c757d';
                var statusText = ad.active ? 'نشط' : 'غير نشط';

                html += '<div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 15px; position: relative;">' +
                    '<div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">' +
                    '<div style="flex: 1;">' +
                    '<h4 style="color: #1e293b; margin: 0 0 5px 0;">💊 ' + ad.medicineName + '</h4>' +
                    '<p style="color: #059669; margin: 0 0 5px 0; font-weight: bold;">🏢 ' + ad.company + '</p>' +
                    '<p style="color: #64748b; margin: 0; font-size: 14px;">' + (ad.description.length > 100 ? ad.description.substring(0, 100) + '...' : ad.description) + '</p>' +
                    '</div>' +
                    '<div style="text-align: left;">' +
                    '<span style="background: ' + priorityColor + '; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; margin-left: 5px;">' + (ad.priority || 'عادي') + '</span>' +
                    '<span style="background: ' + statusColor + '; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px;">' + statusText + '</span>' +
                    '</div>' +
                    '</div>' +

                    '<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; padding-top: 10px; border-top: 1px solid #e2e8f0;">' +
                    '<div style="font-size: 12px; color: #64748b;">' +
                    '<span>👁️ ' + (ad.views || 0) + ' مشاهدة</span> | ' +
                    '<span>👆 ' + (ad.clicks || 0) + ' نقرة</span> | ' +
                    '<span>📅 ' + formatDateLTR(new Date(ad.dateCreated)) + '</span>' +
                    '</div>' +
                    '<div>' +
                    '<button onclick="editMedicineAd(\'' + ad.id + '\')" class="btn" style="font-size: 12px; padding: 5px 10px; margin: 2px;">✏️ تعديل</button>' +
                    '<button onclick="toggleAdStatus(\'' + ad.id + '\')" class="btn ' + (ad.active ? 'btn-warning' : 'btn-success') + '" style="font-size: 12px; padding: 5px 10px; margin: 2px;">' + (ad.active ? '⏸️ إيقاف' : '▶️ تفعيل') + '</button>' +
                    '<button onclick="deleteMedicineAd(\'' + ad.id + '\')" class="btn btn-danger" style="font-size: 12px; padding: 5px 10px; margin: 2px;">🗑️ حذف</button>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
            });

            container.innerHTML = html;
        }

        function updateMedicineAdsStats() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var activeAds = ads.filter(function(ad) { return ad.active; });
            var totalViews = ads.reduce(function(sum, ad) { return sum + (ad.views || 0); }, 0);
            var totalClicks = ads.reduce(function(sum, ad) { return sum + (ad.clicks || 0); }, 0);

            document.getElementById('total-ads-count').textContent = ads.length;
            document.getElementById('active-ads-count').textContent = activeAds.length;
            document.getElementById('total-views').textContent = totalViews;
            document.getElementById('total-clicks').textContent = totalClicks;
        }

        function toggleAdStatus(adId) {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
            var adIndex = ads.findIndex(function(ad) { return ad.id === adId; });

            if (adIndex !== -1) {
                ads[adIndex].active = !ads[adIndex].active;
                localStorage.setItem('medicineAds', JSON.stringify(ads));

                alert('✅ تم ' + (ads[adIndex].active ? 'تفعيل' : 'إيقاف') + ' الإعلان بنجاح');
                loadMedicineAdsList();
                updateMedicineAdsStats();
            }
        }

        function deleteMedicineAd(adId) {
            if (confirm('⚠️ هل أنت متأكد من حذف هذا الإعلان؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');
                ads = ads.filter(function(ad) { return ad.id !== adId; });
                localStorage.setItem('medicineAds', JSON.stringify(ads));

                alert('✅ تم حذف الإعلان بنجاح');
                loadMedicineAdsList();
                updateMedicineAdsStats();
            }
        }

        function editMedicineAd(adId) {
            alert('🔧 ميزة التعديل قيد التطوير - قريباً');
        }

        function showAdsAnalytics() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');

            if (ads.length === 0) {
                alert('📊 لا توجد إعلانات لعرض الإحصائيات');
                return;
            }

            var analytics = {
                total: ads.length,
                active: ads.filter(function(ad) { return ad.active; }).length,
                inactive: ads.filter(function(ad) { return !ad.active; }).length,
                totalViews: ads.reduce(function(sum, ad) { return sum + (ad.views || 0); }, 0),
                totalClicks: ads.reduce(function(sum, ad) { return sum + (ad.clicks || 0); }, 0),
                categories: {}
            };

            // تجميع الفئات
            ads.forEach(function(ad) {
                var category = ad.category || 'غير محدد';
                analytics.categories[category] = (analytics.categories[category] || 0) + 1;
            });

            var analyticsHtml = '<h3>📊 إحصائيات الإعلانات الدوائية</h3>' +
                '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">' +
                '<div style="background: #059669; color: white; padding: 15px; border-radius: 8px; text-align: center;">' +
                '<div style="font-size: 24px; font-weight: bold;">' + analytics.total + '</div>' +
                '<div>إجمالي الإعلانات</div>' +
                '</div>' +
                '<div style="background: #0891b2; color: white; padding: 15px; border-radius: 8px; text-align: center;">' +
                '<div style="font-size: 24px; font-weight: bold;">' + analytics.active + '</div>' +
                '<div>الإعلانات النشطة</div>' +
                '</div>' +
                '<div style="background: #7c3aed; color: white; padding: 15px; border-radius: 8px; text-align: center;">' +
                '<div style="font-size: 24px; font-weight: bold;">' + analytics.totalViews + '</div>' +
                '<div>إجمالي المشاهدات</div>' +
                '</div>' +
                '<div style="background: #d97706; color: white; padding: 15px; border-radius: 8px; text-align: center;">' +
                '<div style="font-size: 24px; font-weight: bold;">' + analytics.totalClicks + '</div>' +
                '<div>إجمالي النقرات</div>' +
                '</div>' +
                '</div>' +
                '<h4>📂 توزيع الفئات:</h4>' +
                '<ul>';

            for (var category in analytics.categories) {
                analyticsHtml += '<li>' + category + ': ' + analytics.categories[category] + ' إعلان</li>';
            }

            analyticsHtml += '</ul>';

            // عرض الإحصائيات في نافذة منبثقة
            var analyticsWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            analyticsWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>إحصائيات الإعلانات الدوائية</title>
                    <style>
                        body { font-family: 'Cairo', Arial, sans-serif; padding: 20px; background: #f8fafc; }
                        h3, h4 { color: #1e293b; }
                    </style>
                </head>
                <body>
                    ${analyticsHtml}
                    <div style="text-align: center; margin-top: 30px;">
                        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">إغلاق</button>
                    </div>
                </body>
                </html>
            `);
        }

        function exportAdsData() {
            var ads = JSON.parse(localStorage.getItem('medicineAds') || '[]');

            if (ads.length === 0) {
                alert('📤 لا توجد إعلانات لتصديرها');
                return;
            }

            // إنشاء CSV
            var csvContent = "اسم الدواء,الشركة,الفئة,السعر,الوصف,حالة التوفر,الأولوية,الحالة,المشاهدات,النقرات,تاريخ الإنشاء\n";

            ads.forEach(function(ad) {
                csvContent += [
                    ad.medicineName || '',
                    ad.company || '',
                    ad.category || '',
                    ad.price || '',
                    (ad.description || '').replace(/,/g, ';'),
                    ad.availability || '',
                    ad.priority || '',
                    ad.active ? 'نشط' : 'غير نشط',
                    ad.views || 0,
                    ad.clicks || 0,
                    formatDateLTR(new Date(ad.dateCreated))
                ].join(',') + '\n';
            });

            // تحميل الملف
            var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            var link = document.createElement('a');
            var url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'medicine_ads_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير بيانات الإعلانات بنجاح');
        }

        // تحديث الإحصائيات عند تحميل صفحة الإعلانات
        function initializeMedicineAdsPage() {
            updateMedicineAdsStats();
            loadMedicineAdsList();
        }

        // إضافة استدعاء تحديث الإحصائيات عند عرض الصفحة
        var originalShowPage = showPage;
        showPage = function(pageId) {
            originalShowPage(pageId);
            if (pageId === 'medicine-ads') {
                setTimeout(initializeMedicineAdsPage, 100);
            }
            if (pageId === 'prescriptions') {
                setTimeout(loadCommonMedicines, 100);
            }
        };

        // نظام حماية الإعدادات
        var isSettingsAuthenticated = false;
        var currentSessionTimeout = null;

        function authenticateSettings() {
            if (isSettingsAuthenticated) {
                showPage('settings');
                return;
            }

            showAuthenticationModal();
        }

        function showAuthenticationModal() {
            var modal = document.createElement('div');
            modal.id = 'authModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 40px; max-width: 450px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.3); text-align: center;">
                    <div style="margin-bottom: 30px;">
                        <div style="font-size: 64px; margin-bottom: 15px;">🔐</div>
                        <h2 style="margin: 0 0 10px 0; color: #2c3e50; font-family: 'Cairo', sans-serif;">مصادقة الوصول</h2>
                        <p style="color: #7f8c8d; margin: 0; font-size: 16px;">يتطلب الوصول لإعدادات العيادة مصادقة من لوحة الإدارة العامة</p>
                    </div>

                    <form id="authForm" onsubmit="validateCredentials(event)">
                        <div style="margin-bottom: 20px; text-align: right;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">اسم المستخدم</label>
                            <input type="text" id="authUsername" required style="width: 100%; padding: 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; font-family: 'Cairo', sans-serif;" placeholder="أدخل اسم المستخدم">
                        </div>

                        <div style="margin-bottom: 25px; text-align: right;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">كلمة المرور</label>
                            <div style="position: relative;">
                                <input type="password" id="authPassword" required style="width: 100%; padding: 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; font-family: 'Cairo', sans-serif;" placeholder="أدخل كلمة المرور">
                                <button type="button" onclick="toggleAuthPasswordVisibility()" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 18px;">👁️</button>
                            </div>
                        </div>

                        <div style="margin-bottom: 25px;">
                            <button type="submit" style="width: 100%; background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 15px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; font-family: 'Cairo', sans-serif; transition: all 0.3s ease;">
                                🔓 دخول للإعدادات
                            </button>
                        </div>

                        <div style="text-align: center;">
                            <button type="button" onclick="closeAuthModal()" style="background: #6c757d; color: white; padding: 10px 25px; border: none; border-radius: 5px; cursor: pointer; font-family: 'Cairo', sans-serif;">❌ إلغاء</button>
                        </div>
                    </form>

                    <div style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d;">
                        <p style="margin: 0;">💡 للحصول على أوراق الاعتماد، تواصل مع مدير النظام</p>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // التركيز على حقل اسم المستخدم
            setTimeout(function() {
                document.getElementById('authUsername').focus();
            }, 100);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeAuthModal();
                }
            });
        }

        function toggleAuthPasswordVisibility() {
            var input = document.getElementById('authPassword');
            var button = input.nextElementSibling;

            if (input.type === 'password') {
                input.type = 'text';
                button.textContent = '🙈';
            } else {
                input.type = 'password';
                button.textContent = '👁️';
            }
        }

        function validateCredentials(event) {
            event.preventDefault();

            var username = document.getElementById('authUsername').value;
            var password = document.getElementById('authPassword').value;

            // الحصول على أوراق الاعتماد من localStorage
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');

            // البحث عن المستخدم
            var user = credentials.find(function(cred) {
                return cred.username === username && cred.password === password && cred.isActive;
            });

            if (user) {
                // تحديث آخر استخدام
                user.lastUsed = new Date().toISOString();
                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                // تسجيل الدخول بنجاح
                isSettingsAuthenticated = true;

                // إعداد انتهاء الجلسة (30 دقيقة)
                if (currentSessionTimeout) {
                    clearTimeout(currentSessionTimeout);
                }
                currentSessionTimeout = setTimeout(function() {
                    isSettingsAuthenticated = false;
                    alert('⏰ انتهت صلاحية الجلسة\nيرجى تسجيل الدخول مرة أخرى للوصول للإعدادات');
                }, 30 * 60 * 1000); // 30 دقيقة

                closeAuthModal();

                // عرض رسالة نجاح
                setTimeout(function() {
                    alert('✅ تم تسجيل الدخول بنجاح!\n\n' +
                        'مرحباً ' + user.username + '\n' +
                        'صلاحية الجلسة: 30 دقيقة\n\n' +
                        'يمكنك الآن الوصول لإعدادات العيادة');

                    showPage('settings');
                }, 100);

            } else {
                // فشل تسجيل الدخول
                alert('❌ خطأ في المصادقة!\n\n' +
                    'اسم المستخدم أو كلمة المرور غير صحيحة\n' +
                    'أو أن الحساب معطل\n\n' +
                    'يرجى التحقق من البيانات والمحاولة مرة أخرى');

                // إعادة تعيين الحقول
                document.getElementById('authUsername').value = '';
                document.getElementById('authPassword').value = '';
                document.getElementById('authUsername').focus();
            }
        }

        function closeAuthModal() {
            var modal = document.getElementById('authModal');
            if (modal) {
                modal.remove();
            }
        }

        // إضافة زر تسجيل الخروج في صفحة الإعدادات
        function addLogoutButtonToSettings() {
            if (isSettingsAuthenticated) {
                var settingsPage = document.getElementById('settings');
                if (settingsPage) {
                    var existingLogoutBtn = settingsPage.querySelector('.logout-btn');
                    if (!existingLogoutBtn) {
                        var logoutBtn = document.createElement('div');
                        logoutBtn.className = 'logout-btn';
                        logoutBtn.style.cssText = `
                            position: absolute;
                            top: 20px;
                            left: 20px;
                            background: #e74c3c;
                            color: white;
                            padding: 10px 15px;
                            border-radius: 5px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            z-index: 100;
                        `;
                        logoutBtn.innerHTML = '🚪 تسجيل الخروج';
                        logoutBtn.onclick = function() {
                            if (confirm('هل تريد تسجيل الخروج من إعدادات العيادة؟')) {
                                isSettingsAuthenticated = false;
                                if (currentSessionTimeout) {
                                    clearTimeout(currentSessionTimeout);
                                }
                                alert('✅ تم تسجيل الخروج بنجاح');
                                showPage('dashboard');
                            }
                        };
                        settingsPage.appendChild(logoutBtn);
                    }
                }
            }
        }

        // تحديث دالة showPage لإضافة زر تسجيل الخروج
        var originalShowPageAuth = showPage;
        showPage = function(pageId) {
            // التحقق من الحماية للإعدادات
            if (pageId === 'settings' && !isSettingsAuthenticated) {
                authenticateSettings();
                return;
            }

            originalShowPageAuth(pageId);

            if (pageId === 'medicine-ads') {
                setTimeout(initializeMedicineAdsPage, 100);
            }

            if (pageId === 'settings') {
                setTimeout(addLogoutButtonToSettings, 100);
            }
        };
    </script>
</body>
</html>
