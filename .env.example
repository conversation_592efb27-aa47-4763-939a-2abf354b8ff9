# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings (PostgreSQL for production)
DB_NAME=clinic_management
DB_USER=postgres
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id

# Twilio (Alternative to WhatsApp)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Redis (for caching and Celery)
REDIS_URL=redis://127.0.0.1:6379/1
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# AWS S3 (for file storage in production)
USE_S3=False
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Sentry (for error tracking)
SENTRY_DSN=your-sentry-dsn

# Admin URL (for security)
ADMIN_URL=admin/

# Backup Settings
BACKUP_S3_BUCKET=your-backup-bucket

# Social Authentication (optional)
GOOGLE_OAUTH2_KEY=your-google-oauth2-key
GOOGLE_OAUTH2_SECRET=your-google-oauth2-secret

FACEBOOK_KEY=your-facebook-key
FACEBOOK_SECRET=your-facebook-secret

# Payment Gateway (optional)
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key

PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# SMS Gateway (optional)
SMS_GATEWAY_API_KEY=your-sms-api-key
SMS_GATEWAY_SENDER_ID=your-sender-id

# Push Notifications
FCM_SERVER_KEY=your-fcm-server-key
APNS_CERTIFICATE_PATH=path-to-apns-certificate

# Analytics
GOOGLE_ANALYTICS_ID=your-google-analytics-id
FACEBOOK_PIXEL_ID=your-facebook-pixel-id

# CDN Settings
CDN_URL=https://your-cdn-domain.com

# Security
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Rate Limiting
RATELIMIT_ENABLE=True

# Monitoring
MONITORING_ENABLED=True
MONITORING_INTERVAL=300

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/clinic_management/

# Backup Schedule
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Maintenance Mode
MAINTENANCE_MODE=False
MAINTENANCE_MESSAGE=النظام قيد الصيانة، يرجى المحاولة لاحقاً

# Feature Flags
ENABLE_WHATSAPP=True
ENABLE_SMS=True
ENABLE_EMAIL_NOTIFICATIONS=True
ENABLE_PUSH_NOTIFICATIONS=False
ENABLE_SOCIAL_LOGIN=False
ENABLE_PAYMENT=False
ENABLE_ANALYTICS=False

# API Settings
API_RATE_LIMIT=1000/hour
API_VERSION=v1

# File Upload Limits
MAX_FILE_SIZE=5242880  # 5MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Session Settings
SESSION_TIMEOUT=3600  # 1 hour
SESSION_SAVE_EVERY_REQUEST=True

# Cache Settings
CACHE_TIMEOUT=300  # 5 minutes
CACHE_KEY_PREFIX=clinic_

# Database Connection Pool
DB_CONN_MAX_AGE=60
DB_CONN_HEALTH_CHECKS=True

# Celery Settings
CELERY_TASK_ALWAYS_EAGER=False
CELERY_TASK_EAGER_PROPAGATES=True
CELERY_WORKER_CONCURRENCY=4

# Internationalization
DEFAULT_LANGUAGE=ar
ENABLE_LANGUAGE_SWITCHING=True

# Time Zone
DEFAULT_TIMEZONE=Asia/Baghdad

# Development Settings
ENABLE_DEBUG_TOOLBAR=False
ENABLE_SILK_PROFILING=False

# Testing
TEST_DATABASE_NAME=test_clinic_management
ENABLE_COVERAGE=True

# Docker Settings (if using Docker)
DOCKER_ENV=False
POSTGRES_HOST=db
REDIS_HOST=redis

# Kubernetes Settings (if using K8s)
K8S_NAMESPACE=clinic-management
K8S_SERVICE_ACCOUNT=clinic-sa

# Health Check URLs
HEALTH_CHECK_URL=/health/
READINESS_CHECK_URL=/ready/
LIVENESS_CHECK_URL=/alive/
