#!/bin/bash

# Clinic Management System Deployment Script
# This script automates the deployment process for production

set -e  # Exit on any error

echo "🏥 بدء نشر نظام إدارة العيادات الغذائية..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "لا تشغل هذا السكريبت كـ root"
   exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_error "ملف .env غير موجود. انسخ .env.example إلى .env وقم بتعديل الإعدادات"
    exit 1
fi

# Load environment variables
source .env

print_status "تحديث النظام..."
sudo apt update && sudo apt upgrade -y

print_status "تثبيت المتطلبات الأساسية..."
sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib nginx redis-server supervisor git

print_status "إنشاء مستخدم النظام..."
if ! id "clinic" &>/dev/null; then
    sudo useradd -m -s /bin/bash clinic
    print_success "تم إنشاء مستخدم clinic"
else
    print_warning "مستخدم clinic موجود بالفعل"
fi

# Create application directory
APP_DIR="/home/<USER>/clinic_management"
print_status "إنشاء مجلد التطبيق..."
sudo mkdir -p $APP_DIR
sudo chown clinic:clinic $APP_DIR

# Clone or update repository
if [ -d "$APP_DIR/.git" ]; then
    print_status "تحديث الكود..."
    cd $APP_DIR
    sudo -u clinic git pull origin main
else
    print_status "استنساخ المستودع..."
    sudo -u clinic git clone https://github.com/your-username/clinic-management.git $APP_DIR
    cd $APP_DIR
fi

# Create virtual environment
print_status "إنشاء البيئة الافتراضية..."
sudo -u clinic python3 -m venv venv
sudo -u clinic ./venv/bin/pip install --upgrade pip

# Install Python dependencies
print_status "تثبيت متطلبات Python..."
sudo -u clinic ./venv/bin/pip install -r requirements.txt

# Setup PostgreSQL database
print_status "إعداد قاعدة البيانات..."
sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || print_warning "قاعدة البيانات موجودة بالفعل"
sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || print_warning "المستخدم موجود بالفعل"
sudo -u postgres psql -c "ALTER ROLE $DB_USER SET client_encoding TO 'utf8';"
sudo -u postgres psql -c "ALTER ROLE $DB_USER SET default_transaction_isolation TO 'read committed';"
sudo -u postgres psql -c "ALTER ROLE $DB_USER SET timezone TO 'UTC';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"

# Copy environment file
print_status "نسخ ملف البيئة..."
sudo -u clinic cp .env $APP_DIR/

# Run Django migrations
print_status "تشغيل migrations..."
cd $APP_DIR
sudo -u clinic ./venv/bin/python manage.py makemigrations
sudo -u clinic ./venv/bin/python manage.py migrate

# Collect static files
print_status "جمع الملفات الثابتة..."
sudo -u clinic ./venv/bin/python manage.py collectstatic --noinput

# Create superuser (if not exists)
print_status "إنشاء مستخدم إداري..."
sudo -u clinic ./venv/bin/python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('تم إنشاء المستخدم الإداري')
else:
    print('المستخدم الإداري موجود بالفعل')
"

# Create directories
print_status "إنشاء المجلدات المطلوبة..."
sudo -u clinic mkdir -p $APP_DIR/logs
sudo -u clinic mkdir -p $APP_DIR/media
sudo -u clinic mkdir -p $APP_DIR/staticfiles

# Setup Gunicorn
print_status "إعداد Gunicorn..."
cat > /tmp/gunicorn.service << EOF
[Unit]
Description=Clinic Management Gunicorn daemon
After=network.target

[Service]
User=clinic
Group=clinic
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/venv/bin/gunicorn --workers 3 --bind unix:$APP_DIR/clinic_management.sock clinic_management.wsgi:application
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo mv /tmp/gunicorn.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable gunicorn
sudo systemctl start gunicorn

# Setup Nginx
print_status "إعداد Nginx..."
cat > /tmp/clinic_nginx << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    location /static/ {
        root $APP_DIR;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        root $APP_DIR;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:$APP_DIR/clinic_management.sock;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

sudo mv /tmp/clinic_nginx /etc/nginx/sites-available/clinic_management
sudo ln -sf /etc/nginx/sites-available/clinic_management /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Setup Celery (for background tasks)
print_status "إعداد Celery..."
cat > /tmp/celery.service << EOF
[Unit]
Description=Clinic Management Celery Worker
After=network.target

[Service]
Type=forking
User=clinic
Group=clinic
EnvironmentFile=$APP_DIR/.env
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/venv/bin/celery -A clinic_management worker --loglevel=info --detach
ExecStop=$APP_DIR/venv/bin/celery -A clinic_management control shutdown
ExecReload=$APP_DIR/venv/bin/celery -A clinic_management control reload
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo mv /tmp/celery.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable celery
sudo systemctl start celery

# Setup Celery Beat (for scheduled tasks)
cat > /tmp/celerybeat.service << EOF
[Unit]
Description=Clinic Management Celery Beat
After=network.target

[Service]
Type=simple
User=clinic
Group=clinic
EnvironmentFile=$APP_DIR/.env
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/venv/bin/celery -A clinic_management beat --loglevel=info
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo mv /tmp/celerybeat.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable celerybeat
sudo systemctl start celerybeat

# Setup log rotation
print_status "إعداد دوران السجلات..."
cat > /tmp/clinic_logrotate << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 clinic clinic
    postrotate
        systemctl reload gunicorn
    endscript
}
EOF

sudo mv /tmp/clinic_logrotate /etc/logrotate.d/clinic_management

# Setup firewall
print_status "إعداد الجدار الناري..."
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw --force enable

# Setup SSL with Let's Encrypt (optional)
read -p "هل تريد إعداد SSL مع Let's Encrypt؟ (y/n): " setup_ssl
if [[ $setup_ssl == "y" || $setup_ssl == "Y" ]]; then
    print_status "تثبيت Certbot..."
    sudo apt install -y certbot python3-certbot-nginx
    
    read -p "أدخل اسم النطاق: " domain_name
    sudo certbot --nginx -d $domain_name
    
    # Auto-renewal
    sudo crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | sudo crontab -
fi

# Create backup script
print_status "إنشاء سكريبت النسخ الاحتياطي..."
cat > $APP_DIR/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/clinic_management"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U $DB_USER $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Media files backup
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz -C /home/<USER>/clinic_management media/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

chmod +x $APP_DIR/backup.sh
sudo chown clinic:clinic $APP_DIR/backup.sh

# Add backup to crontab
(sudo -u clinic crontab -l 2>/dev/null; echo "0 2 * * * $APP_DIR/backup.sh") | sudo -u clinic crontab -

# Final status check
print_status "فحص حالة الخدمات..."
sudo systemctl status gunicorn --no-pager
sudo systemctl status nginx --no-pager
sudo systemctl status celery --no-pager
sudo systemctl status celerybeat --no-pager

print_success "🎉 تم نشر النظام بنجاح!"
print_status "يمكنك الآن الوصول للنظام عبر: http://your-domain.com"
print_status "لوحة الإدارة: http://your-domain.com/admin/"
print_status "اسم المستخدم: admin"
print_status "كلمة المرور: admin123"
print_warning "⚠️  تذكر تغيير كلمة مرور المدير!"

echo ""
print_status "الأوامر المفيدة:"
echo "- إعادة تشغيل Gunicorn: sudo systemctl restart gunicorn"
echo "- إعادة تشغيل Nginx: sudo systemctl restart nginx"
echo "- عرض السجلات: sudo journalctl -u gunicorn -f"
echo "- تحديث الكود: cd $APP_DIR && sudo -u clinic git pull && sudo systemctl restart gunicorn"
echo "- النسخ الاحتياطي: $APP_DIR/backup.sh"

print_success "✅ النشر مكتمل!"
