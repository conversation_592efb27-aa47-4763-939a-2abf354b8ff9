<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            direction: rtl;
            background: #f5f5f5;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            right: 0;
            top: 0;
        }

        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            padding: 0 20px;
            color: #3498db;
        }

        .nav-btn {
            display: block;
            width: 100%;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border: none;
            background: none;
            text-align: right;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: #34495e;
        }

        .nav-btn.active {
            background: #3498db;
        }

        .content {
            margin-right: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }

        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-box {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .stat-box:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .financial-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .financial-card {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }

        .financial-card.expense {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .financial-card.profit {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .money {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .currency {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>

            <button class="nav-btn active" onclick="showPage('dashboard')">
                📊 لوحة التحكم
            </button>

            <button class="nav-btn" onclick="showPage('patients')">
                👥 إدارة المرضى
            </button>

            <button class="nav-btn" onclick="showPage('nutrition')">
                🍽️ الخطط الغذائية
            </button>

            <button class="nav-btn" onclick="showPage('prescriptions')">
                💊 الوصفات الطبية
            </button>

            <button class="nav-btn" onclick="showPage('accounting')">
                💰 إدارة الحسابات
            </button>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="page active">
                <div class="card">
                    <h1>📊 لوحة التحكم</h1>
                    <div class="success">
                        ✅ مرحباً بك في نظام إدارة العيادات! النظام يعمل بشكل مثالي!
                    </div>

                    <div class="stats">
                        <div class="stat-box" onclick="alert('المرضى: 25 مريض')">
                            <div class="stat-number">25</div>
                            <div>إجمالي المرضى</div>
                        </div>
                        <div class="stat-box" onclick="alert('الخطط: 12 خطة نشطة')">
                            <div class="stat-number">12</div>
                            <div>خطط نشطة</div>
                        </div>
                        <div class="stat-box" onclick="alert('المواعيد: 8 مواعيد اليوم')">
                            <div class="stat-number">8</div>
                            <div>مواعيد اليوم</div>
                        </div>
                        <div class="stat-box" onclick="alert('الرسائل: 45 رسالة جديدة')">
                            <div class="stat-number">45</div>
                            <div>رسائل جديدة</div>
                        </div>
                    </div>

                    <h3>🎯 ميزات النظام:</h3>
                    <ul>
                        <li>✅ إدارة شاملة للمرضى والملفات الطبية</li>
                        <li>✅ كتابة وطباعة الوصفات الطبية</li>
                        <li>✅ نظام محاسبي متكامل بالدينار العراقي</li>
                        <li>✅ إدارة الخطط الغذائية المتخصصة</li>
                        <li>✅ تكامل مع الواتساب لإرسال الرسائل</li>
                    </ul>
                </div>
            </div>

            <!-- Patients -->
            <div id="patients" class="page">
                <div class="card">
                    <h1>👥 إدارة المرضى</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                    </div>

                    <h3>🔧 الوظائف المتاحة:</h3>
                    <ul>
                        <li>إضافة مرضى جدد مع جميع البيانات</li>
                        <li>تعديل وتحديث بيانات المرضى</li>
                        <li>عرض التاريخ الطبي الكامل</li>
                        <li>متابعة تقدم المرضى</li>
                    </ul>

                    <button class="btn btn-success" onclick="alert('إضافة مريض جديد')">
                        ➕ إضافة مريض جديد
                    </button>
                    <button class="btn" onclick="alert('عرض قائمة المرضى')">
                        📋 عرض قائمة المرضى
                    </button>
                </div>
            </div>

            <!-- Nutrition -->
            <div id="nutrition" class="page">
                <div class="card">
                    <h1>🍽️ الخطط الغذائية</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                    </div>

                    <div class="stats">
                        <div class="stat-box" style="background: linear-gradient(135deg, #e74c3c, #c0392b);" onclick="alert('خطط إنقاص الوزن: 12 خطة')">
                            <div class="stat-number">12</div>
                            <div>خطط إنقاص الوزن</div>
                        </div>
                        <div class="stat-box" style="background: linear-gradient(135deg, #27ae60, #2ecc71);" onclick="alert('خطط زيادة الوزن: 8 خطط')">
                            <div class="stat-number">8</div>
                            <div>خطط زيادة الوزن</div>
                        </div>
                        <div class="stat-box" style="background: linear-gradient(135deg, #f39c12, #e67e22);" onclick="alert('خطط الحفاظ على الوزن: 5 خطط')">
                            <div class="stat-number">5</div>
                            <div>خطط الحفاظ على الوزن</div>
                        </div>
                    </div>

                    <button class="btn btn-success" onclick="alert('إنشاء خطة غذائية جديدة')">
                        ➕ إنشاء خطة جديدة
                    </button>
                </div>
            </div>

            <!-- Prescriptions -->
            <div id="prescriptions" class="page">
                <div class="card">
                    <h1>💊 الوصفات الطبية</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                    </div>

                    <div class="stats">
                        <div class="stat-box" onclick="alert('وصفات اليوم: 8 وصفات')">
                            <div class="stat-number">8</div>
                            <div>وصفات اليوم</div>
                        </div>
                        <div class="stat-box" style="background: linear-gradient(135deg, #f093fb, #f5576c);" onclick="alert('إجمالي الوصفات: 156 وصفة')">
                            <div class="stat-number">156</div>
                            <div>إجمالي الوصفات</div>
                        </div>
                        <div class="stat-box" style="background: linear-gradient(135deg, #4facfe, #00f2fe);" onclick="alert('وصفات مطبوعة: 142 وصفة')">
                            <div class="stat-number">142</div>
                            <div>وصفات مطبوعة</div>
                        </div>
                    </div>

                    <button class="btn btn-success" onclick="alert('كتابة وصفة جديدة')">
                        📝 كتابة وصفة جديدة
                    </button>
                    <button class="btn" onclick="printPrescription()">
                        🖨️ طباعة وصفة تجريبية
                    </button>
                    <button class="btn" onclick="alert('أرشيف الوصفات')">
                        📚 أرشيف الوصفات
                    </button>
                </div>
            </div>

            <!-- Accounting -->
            <div id="accounting" class="page">
                <div class="card">
                    <h1>💰 إدارة الحسابات</h1>
                    <div class="success">
                        ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                    </div>

                    <div class="financial-cards">
                        <div class="financial-card">
                            <h3>💚 إجمالي الإيرادات</h3>
                            <div class="money">58,500,000</div>
                            <div class="currency">د.ع</div>
                            <small>+15% هذا الشهر</small>
                        </div>
                        <div class="financial-card expense">
                            <h3>💸 إجمالي المصروفات</h3>
                            <div class="money">24,050,000</div>
                            <div class="currency">د.ع</div>
                            <small>-5% عن الشهر الماضي</small>
                        </div>
                        <div class="financial-card profit">
                            <h3>📈 صافي الربح</h3>
                            <div class="money">34,450,000</div>
                            <div class="currency">د.ع</div>
                            <small>+25% نمو</small>
                        </div>
                    </div>

                    <h3>💼 الخدمات المالية:</h3>
                    <ul>
                        <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                        <li>إدارة الفواتير والمدفوعات</li>
                        <li>تقارير مالية شهرية ومفصلة</li>
                        <li>حساب الضرائب المستحقة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            console.log('تم النقر على:', pageId);

            // إخفاء جميع الصفحات
            var pages = document.querySelectorAll('.page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }

            // إزالة active من جميع الأزرار
            var buttons = document.querySelectorAll('.nav-btn');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }

            // إظهار الصفحة المحددة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('تم إظهار الصفحة:', pageId);
            }

            // إضافة active للزر المحدد
            var clickedButton = event.target;
            if (clickedButton) {
                clickedButton.classList.add('active');
            }

            // رسالة تأكيد
            alert('تم الانتقال إلى: ' + getPageName(pageId));
        }

        function getPageName(pageId) {
            var names = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'prescriptions': 'الوصفات الطبية',
                'accounting': 'إدارة الحسابات'
            };
            return names[pageId] || pageId;
        }

        function printPrescription() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            var content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            line-height: 1.6;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #2c3e50;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .clinic-name {
                            font-size: 28px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                        .patient-info {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                            border-right: 5px solid #3498db;
                        }
                        .medicine {
                            background: #fff;
                            border: 2px solid #e9ecef;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 8px;
                            border-right: 4px solid #27ae60;
                        }
                        .medicine-name {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 8px;
                        }
                        .instructions {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .signature {
                            margin-top: 50px;
                            text-align: left;
                        }
                        .signature-line {
                            border-bottom: 2px solid #333;
                            width: 200px;
                            margin-bottom: 10px;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="clinic-name">🏥 عيادة التغذية العلاجية</div>
                        <div>د. [اسم الطبيب] - أخصائي التغذية العلاجية</div>
                        <div>العنوان: [عنوان العيادة] | الهاتف: [رقم الهاتف]</div>
                    </div>

                    <div class="patient-info">
                        <strong>👤 بيانات المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>📅 تاريخ الوصفة:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                        <strong>🆔 رقم الوصفة:</strong> RX-${Math.floor(Math.random() * 100000)}<br>
                        <strong>🔍 التشخيص:</strong> نقص فيتامين د وضعف عام
                    </div>

                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">💊 الأدوية المطلوبة:</h3>

                    <div class="medicine">
                        <div class="medicine-name">1. فيتامين د3 (Vitamin D3)</div>
                        <div><strong>التركيز:</strong> 1000 وحدة دولية</div>
                        <div><strong>الجرعة:</strong> حبة واحدة يومياً مع الطعام</div>
                        <div><strong>المدة:</strong> 3 أشهر</div>
                    </div>

                    <div class="medicine">
                        <div class="medicine-name">2. كالسيوم (Calcium Carbonate)</div>
                        <div><strong>التركيز:</strong> 500 ملغ</div>
                        <div><strong>الجرعة:</strong> حبة واحدة مع الطعام مساءً</div>
                        <div><strong>المدة:</strong> شهر واحد</div>
                    </div>

                    <div class="instructions">
                        <h4 style="color: #856404; margin-top: 0;">⚠️ التعليمات العامة:</h4>
                        <ul>
                            <li>التعرض لأشعة الشمس صباحاً لمدة 15-20 دقيقة يومياً</li>
                            <li>شرب كمية كافية من الماء (8-10 أكواب يومياً)</li>
                            <li>تناول الأدوية مع الطعام لتجنب اضطراب المعدة</li>
                            <li>المتابعة بعد شهر واحد لتقييم التحسن</li>
                        </ul>
                    </div>

                    <div class="signature">
                        <div>توقيع الطبيب:</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 14px; color: #666;">د. [اسم الطبيب]</div>
                        <div style="font-size: 12px; color: #999; margin-top: 20px;">
                            تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                        </div>
                    </div>

                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `;

            printWindow.document.write(content);
            printWindow.document.close();
        }

        // تأكيد تحميل الصفحة
        console.log('تم تحميل نظام إدارة العيادات بنجاح!');
        console.log('جميع الوظائف جاهزة للاستخدام!');
    </script>
</body>
</html>