#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clinic_management.settings')
django.setup()

from patients.models import Patient
from accounts.models import User

def create_patients():
    """إنشاء مرضى جدد للاختبار"""
    try:
        # الحصول على الطبيب
        doctor = User.objects.get(username='doctor1')
        print(f"الطبيب: {doctor.username}")

        # التحقق من المرضى الموجودين
        existing_patients = Patient.objects.filter(doctor=doctor)
        print(f"عدد المرضى الموجودين: {existing_patients.count()}")

        # عرض المرضى الموجودين
        for patient in existing_patients:
            print(f"- المريض {patient.id}: {patient.user.get_full_name()} ({patient.user.username})")

        # إنشاء مرضى جدد
        names = [
            ('أحمد', 'محمد'),
            ('فاطمة', 'علي'),
            ('محمد', 'حسن'),
            ('زينب', 'أحمد'),
            ('علي', 'محمود'),
            ('مريم', 'خالد'),
            ('حسن', 'عبدالله'),
            ('نور', 'سعد'),
            ('يوسف', 'إبراهيم'),
            ('سارة', 'عمر')
        ]

        for i, (first_name, last_name) in enumerate(names, start=2):
            # التحقق من عدم وجود المستخدم
            username = f'patient_{i}'
            if User.objects.filter(username=username).exists():
                print(f"المريض {username} موجود بالفعل")
                continue

            # إنشاء مستخدم جديد
            patient_user = User.objects.create_user(
                username=username,
                email=f'patient{i}@clinic.com',
                password='patient123',
                first_name=first_name,
                last_name=last_name,
                user_type='patient'
            )

            # إنشاء ملف المريض
            patient = Patient.objects.create(
                user=patient_user,
                doctor=doctor,
                gender='male' if i % 2 == 0 else 'female',
                height=160 + (i % 30),
                current_weight=60 + (i % 40),
                target_weight=55 + (i % 35),
                health_goal='weight_loss' if i % 2 == 0 else 'muscle_gain'
            )
            print(f"تم إنشاء المريض {patient.id}: {patient.user.get_full_name()}")

        # عرض النتيجة النهائية
        total_patients = Patient.objects.filter(doctor=doctor).count()
        print(f"\nإجمالي المرضى الآن: {total_patients}")

        # عرض جميع المرضى
        print("\nقائمة المرضى النهائية:")
        for patient in Patient.objects.filter(doctor=doctor).order_by('id'):
            print(f"- المريض {patient.id}: {patient.user.get_full_name()} ({patient.user.username})")

    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_patients()
