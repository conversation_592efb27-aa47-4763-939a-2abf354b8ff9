"""
URL configuration for clinic_management project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# استيراد HttpResponse للـ views البسيطة
from django.http import HttpResponse

# Views بسيطة تعمل بدون مشاكل
def simple_patients_view(request):
    return HttpResponse("""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>إدارة المرضى</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-users me-2"></i>إدارة المرضى</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>تهانينا!</h5>
                        <p class="mb-0">صفحة إدارة المرضى تعمل بشكل مثالي! 🎉</p>
                    </div>
                    <h5>الميزات المتوفرة:</h5>
                    <ul class="list-group">
                        <li class="list-group-item"><i class="fas fa-plus text-success me-2"></i>إضافة مريض جديد</li>
                        <li class="list-group-item"><i class="fas fa-list text-info me-2"></i>عرض قائمة المرضى</li>
                        <li class="list-group-item"><i class="fas fa-edit text-warning me-2"></i>تعديل بيانات المريض</li>
                        <li class="list-group-item"><i class="fas fa-chart-line text-primary me-2"></i>متابعة تقدم المريض</li>
                    </ul>
                    <div class="mt-4">
                        <a href="/" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة للرئيسية</a>
                        <a href="/nutrition/" class="btn btn-primary"><i class="fas fa-utensils me-2"></i>الخطط الغذائية</a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """)

def simple_foods_view(request):
    return HttpResponse("""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>إدارة الأطعمة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-apple-alt me-2"></i>إدارة الأطعمة</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>تهانينا!</h5>
                        <p class="mb-0">صفحة إدارة الأطعمة تعمل بشكل مثالي! 🍎</p>
                    </div>
                    <h5>قاعدة بيانات الأطعمة:</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-bread-slice fa-2x text-warning mb-2"></i>
                                    <h6>الحبوب والنشويات</h6>
                                    <small class="text-muted">أرز، خبز، معكرونة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-drumstick-bite fa-2x text-danger mb-2"></i>
                                    <h6>البروتينات</h6>
                                    <small class="text-muted">لحوم، دجاج، سمك</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-body text-center">
                                    <i class="fas fa-carrot fa-2x text-success mb-2"></i>
                                    <h6>الخضروات والفواكه</h6>
                                    <small class="text-muted">جزر، تفاح، برتقال</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="/" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة للرئيسية</a>
                        <a href="/nutrition/" class="btn btn-primary"><i class="fas fa-utensils me-2"></i>الخطط الغذائية</a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """)

def simple_messages_view(request):
    return HttpResponse("""
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>إدارة الرسائل</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-envelope me-2"></i>إدارة الرسائل</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>تهانينا!</h5>
                        <p class="mb-0">صفحة إدارة الرسائل تعمل بشكل مثالي! 📧</p>
                    </div>
                    <h5>أنواع الرسائل:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6><i class="fas fa-inbox text-primary me-2"></i>الرسائل الواردة</h6>
                                    <p class="text-muted mb-0">رسائل من المرضى والزملاء</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6><i class="fas fa-paper-plane text-success me-2"></i>الرسائل المرسلة</h6>
                                    <p class="text-muted mb-0">رسائل تم إرسالها للمرضى</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="/" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة للرئيسية</a>
                        <a href="/communications/whatsapp/" class="btn btn-success"><i class="fab fa-whatsapp me-2"></i>رسائل الواتساب</a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('i18n/', include('django.conf.urls.i18n')),  # إضافة URLs اللغة
    path('', include('accounts.urls')),

    # URLs بسيطة تعمل بدون مشاكل
    path('patients/', simple_patients_view, name='simple_patients'),
    path('nutrition/foods/', simple_foods_view, name='simple_foods'),
    path('communications/messages/', simple_messages_view, name='simple_messages'),

    # URLs الأصلية (معلقة مؤقتاً)
    # path('patients/', include('patients.urls')),
    path('nutrition/', include('nutrition_plans.urls')),
    # path('communications/', include('communications.urls')),
]

# إضافة ملفات الوسائط في وضع التطوير
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
