<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة العامة - نظام إدارة العيادات</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&family=Almarai:wght@300;400;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', 'Almarai', 'Segoe UI', '<PERSON>homa', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
        }

        .admin-header {
            background: linear-gradient(135deg, #0f766e 0%, #14b8a6 50%, #5eead4 100%);
            color: white;
            padding: 20px 40px;
            box-shadow: 0 4px 20px rgba(15, 118, 110, 0.4);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 50%, rgba(255,255,255,0.12) 1px, transparent 1px),
                radial-gradient(circle at 80% 50%, rgba(255,255,255,0.08) 1px, transparent 1px),
                radial-gradient(circle at 40% 20%, rgba(255,255,255,0.06) 1px, transparent 1px),
                radial-gradient(circle at 60% 80%, rgba(255,255,255,0.06) 1px, transparent 1px),
                linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.04) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(255,255,255,0.04) 50%, transparent 60%);
            background-size: 25px 25px, 20px 20px, 30px 30px, 22px 22px, 12px 12px, 15px 15px;
            background-position: 0 0, 12px 12px, 4px 4px, 16px 16px, 0 0, 8px 8px;
            opacity: 0.7;
            pointer-events: none;
        }

        .admin-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 1px,
                    rgba(255,255,255,0.03) 1px,
                    rgba(255,255,255,0.03) 2px
                ),
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 1px,
                    rgba(255,255,255,0.03) 1px,
                    rgba(255,255,255,0.03) 2px
                );
            opacity: 0.5;
            pointer-events: none;
        }

        .admin-title {
            font-size: 28px;
            font-weight: 800;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
            letter-spacing: 0.5px;
            position: relative;
            z-index: 3;
        }

        .admin-info {
            text-align: right;
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 3;
            backdrop-filter: blur(5px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 36px;
            margin-bottom: 12px;
            display: block;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 900;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            margin-bottom: 8px;
            color: #2c3e50;
            letter-spacing: 1px;
        }

        .stat-label {
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .stat-description {
            font-size: 12px;
            color: #95a5a6;
            line-height: 1.5;
        }

        .management-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .management-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .management-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 24px;
            margin-left: 12px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 700;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: #2c3e50;
            letter-spacing: 0.3px;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
            margin: 5px;
            letter-spacing: 0.3px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        /* تنسيق الأرقام والتواريخ - إنجليزي من اليسار لليمين */
        input[type="number"],
        input[type="tel"],
        input[type="date"],
        input[type="time"],
        input[type="email"],
        input[type="url"],
        .number-field,
        .date-field,
        .currency-field {
            direction: ltr !important;
            text-align: left !important;
            font-family: 'Courier New', monospace;
        }

        .currency-display,
        .number-display,
        .date-display,
        .time-display {
            direction: ltr !important;
            text-align: left !important;
            font-family: 'Courier New', monospace;
        }

        /* تنسيق خاص للعملة */
        .currency-amount {
            direction: ltr !important;
            text-align: right !important;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="admin-title">🏥 لوحة الإدارة العامة</div>
        <div class="admin-info">
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">مدير النظام</div>
            <div style="font-size: 14px; opacity: 0.9;">نظام إدارة العيادات المتقدم</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 5px;" id="current-date"></div>
        </div>
    </div>

    <div class="container">
        <!-- إحصائيات النظام -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">🏥</span>
                <div class="stat-number" id="total-clinics">5</div>
                <div class="stat-label">العيادات المسجلة</div>
                <div class="stat-description">إجمالي العيادات المسجلة في النظام</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">👨‍⚕️</span>
                <div class="stat-number" id="total-doctors">12</div>
                <div class="stat-label">الأطباء النشطين</div>
                <div class="stat-description">عدد الأطباء المستخدمين للنظام</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-number" id="total-patients">150</div>
                <div class="stat-label">إجمالي المرضى</div>
                <div class="stat-description">عدد المرضى في جميع العيادات</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-number" id="total-revenue">2,500,000</div>
                <div class="stat-label">الإيرادات الشهرية</div>
                <div class="stat-description">إجمالي إيرادات النظام</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">🔐</span>
                <div class="stat-number" id="total-credentials">0</div>
                <div class="stat-label">أوراق الاعتماد</div>
                <div class="stat-description">عدد أوراق الاعتماد النشطة</div>
            </div>
        </div>

        <!-- إدارة النظام -->
        <div class="management-grid">
            <!-- إدارة العيادات -->
            <div class="management-card">
                <div class="card-header">
                    <span class="card-icon">🏥</span>
                    <div class="card-title">إدارة العيادات</div>
                </div>
                <p style="color: #7f8c8d; margin-bottom: 15px; font-size: 13px;">إدارة العيادات المسجلة والأطباء</p>
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="registerClinic()">➕ تسجيل عيادة جديدة</button>
                    <button class="btn" onclick="viewClinics()">👁️ عرض العيادات</button>
                    <button class="btn btn-warning" onclick="clinicReports()">📊 تقارير العيادات</button>
                </div>
            </div>

            <!-- إدارة المالية -->
            <div class="management-card">
                <div class="card-header">
                    <span class="card-icon">💰</span>
                    <div class="card-title">إدارة المالية</div>
                </div>
                <p style="color: #7f8c8d; margin-bottom: 15px; font-size: 13px;">إدارة الإيرادات والمصروفات والتقارير المالية</p>
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addIncome()">💰 إضافة إيراد</button>
                    <button class="btn btn-danger" onclick="addExpense()">💸 إضافة مصروف</button>
                    <button class="btn" onclick="financialOverview()">📊 النظرة العامة</button>
                    <button class="btn btn-warning" onclick="financialReports()">📈 التقارير المالية</button>
                </div>
            </div>

            <!-- إعدادات البرنامج -->
            <div class="management-card">
                <div class="card-header">
                    <span class="card-icon">⚙️</span>
                    <div class="card-title">إعدادات البرنامج</div>
                </div>
                <p style="color: #7f8c8d; margin-bottom: 15px; font-size: 13px;">إدارة اسم ولوجو البرنامج المعروض في جميع العيادات</p>
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="configureProgramSettings()">🎨 تكوين إعدادات البرنامج</button>
                    <button class="btn" onclick="viewProgramSettings()">👁️ عرض الإعدادات الحالية</button>
                    <button class="btn btn-warning" onclick="resetProgramSettings()">🔄 إعادة تعيين الإعدادات</button>
                </div>
            </div>

            <!-- إدارة أمان النظام -->
            <div class="management-card">
                <div class="card-header">
                    <span class="card-icon">🔐</span>
                    <div class="card-title">إدارة أمان النظام</div>
                </div>
                <p style="color: #7f8c8d; margin-bottom: 15px; font-size: 13px;">إدارة أوراق اعتماد الوصول لإعدادات العيادات</p>
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="createCredentials()">🔑 إنشاء أوراق اعتماد جديدة</button>
                    <button class="btn" onclick="viewCredentials()">👁️ عرض أوراق الاعتماد</button>
                    <button class="btn btn-warning" onclick="manageCredentialsByClinic()">🔄 إدارة أوراق الاعتماد حسب العيادة</button>
                    <button class="btn btn-danger" onclick="revokeAllCredentials()">🚫 إلغاء جميع أوراق الاعتماد</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // دوال تنسيق التاريخ والوقت من اليسار إلى اليمين
        function formatDateLTR(date) {
            // تنسيق التاريخ بالإنجليزية من اليسار إلى اليمين (MM/DD/YYYY)
            var dateObj = typeof date === 'string' ? new Date(date) : date;
            return dateObj.toLocaleDateString('en-US');
        }

        function formatTimeLTR(date) {
            // تنسيق الوقت بالإنجليزية من اليسار إلى اليمين (24 ساعة)
            var dateObj = typeof date === 'string' ? new Date(date) : date;
            return dateObj.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatDateTimeLTR(date) {
            // تنسيق التاريخ والوقت معاً بالإنجليزية من اليسار إلى اليمين
            return formatDateLTR(date) + ' - ' + formatTimeLTR(date);
        }

        function formatNumberLTR(number) {
            // تنسيق الأرقام بالإنجليزية من اليسار إلى اليمين
            return number.toLocaleString('en-US');
        }

        function formatCurrencyLTR(amount) {
            // تنسيق العملة بالإنجليزية من اليسار إلى اليمين
            return formatNumberLTR(amount) + ' IQD';
        }

        // تحديث التاريخ الحالي
        document.getElementById('current-date').textContent = formatDateLTR(new Date());

        // دوال إدارة العيادات
        function registerClinic() {
            showModal('registerClinicModal', 'تسجيل عيادة جديدة', `
                <form id="registerClinicForm" onsubmit="saveClinic(event)">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العيادة *</label>
                            <input type="text" id="clinicName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الطبيب *</label>
                            <input type="text" id="doctorName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">التخصص *</label>
                            <select id="specialty" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر التخصص</option>
                                <option value="طب عام">طب عام</option>
                                <option value="طب أطفال">طب أطفال</option>
                                <option value="طب نساء وولادة">طب نساء وولادة</option>
                                <option value="طب قلب">طب قلب</option>
                                <option value="طب عظام">طب عظام</option>
                                <option value="طب أسنان">طب أسنان</option>
                                <option value="طب عيون">طب عيون</option>
                                <option value="طب جلدية">طب جلدية</option>
                                <option value="طب نفسي">طب نفسي</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف *</label>
                            <input type="tel" id="clinicPhone" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">العنوان *</label>
                            <textarea id="clinicAddress" required rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></textarea>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني</label>
                            <input type="email" id="clinicEmail" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الموقع الإلكتروني</label>
                            <input type="url" id="clinicWebsite" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأقصى للمستخدمين *</label>
                            <input type="number" id="maxUsers" required min="1" max="50" value="5" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <small style="color: #666; font-size: 12px;">عدد المستخدمين المسموح لهذه العيادة (1-50)</small>
                            <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; margin-top: 10px; border-left: 3px solid #28a745;">
                                <small style="color: #155724; font-size: 11px;">
                                    <strong>ملاحظة:</strong> هذا هو الإعداد الوحيد الذي يتم التحكم به من الإدارة العامة.
                                    باقي إعدادات العيادة (اسم الطبيب، العنوان، إلخ) يتم إدارتها من admin العيادة نفسها.
                                </small>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ حفظ العيادة</button>
                        <button type="button" onclick="closeModal('registerClinicModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);
        }

        function saveClinic(event) {
            event.preventDefault();

            var clinicData = {
                id: 'clinic_' + Date.now(),
                name: document.getElementById('clinicName').value,
                doctorName: document.getElementById('doctorName').value,
                specialty: document.getElementById('specialty').value,
                phone: document.getElementById('clinicPhone').value,
                address: document.getElementById('clinicAddress').value,
                email: document.getElementById('clinicEmail').value,
                website: document.getElementById('clinicWebsite').value,
                maxUsers: parseInt(document.getElementById('maxUsers').value),
                registrationDate: new Date().toISOString(),
                status: 'نشط'
            };

            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            clinics.push(clinicData);
            localStorage.setItem('registeredClinics', JSON.stringify(clinics));

            // حفظ أيضاً في مفتاح منفصل للوصول من العيادات
            localStorage.setItem('clinics', JSON.stringify(clinics));

            alert('✅ تم تسجيل العيادة بنجاح!\n\nاسم العيادة: ' + clinicData.name + '\nالطبيب: ' + clinicData.doctorName + '\nالتخصص: ' + clinicData.specialty + '\nالحد الأقصى للمستخدمين: ' + clinicData.maxUsers);

            closeModal('registerClinicModal');
            updateStats();
        }

        function viewClinics() {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            if (clinics.length === 0) {
                showModal('viewClinicsModal', 'العيادات المسجلة', '<p style="text-align: center; color: #666; padding: 40px;">لا توجد عيادات مسجلة</p>');
                return;
            }

            var content = '<div style="max-height: 500px; overflow-y: auto;">';
            clinics.forEach(function(clinic, index) {
                content += `
                    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                            <div>
                                <h4 style="color: #2c3e50; margin: 0 0 5px 0;">🏥 ${clinic.name}</h4>
                                <p style="color: #27ae60; margin: 0 0 5px 0; font-weight: bold;">👨‍⚕️ د. ${clinic.doctorName}</p>
                                <p style="color: #3498db; margin: 0 0 10px 0;">📋 ${clinic.specialty}</p>
                            </div>
                            <span style="background: #27ae60; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${clinic.status}</span>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            <p style="margin: 5px 0;">📞 ${clinic.phone}</p>
                            <p style="margin: 5px 0;">📍 ${clinic.address}</p>
                            ${clinic.email ? `<p style="margin: 5px 0;">📧 ${clinic.email}</p>` : ''}
                            ${clinic.website ? `<p style="margin: 5px 0;">🌐 ${clinic.website}</p>` : ''}
                            <p style="margin: 5px 0;">👥 الحد الأقصى للمستخدمين: <span style="color: #007bff; font-weight: bold;">${clinic.maxUsers || 'غير محدد'}</span></p>
                            <p style="margin: 5px 0;">📅 تاريخ التسجيل: ${formatDateLTR(new Date(clinic.registrationDate))}</p>
                        </div>
                        <div style="text-align: left; margin-top: 10px;">
                            <button onclick="editClinicUsers('${clinic.id}')" style="background: #17a2b8; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">👥 إدارة المستخدمين</button>
                            <button onclick="editClinic('${clinic.id}')" style="background: #f39c12; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">✏️ تعديل</button>
                            <button onclick="deleteClinic('${clinic.id}')" style="background: #e74c3c; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">🗑️ حذف</button>
                        </div>
                    </div>
                `;
            });
            content += '</div>';

            showModal('viewClinicsModal', 'العيادات المسجلة (' + clinics.length + ')', content);
        }

        function editClinicUsers(clinicId) {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinic = clinics.find(function(c) { return c.id === clinicId; });

            if (!clinic) {
                alert('⚠️ لم يتم العثور على العيادة');
                return;
            }

            var currentMaxUsers = clinic.maxUsers || 5;
            var message = 'تعديل الحد الأقصى للمستخدمين\n\n';
            message += 'العيادة: ' + clinic.name + '\n';
            message += 'الحد الحالي: ' + currentMaxUsers + '\n\n';
            message += '📝 ملاحظة: هذا هو الإعداد الوحيد الذي تتحكم به الإدارة العامة.\n';
            message += 'باقي إعدادات العيادة يديرها admin العيادة نفسها.\n\n';
            message += 'أدخل الحد الجديد (1-50):';

            var newMaxUsers = prompt(message, currentMaxUsers);

            if (newMaxUsers === null) return; // إلغاء

            newMaxUsers = parseInt(newMaxUsers);

            if (isNaN(newMaxUsers) || newMaxUsers < 1 || newMaxUsers > 50) {
                alert('⚠️ يرجى إدخال رقم صحيح بين 1 و 50');
                return;
            }

            // تحديث الحد الأقصى
            clinic.maxUsers = newMaxUsers;
            localStorage.setItem('registeredClinics', JSON.stringify(clinics));

            // تحديث أيضاً في مفتاح clinics للتوافق
            var clinicsBackup = JSON.parse(localStorage.getItem('clinics') || '[]');
            var clinicBackup = clinicsBackup.find(function(c) { return c.id === clinicId; });
            if (clinicBackup) {
                clinicBackup.maxUsers = newMaxUsers;
                localStorage.setItem('clinics', JSON.stringify(clinicsBackup));
            }

            alert('✅ تم تحديث الحد الأقصى للمستخدمين بنجاح!\n\n' +
                  'العيادة: ' + clinic.name + '\n' +
                  'الحد الجديد: ' + newMaxUsers + ' مستخدم\n\n' +
                  '📋 سيتم تطبيق التغيير في العيادة عند تحديث الإعدادات من قسم إدارة المستخدمين');

            // إعادة تحميل قائمة العيادات
            viewClinics();
        }

        function editClinic(clinicId) {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinic = clinics.find(function(c) { return c.id === clinicId; });

            if (!clinic) {
                alert('⚠️ لم يتم العثور على العيادة');
                return;
            }

            // إنشاء نموذج التعديل
            var modalContent = `
                <form id="editClinicForm" onsubmit="updateClinic(event, '${clinicId}')">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العيادة *</label>
                            <input type="text" id="editClinicName" value="${clinic.name}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الطبيب *</label>
                            <input type="text" id="editDoctorName" value="${clinic.doctorName}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">التخصص *</label>
                            <select id="editSpecialty" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="طب عام" ${clinic.specialty === 'طب عام' ? 'selected' : ''}>طب عام</option>
                                <option value="طب أطفال" ${clinic.specialty === 'طب أطفال' ? 'selected' : ''}>طب أطفال</option>
                                <option value="طب نساء وولادة" ${clinic.specialty === 'طب نساء وولادة' ? 'selected' : ''}>طب نساء وولادة</option>
                                <option value="طب قلب" ${clinic.specialty === 'طب قلب' ? 'selected' : ''}>طب قلب</option>
                                <option value="طب عظام" ${clinic.specialty === 'طب عظام' ? 'selected' : ''}>طب عظام</option>
                                <option value="طب أسنان" ${clinic.specialty === 'طب أسنان' ? 'selected' : ''}>طب أسنان</option>
                                <option value="طب عيون" ${clinic.specialty === 'طب عيون' ? 'selected' : ''}>طب عيون</option>
                                <option value="طب جلدية" ${clinic.specialty === 'طب جلدية' ? 'selected' : ''}>طب جلدية</option>
                                <option value="طب نفسي" ${clinic.specialty === 'طب نفسي' ? 'selected' : ''}>طب نفسي</option>
                                <option value="أخرى" ${clinic.specialty === 'أخرى' ? 'selected' : ''}>أخرى</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف *</label>
                            <input type="tel" id="editClinicPhone" value="${clinic.phone}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني</label>
                            <input type="email" id="editClinicEmail" value="${clinic.email || ''}" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الموقع الإلكتروني</label>
                            <input type="url" id="editClinicWebsite" value="${clinic.website || ''}" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأقصى للمستخدمين *</label>
                            <input type="number" id="editMaxUsers" value="${clinic.maxUsers || 5}" required min="1" max="50" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <small style="color: #666; font-size: 12px;">عدد المستخدمين المسموح (1-50)</small>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">حالة العيادة</label>
                            <select id="editClinicStatus" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="نشط" ${clinic.status === 'نشط' ? 'selected' : ''}>نشط</option>
                                <option value="معطل" ${clinic.status === 'معطل' ? 'selected' : ''}>معطل</option>
                                <option value="قيد المراجعة" ${clinic.status === 'قيد المراجعة' ? 'selected' : ''}>قيد المراجعة</option>
                            </select>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">العنوان *</label>
                            <textarea id="editClinicAddress" required rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">${clinic.address}</textarea>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h6 style="color: #495057; margin: 0 0 10px 0;">📊 معلومات إضافية</h6>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px; color: #666;">
                            <div><strong>تاريخ التسجيل:</strong> ${formatDateLTR(new Date(clinic.registrationDate))}</div>
                            <div><strong>معرف العيادة:</strong> ${clinic.id}</div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; font-weight: bold;">✅ حفظ التعديلات</button>
                        <button type="button" onclick="closeModal('editClinicModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `;

            showModal('editClinicModal', '✏️ تعديل بيانات العيادة', modalContent);
        }

        function updateClinic(event, clinicId) {
            event.preventDefault();

            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinicIndex = clinics.findIndex(function(c) { return c.id === clinicId; });

            if (clinicIndex === -1) {
                alert('⚠️ لم يتم العثور على العيادة');
                return;
            }

            // جمع البيانات المحدثة
            var updatedClinic = {
                ...clinics[clinicIndex], // الاحتفاظ بالبيانات الأصلية
                name: document.getElementById('editClinicName').value,
                doctorName: document.getElementById('editDoctorName').value,
                specialty: document.getElementById('editSpecialty').value,
                phone: document.getElementById('editClinicPhone').value,
                address: document.getElementById('editClinicAddress').value,
                email: document.getElementById('editClinicEmail').value,
                website: document.getElementById('editClinicWebsite').value,
                maxUsers: parseInt(document.getElementById('editMaxUsers').value),
                status: document.getElementById('editClinicStatus').value
            };

            // تحديث العيادة في المصفوفة
            clinics[clinicIndex] = updatedClinic;

            // حفظ التحديثات
            localStorage.setItem('registeredClinics', JSON.stringify(clinics));

            // تحديث أيضاً في مفتاح clinics للتوافق
            var clinicsBackup = JSON.parse(localStorage.getItem('clinics') || '[]');
            var backupIndex = clinicsBackup.findIndex(function(c) { return c.id === clinicId; });
            if (backupIndex !== -1) {
                clinicsBackup[backupIndex] = updatedClinic;
                localStorage.setItem('clinics', JSON.stringify(clinicsBackup));
            }

            alert('✅ تم تحديث بيانات العيادة بنجاح!\n\n' +
                  'اسم العيادة: ' + updatedClinic.name + '\n' +
                  'الطبيب: ' + updatedClinic.doctorName + '\n' +
                  'التخصص: ' + updatedClinic.specialty + '\n' +
                  'الحد الأقصى للمستخدمين: ' + updatedClinic.maxUsers);

            closeModal('editClinicModal');
            viewClinics(); // إعادة تحميل قائمة العيادات
        }

        function deleteClinic(clinicId) {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinic = clinics.find(function(c) { return c.id === clinicId; });

            if (!clinic) {
                alert('⚠️ لم يتم العثور على العيادة');
                return;
            }

            var confirmMessage = '⚠️ هل أنت متأكد من حذف هذه العيادة؟\n\n';
            confirmMessage += 'اسم العيادة: ' + clinic.name + '\n';
            confirmMessage += 'الطبيب: ' + clinic.doctorName + '\n';
            confirmMessage += 'التخصص: ' + clinic.specialty + '\n\n';
            confirmMessage += '🚨 تحذير: سيتم حذف جميع البيانات المرتبطة بهذه العيادة!\n';
            confirmMessage += 'لا يمكن التراجع عن هذا الإجراء.';

            if (confirm(confirmMessage)) {
                // حذف من registeredClinics
                var updatedClinics = clinics.filter(function(c) { return c.id !== clinicId; });
                localStorage.setItem('registeredClinics', JSON.stringify(updatedClinics));

                // حذف أيضاً من clinics للتوافق
                var clinicsBackup = JSON.parse(localStorage.getItem('clinics') || '[]');
                var updatedBackup = clinicsBackup.filter(function(c) { return c.id !== clinicId; });
                localStorage.setItem('clinics', JSON.stringify(updatedBackup));

                alert('✅ تم حذف العيادة بنجاح!\n\nاسم العيادة: ' + clinic.name);
                viewClinics(); // إعادة تحميل القائمة
                updateStats();
            }
        }

        function clinicReports() {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            if (clinics.length === 0) {
                alert('📊 لا توجد عيادات لإنشاء التقارير');
                return;
            }

            var specialtyCount = {};
            var statusCount = { 'نشط': 0, 'غير نشط': 0 };

            clinics.forEach(function(clinic) {
                // تجميع التخصصات
                specialtyCount[clinic.specialty] = (specialtyCount[clinic.specialty] || 0) + 1;
                // تجميع الحالات
                statusCount[clinic.status] = (statusCount[clinic.status] || 0) + 1;
            });

            var content = `
                <h3>📊 تقرير العيادات المسجلة</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #27ae60; color: white; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold;">${clinics.length}</div>
                        <div>إجمالي العيادات</div>
                    </div>
                    <div style="background: #3498db; color: white; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold;">${statusCount['نشط']}</div>
                        <div>العيادات النشطة</div>
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold;">${statusCount['غير نشط'] || 0}</div>
                        <div>العيادات غير النشطة</div>
                    </div>
                </div>
                <h4>📋 توزيع التخصصات:</h4>
                <ul style="margin: 10px 0;">
            `;

            for (var specialty in specialtyCount) {
                content += `<li>${specialty}: ${specialtyCount[specialty]} عيادة</li>`;
            }

            content += '</ul>';

            showModal('clinicReportsModal', 'تقارير العيادات', content);
        }

        // دوال الإدارة المالية
        function addIncome() {
            showModal('addIncomeModal', 'إضافة إيراد جديد', `
                <form id="addIncomeForm" onsubmit="saveIncome(event)">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مصدر الإيراد *</label>
                            <select id="incomeSource" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر مصدر الإيراد</option>
                                <option value="رسوم اشتراك عيادة">رسوم اشتراك عيادة</option>
                                <option value="رسوم إضافية">رسوم إضافية</option>
                                <option value="رسوم دعم فني">رسوم دعم فني</option>
                                <option value="رسوم تدريب">رسوم تدريب</option>
                                <option value="إيرادات إعلانات">إيرادات إعلانات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المبلغ (د.ع) *</label>
                            <input type="number" id="incomeAmount" required min="0" step="0.01" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">التاريخ *</label>
                            <input type="date" id="incomeDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">العيادة المرتبطة</label>
                            <select id="incomeClinic" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر العيادة (اختياري)</option>
                            </select>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الوصف</label>
                            <textarea id="incomeDescription" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="تفاصيل إضافية عن الإيراد"></textarea>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ حفظ الإيراد</button>
                        <button type="button" onclick="closeModal('addIncomeModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);

            // تحميل العيادات في القائمة المنسدلة
            loadClinicsInSelect('incomeClinic');
            // تعيين التاريخ الحالي
            document.getElementById('incomeDate').value = new Date().toISOString().split('T')[0];
        }

        function saveIncome(event) {
            event.preventDefault();

            var incomeData = {
                id: 'income_' + Date.now(),
                source: document.getElementById('incomeSource').value,
                amount: parseFloat(document.getElementById('incomeAmount').value),
                date: document.getElementById('incomeDate').value,
                clinic: document.getElementById('incomeClinic').value,
                description: document.getElementById('incomeDescription').value,
                createdAt: new Date().toISOString()
            };

            var incomes = JSON.parse(localStorage.getItem('adminIncomes') || '[]');
            incomes.push(incomeData);
            localStorage.setItem('adminIncomes', JSON.stringify(incomes));

            alert('✅ تم إضافة الإيراد بنجاح!\n\nالمصدر: ' + incomeData.source + '\nالمبلغ: ' + incomeData.amount.toLocaleString() + ' د.ع');

            closeModal('addIncomeModal');
            updateStats();
        }

        function addExpense() {
            showModal('addExpenseModal', 'إضافة مصروف جديد', `
                <form id="addExpenseForm" onsubmit="saveExpense(event)">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع المصروف *</label>
                            <select id="expenseType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر نوع المصروف</option>
                                <option value="تطوير النظام">تطوير النظام</option>
                                <option value="استضافة وخوادم">استضافة وخوادم</option>
                                <option value="دعم فني">دعم فني</option>
                                <option value="تسويق وإعلان">تسويق وإعلان</option>
                                <option value="رواتب موظفين">رواتب موظفين</option>
                                <option value="مصاريف إدارية">مصاريف إدارية</option>
                                <option value="صيانة">صيانة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المبلغ (د.ع) *</label>
                            <input type="number" id="expenseAmount" required min="0" step="0.01" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">التاريخ *</label>
                            <input type="date" id="expenseDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">طريقة الدفع</label>
                            <select id="paymentMethod" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="نقداً">نقداً</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="شيك">شيك</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الوصف</label>
                            <textarea id="expenseDescription" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="تفاصيل إضافية عن المصروف"></textarea>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #e74c3c; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ حفظ المصروف</button>
                        <button type="button" onclick="closeModal('addExpenseModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);

            // تعيين التاريخ الحالي
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
        }

        function saveExpense(event) {
            event.preventDefault();

            var expenseData = {
                id: 'expense_' + Date.now(),
                type: document.getElementById('expenseType').value,
                amount: parseFloat(document.getElementById('expenseAmount').value),
                date: document.getElementById('expenseDate').value,
                paymentMethod: document.getElementById('paymentMethod').value,
                description: document.getElementById('expenseDescription').value,
                createdAt: new Date().toISOString()
            };

            var expenses = JSON.parse(localStorage.getItem('adminExpenses') || '[]');
            expenses.push(expenseData);
            localStorage.setItem('adminExpenses', JSON.stringify(expenses));

            alert('✅ تم إضافة المصروف بنجاح!\n\nالنوع: ' + expenseData.type + '\nالمبلغ: ' + expenseData.amount.toLocaleString() + ' د.ع');

            closeModal('addExpenseModal');
            updateStats();
        }

        function financialOverview() {
            var incomes = JSON.parse(localStorage.getItem('adminIncomes') || '[]');
            var expenses = JSON.parse(localStorage.getItem('adminExpenses') || '[]');

            var totalIncome = incomes.reduce(function(sum, income) { return sum + (income.amount || 0); }, 0);
            var totalExpenses = expenses.reduce(function(sum, expense) { return sum + (expense.amount || 0); }, 0);
            var netProfit = totalIncome - totalExpenses;

            var content = `
                <h3>💰 النظرة العامة المالية</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #27ae60; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;">💰 إجمالي الإيرادات</h4>
                        <div style="font-size: 24px; font-weight: bold;">${totalIncome.toLocaleString()} د.ع</div>
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;">💸 إجمالي المصروفات</h4>
                        <div style="font-size: 24px; font-weight: bold;">${totalExpenses.toLocaleString()} د.ع</div>
                    </div>
                    <div style="background: ${netProfit >= 0 ? '#3498db' : '#e67e22'}; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;">📊 صافي الربح</h4>
                        <div style="font-size: 24px; font-weight: bold;">${netProfit.toLocaleString()} د.ع</div>
                    </div>
                </div>
            `;

            showModal('financialOverviewModal', 'النظرة العامة المالية', content);
        }

        function financialReports() {
            // محاكاة بيانات مالية للعيادات
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            if (clinics.length === 0) {
                alert('⚠️ لا توجد عيادات مسجلة لإنشاء التقارير المالية');
                return;
            }

            // إنشاء بيانات مالية تجريبية لكل عيادة
            var financialData = generateFinancialData(clinics);

            var content = `
                <div style="max-height: 600px; overflow-y: auto;">
                    <!-- ملخص مالي عام -->
                    <div style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 25px;">
                        <h4 style="margin: 0 0 20px 0; text-align: center;">💰 الملخص المالي العام</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center;">
                                <div style="font-size: 28px; font-weight: bold; color: #2ecc71; direction: ltr;">${formatCurrencyLTR(financialData.totalRevenue)}</div>
                                <div style="font-size: 14px; opacity: 0.8;">إجمالي الإيرادات</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 28px; font-weight: bold; color: #e74c3c; direction: ltr;">${formatCurrencyLTR(financialData.totalExpenses)}</div>
                                <div style="font-size: 14px; opacity: 0.8;">إجمالي المصروفات</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 28px; font-weight: bold; color: #f39c12; direction: ltr;">${formatCurrencyLTR(financialData.netProfit)}</div>
                                <div style="font-size: 14px; opacity: 0.8;">صافي الربح</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 28px; font-weight: bold; color: #3498db; direction: ltr;">${formatNumberLTR(clinics.length)}</div>
                                <div style="font-size: 14px; opacity: 0.8;">عدد العيادات</div>
                            </div>
                        </div>
                    </div>

                    <!-- تقارير العيادات الفردية -->
                    <div style="margin-bottom: 25px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">📊 تقارير العيادات الفردية</h5>
                        <div style="display: grid; gap: 15px;">
            `;

            financialData.clinicReports.forEach(function(report) {
                var profitColor = report.netProfit >= 0 ? '#27ae60' : '#e74c3c';
                var profitIcon = report.netProfit >= 0 ? '📈' : '📉';

                content += `
                    <div style="border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                            <div>
                                <h6 style="margin: 0; color: #2c3e50; font-size: 16px;">${report.clinicName}</h6>
                                <small style="color: #666;">د. ${report.doctorName} - ${report.specialty}</small>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 18px; font-weight: bold; color: ${profitColor}; direction: ltr;">
                                    ${profitIcon} ${formatCurrencyLTR(report.netProfit)}
                                </div>
                                <small style="color: #666;">صافي الربح</small>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div style="text-align: center; background: white; padding: 10px; border-radius: 8px;">
                                <div style="font-size: 16px; font-weight: bold; color: #27ae60; direction: ltr;">${formatCurrencyLTR(report.revenue)}</div>
                                <div style="font-size: 12px; color: #666;">الإيرادات</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 10px; border-radius: 8px;">
                                <div style="font-size: 16px; font-weight: bold; color: #e74c3c; direction: ltr;">${formatCurrencyLTR(report.expenses)}</div>
                                <div style="font-size: 12px; color: #666;">المصروفات</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 10px; border-radius: 8px;">
                                <div style="font-size: 16px; font-weight: bold; color: #3498db; direction: ltr;">${formatNumberLTR(report.patients)}</div>
                                <div style="font-size: 12px; color: #666;">عدد المرضى</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 10px; border-radius: 8px;">
                                <div style="font-size: 16px; font-weight: bold; color: #9b59b6; direction: ltr;">${formatNumberLTR(report.appointments)}</div>
                                <div style="font-size: 12px; color: #666;">المواعيد</div>
                            </div>
                        </div>

                        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: 12px; color: #666; direction: ltr;">
                                    📅 آخر تحديث: ${formatDateLTR(new Date())}
                                </div>
                                <div style="font-size: 12px; color: #666; direction: ltr;">
                                    💳 معدل الدفع: ${formatNumberLTR(report.paymentRate)}%
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            content += `
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #3498db;">
                        <h6 style="color: #2c3e50; margin: 0 0 15px 0;">📈 إحصائيات إضافية</h6>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; font-size: 14px;">
                            <div>
                                <strong>متوسط الإيرادات لكل عيادة:</strong><br>
                                <span style="color: #27ae60; font-weight: bold; direction: ltr;">${formatCurrencyLTR(Math.round(financialData.totalRevenue / clinics.length))}</span>
                            </div>
                            <div>
                                <strong>أعلى عيادة ربحاً:</strong><br>
                                <span style="color: #2c3e50; font-weight: bold;">${financialData.topClinic.name}</span>
                            </div>
                            <div>
                                <strong>إجمالي المرضى:</strong><br>
                                <span style="color: #3498db; font-weight: bold; direction: ltr;">${formatNumberLTR(financialData.totalPatients)} مريض</span>
                            </div>
                            <div>
                                <strong>إجمالي المواعيد:</strong><br>
                                <span style="color: #9b59b6; font-weight: bold; direction: ltr;">${formatNumberLTR(financialData.totalAppointments)} موعد</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div style="text-align: center; margin-top: 25px; padding-top: 20px; border-top: 2px solid #dee2e6;">
                        <button onclick="exportFinancialReport()" style="background: #27ae60; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 5px; font-weight: bold;">📊 تصدير التقرير</button>
                        <button onclick="printFinancialReport()" style="background: #3498db; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 5px; font-weight: bold;">🖨️ طباعة</button>
                        <button onclick="refreshFinancialData()" style="background: #f39c12; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 5px; font-weight: bold;">🔄 تحديث البيانات</button>
                    </div>
                </div>
            `;

            showModal('financialReportsModal', '📈 التقارير المالية الشاملة', content);
        }

        function generateFinancialData(clinics) {
            var totalRevenue = 0;
            var totalExpenses = 0;
            var totalPatients = 0;
            var totalAppointments = 0;
            var clinicReports = [];
            var topClinic = { name: '', profit: 0 };

            clinics.forEach(function(clinic) {
                // إنشاء بيانات عشوائية واقعية لكل عيادة
                var baseRevenue = getRandomBetween(500000, 2000000); // 500k - 2M IQD
                var baseExpenses = Math.round(baseRevenue * getRandomBetween(0.4, 0.7)); // 40-70% من الإيرادات
                var patients = getRandomBetween(50, 300);
                var appointments = Math.round(patients * getRandomBetween(1.2, 2.5));
                var paymentRate = getRandomBetween(75, 95);

                var netProfit = baseRevenue - baseExpenses;

                var report = {
                    clinicName: clinic.name,
                    doctorName: clinic.doctorName,
                    specialty: clinic.specialty,
                    revenue: baseRevenue,
                    expenses: baseExpenses,
                    netProfit: netProfit,
                    patients: patients,
                    appointments: appointments,
                    paymentRate: paymentRate
                };

                clinicReports.push(report);

                totalRevenue += baseRevenue;
                totalExpenses += baseExpenses;
                totalPatients += patients;
                totalAppointments += appointments;

                if (netProfit > topClinic.profit) {
                    topClinic = { name: clinic.name, profit: netProfit };
                }
            });

            return {
                totalRevenue: totalRevenue,
                totalExpenses: totalExpenses,
                netProfit: totalRevenue - totalExpenses,
                totalPatients: totalPatients,
                totalAppointments: totalAppointments,
                clinicReports: clinicReports,
                topClinic: topClinic
            };
        }

        function getRandomBetween(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }

        function exportFinancialReport() {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var financialData = generateFinancialData(clinics);

            var csvContent = "اسم العيادة,اسم الطبيب,التخصص,الإيرادات,المصروفات,صافي الربح,عدد المرضى,عدد المواعيد,معدل الدفع\n";

            financialData.clinicReports.forEach(function(report) {
                csvContent += `"${report.clinicName}","${report.doctorName}","${report.specialty}","${formatNumberLTR(report.revenue)}","${formatNumberLTR(report.expenses)}","${formatNumberLTR(report.netProfit)}","${formatNumberLTR(report.patients)}","${formatNumberLTR(report.appointments)}","${formatNumberLTR(report.paymentRate)}%"\n`;
            });

            // إضافة الملخص العام
            csvContent += `\n"الملخص العام","","","${formatNumberLTR(financialData.totalRevenue)}","${formatNumberLTR(financialData.totalExpenses)}","${formatNumberLTR(financialData.netProfit)}","${formatNumberLTR(financialData.totalPatients)}","${formatNumberLTR(financialData.totalAppointments)}",""\n`;

            var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            var link = document.createElement("a");
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "التقرير_المالي_" + formatDateLTR(new Date()).replace(/\//g, '-') + ".csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير التقرير المالي بنجاح!\nيمكنك العثور على الملف في مجلد التحميلات.');
        }

        function printFinancialReport() {
            var printWindow = window.open('', '_blank');
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var financialData = generateFinancialData(clinics);

            var printContent = `
                <html dir="rtl">
                <head>
                    <title>التقرير المالي الشامل</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .summary { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
                        .clinic-report { border: 1px solid #dee2e6; margin-bottom: 15px; padding: 15px; border-radius: 8px; }
                        .financial-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 10px 0; }
                        .financial-item { text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📈 التقرير المالي الشامل</h1>
                        <p>تاريخ التقرير: ${formatDateLTR(new Date())}</p>
                    </div>

                    <div class="summary">
                        <h2>💰 الملخص المالي العام</h2>
                        <div class="financial-grid">
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(financialData.totalRevenue)}</strong><br>
                                إجمالي الإيرادات
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(financialData.totalExpenses)}</strong><br>
                                إجمالي المصروفات
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(financialData.netProfit)}</strong><br>
                                صافي الربح
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatNumberLTR(clinics.length)}</strong><br>
                                عدد العيادات
                            </div>
                        </div>
                    </div>

                    <h2>📊 تقارير العيادات الفردية</h2>
            `;

            financialData.clinicReports.forEach(function(report) {
                printContent += `
                    <div class="clinic-report">
                        <h3>${report.clinicName}</h3>
                        <p><strong>الطبيب:</strong> د. ${report.doctorName} | <strong>التخصص:</strong> ${report.specialty}</p>
                        <div class="financial-grid">
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(report.revenue)}</strong><br>الإيرادات
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(report.expenses)}</strong><br>المصروفات
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatCurrencyLTR(report.netProfit)}</strong><br>صافي الربح
                            </div>
                            <div class="financial-item">
                                <strong style="direction: ltr;">${formatNumberLTR(report.patients)}</strong><br>عدد المرضى
                            </div>
                        </div>
                    </div>
                `;
            });

            printContent += `
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function refreshFinancialData() {
            alert('🔄 تم تحديث البيانات المالية!\nسيتم إعادة تحميل التقرير بأحدث البيانات.');
            financialReports(); // إعادة تحميل التقرير
        }

        // دوال إعدادات البرنامج
        function configureProgramSettings() {
            var currentSettings = JSON.parse(localStorage.getItem('programSettings') || '{}');

            showModal('programSettingsModal', 'تكوين إعدادات البرنامج', `
                <form id="programSettingsForm" onsubmit="saveProgramSettings(event)">
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">اسم البرنامج *</label>
                        <input type="text" id="programName" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" value="${currentSettings.name || ''}" placeholder="مثال: نظام إدارة العيادات الطبية">
                        <small style="color: #666; font-size: 14px;">سيظهر هذا الاسم في أعلى جميع صفحات العيادات</small>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">شعار البرنامج</label>
                        <div style="border: 2px dashed #e9ecef; border-radius: 8px; padding: 20px; text-align: center; margin-bottom: 15px;">
                            <input type="file" id="programLogo" accept="image/*" style="display: none;" onchange="previewLogo(this)">
                            <div id="logoPreview" style="margin-bottom: 15px;">
                                ${currentSettings.logo ? `<img src="${currentSettings.logo}" style="max-width: 200px; max-height: 100px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">` : '<div style="color: #666; font-size: 16px;">📷 لا يوجد شعار محدد</div>'}
                            </div>
                            <button type="button" onclick="document.getElementById('programLogo').click()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">📁 اختيار شعار</button>
                            ${currentSettings.logo ? '<button type="button" onclick="removeLogo()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">🗑️ حذف الشعار</button>' : ''}
                        </div>
                        <small style="color: #666; font-size: 14px;">يُفضل استخدام صور بصيغة PNG أو JPG بحجم لا يزيد عن 2 ميجابايت</small>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">وصف البرنامج</label>
                        <textarea id="programDescription" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; resize: vertical;" placeholder="وصف مختصر عن البرنامج...">${currentSettings.description || ''}</textarea>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">إصدار البرنامج</label>
                        <input type="text" id="programVersion" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" value="${currentSettings.version || '1.0.0'}" placeholder="مثال: 1.0.0">
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🎨 معاينة في لوحة التحكم</h4>
                        <div id="dashboardPreview" style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <div id="previewContent">
                                <h3 style="margin: 0; font-size: 24px;">🏠 لوحة التحكم الرئيسية</h3>
                            </div>
                        </div>
                        <small style="color: #666; font-size: 12px; display: block; text-align: center; margin-top: 10px;">📝 ملاحظة: ستظهر إعدادات البرنامج في الشريط الجانبي فقط</small>
                    </div>

                    <div style="text-align: center;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 5px;">✅ حفظ الإعدادات</button>
                        <button type="button" onclick="closeModal('programSettingsModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);
        }

        function previewLogo(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var logoPreview = document.getElementById('logoPreview');
                    var dashboardPreview = document.getElementById('previewContent');

                    logoPreview.innerHTML = '<img src="' + e.target.result + '" style="max-width: 200px; max-height: 100px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">';

                    // معاينة لوحة التحكم - عنوان ثابت فقط
                    dashboardPreview.innerHTML = `
                        <h3 style="margin: 0; font-size: 24px;">🏠 لوحة التحكم الرئيسية</h3>
                    `;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function removeLogo() {
            document.getElementById('logoPreview').innerHTML = '<div style="color: #666; font-size: 16px;">📷 لا يوجد شعار محدد</div>';
            document.getElementById('programLogo').value = '';
            updateDashboardPreview();
        }

        function updateDashboardPreview() {
            // لوحة التحكم ستعرض العنوان الثابت فقط
            var dashboardPreview = document.getElementById('previewContent');

            dashboardPreview.innerHTML = `
                <h3 style="margin: 0; font-size: 24px;">🏠 لوحة التحكم الرئيسية</h3>
            `;
        }

        // تحديث المعاينة عند تغيير النص
        document.addEventListener('input', function(e) {
            if (e.target.id === 'programName' || e.target.id === 'programDescription') {
                updateDashboardPreview();
            }
        });

        function saveProgramSettings(event) {
            event.preventDefault();

            var logoFile = document.getElementById('programLogo').files[0];
            var programName = document.getElementById('programName').value;
            var programDescription = document.getElementById('programDescription').value;
            var programVersion = document.getElementById('programVersion').value;

            var settings = {
                name: programName,
                description: programDescription,
                version: programVersion,
                updatedAt: new Date().toISOString()
            };

            // إذا تم اختيار شعار جديد
            if (logoFile) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    settings.logo = e.target.result;
                    localStorage.setItem('programSettings', JSON.stringify(settings));
                    alert('✅ تم حفظ إعدادات البرنامج بنجاح!\n\nسيتم تطبيق التغييرات على جميع العيادات.');
                    closeModal('programSettingsModal');
                };
                reader.readAsDataURL(logoFile);
            } else {
                // الاحتفاظ بالشعار الحالي إذا لم يتم تغييره
                var currentSettings = JSON.parse(localStorage.getItem('programSettings') || '{}');
                if (currentSettings.logo) {
                    settings.logo = currentSettings.logo;
                }
                localStorage.setItem('programSettings', JSON.stringify(settings));
                alert('✅ تم حفظ إعدادات البرنامج بنجاح!\n\nسيتم تطبيق التغييرات على جميع العيادات.');
                closeModal('programSettingsModal');
            }
        }

        function viewProgramSettings() {
            var settings = JSON.parse(localStorage.getItem('programSettings') || '{}');

            if (Object.keys(settings).length === 0) {
                alert('⚠️ لم يتم تكوين إعدادات البرنامج بعد\nيرجى الذهاب إلى "تكوين إعدادات البرنامج" أولاً.');
                return;
            }

            showModal('viewProgramSettingsModal', 'الإعدادات الحالية للبرنامج', `
                <div style="text-align: center;">
                    ${settings.logo ? `<img src="${settings.logo}" style="max-width: 300px; max-height: 150px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); margin-bottom: 20px;">` : '<div style="color: #666; font-size: 18px; margin-bottom: 20px;">📷 لا يوجد شعار محدد</div>'}
                </div>

                <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 20px;">
                    <div style="margin-bottom: 15px;">
                        <strong style="color: #2c3e50;">اسم البرنامج:</strong>
                        <p style="margin: 5px 0; font-size: 18px; color: #34495e;">${settings.name || 'غير محدد'}</p>
                    </div>

                    ${settings.description ? `
                        <div style="margin-bottom: 15px;">
                            <strong style="color: #2c3e50;">وصف البرنامج:</strong>
                            <p style="margin: 5px 0; color: #34495e;">${settings.description}</p>
                        </div>
                    ` : ''}

                    <div style="margin-bottom: 15px;">
                        <strong style="color: #2c3e50;">إصدار البرنامج:</strong>
                        <p style="margin: 5px 0; color: #34495e;">${settings.version || '1.0.0'}</p>
                    </div>

                    <div style="margin-bottom: 0;">
                        <strong style="color: #2c3e50;">آخر تحديث:</strong>
                        <p style="margin: 5px 0; color: #34495e;">${settings.updatedAt ? formatDateLTR(new Date(settings.updatedAt)) + ' - ' + formatTimeLTR(new Date(settings.updatedAt)) : 'غير محدد'}</p>
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
                    <h4 style="margin-bottom: 15px; color: white;">🖥️ معاينة في لوحة التحكم</h4>
                    <h3 style="margin: 0; font-size: 24px;">🏠 لوحة التحكم الرئيسية</h3>
                    <small style="opacity: 0.7; font-size: 12px; display: block; margin-top: 10px;">📝 إعدادات البرنامج ستظهر في الشريط الجانبي فقط</small>
                </div>

                <div style="text-align: center;">
                    <button onclick="configureProgramSettings(); closeModal('viewProgramSettingsModal');" style="background: #f39c12; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 5px;">✏️ تعديل الإعدادات</button>
                    <button onclick="closeModal('viewProgramSettingsModal')" style="background: #6c757d; color: white; padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 5px;">❌ إغلاق</button>
                </div>
            `);
        }

        function resetProgramSettings() {
            if (confirm('⚠️ هل أنت متأكد من إعادة تعيين جميع إعدادات البرنامج؟\nسيتم حذف الاسم والشعار والوصف، وستحتاج لإعادة تكوينها من جديد.')) {
                localStorage.removeItem('programSettings');
                alert('✅ تم إعادة تعيين إعدادات البرنامج بنجاح\nيمكنك الآن تكوين إعدادات جديدة.');
            }
        }

        // دوال إدارة أمان النظام
        function createCredentials() {
            showModal('createCredentialsModal', 'إنشاء أوراق اعتماد جديدة', `
                <form id="createCredentialsForm" onsubmit="saveCredentials(event)">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستخدم *</label>
                            <input type="text" id="credentialUsername" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="مثال: admin_clinic1">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">كلمة المرور *</label>
                            <div style="position: relative;">
                                <input type="password" id="credentialPassword" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="كلمة مرور قوية">
                                <button type="button" onclick="togglePasswordVisibility('credentialPassword')" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">👁️</button>
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">العيادة المرتبطة *</label>
                            <select id="credentialClinic" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="">اختر العيادة</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مستوى الصلاحية</label>
                            <select id="credentialRole" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="admin">مدير العيادة (كامل الصلاحيات)</option>
                                <option value="settings">إعدادات فقط</option>
                                <option value="readonly">قراءة فقط</option>
                            </select>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات</label>
                            <textarea id="credentialNotes" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="ملاحظات إضافية حول هذه الأوراق"></textarea>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <button type="button" onclick="generateRandomPassword()" style="background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">🎲 توليد كلمة مرور عشوائية</button>
                            <button type="button" onclick="checkPasswordStrength()" style="background: #f39c12; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">🔍 فحص قوة كلمة المرور</button>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ إنشاء أوراق الاعتماد</button>
                        <button type="button" onclick="closeModal('createCredentialsModal')" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);

            // تحميل العيادات في القائمة المنسدلة
            loadClinicsInSelect('credentialClinic');
        }

        function generateRandomPassword() {
            var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            var password = '';
            for (var i = 0; i < 12; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('credentialPassword').value = password;
            alert('✅ تم توليد كلمة مرور قوية!\n\nكلمة المرور: ' + password + '\n\n⚠️ تأكد من حفظها في مكان آمن');
        }

        function checkPasswordStrength() {
            var password = document.getElementById('credentialPassword').value;
            if (!password) {
                alert('⚠️ يرجى إدخال كلمة مرور أولاً');
                return;
            }

            var strength = 0;
            var feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('يجب أن تكون 8 أحرف على الأقل');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('يجب أن تحتوي على أحرف صغيرة');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('يجب أن تحتوي على أحرف كبيرة');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('يجب أن تحتوي على أرقام');

            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('يجب أن تحتوي على رموز خاصة');

            var strengthText = ['ضعيفة جداً', 'ضعيفة', 'متوسطة', 'قوية', 'قوية جداً'][strength];
            var strengthColor = ['#e74c3c', '#e67e22', '#f39c12', '#27ae60', '#2ecc71'][strength];

            var message = '🔍 تقييم قوة كلمة المرور:\n\n';
            message += '📊 القوة: ' + strengthText + '\n';
            message += '⭐ النقاط: ' + strength + '/5\n\n';

            if (feedback.length > 0) {
                message += '💡 اقتراحات للتحسين:\n';
                feedback.forEach(function(item) {
                    message += '• ' + item + '\n';
                });
            } else {
                message += '✅ كلمة مرور ممتازة!';
            }

            alert(message);
        }

        function togglePasswordVisibility(inputId) {
            var input = document.getElementById(inputId);
            if (input.type === 'password') {
                input.type = 'text';
            } else {
                input.type = 'password';
            }
        }

        function saveCredentials(event) {
            event.preventDefault();

            var credentialData = {
                id: 'cred_' + Date.now(),
                username: document.getElementById('credentialUsername').value,
                password: document.getElementById('credentialPassword').value,
                clinicId: document.getElementById('credentialClinic').value,
                role: document.getElementById('credentialRole').value,
                notes: document.getElementById('credentialNotes').value,
                createdAt: new Date().toISOString(),
                lastUsed: null,
                isActive: true
            };

            // التحقق من عدم تكرار اسم المستخدم
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var existingUser = credentials.find(function(cred) { return cred.username === credentialData.username; });

            if (existingUser) {
                alert('⚠️ اسم المستخدم موجود مسبقاً!\nيرجى اختيار اسم مستخدم آخر.');
                return;
            }

            credentials.push(credentialData);
            localStorage.setItem('systemCredentials', JSON.stringify(credentials));

            // الحصول على اسم العيادة
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinic = clinics.find(function(c) { return c.id === credentialData.clinicId; });
            var clinicName = clinic ? clinic.name : 'غير محدد';

            alert('✅ تم إنشاء أوراق الاعتماد بنجاح!\n\n' +
                'اسم المستخدم: ' + credentialData.username + '\n' +
                'العيادة: ' + clinicName + '\n' +
                'الصلاحية: ' + credentialData.role + '\n\n' +
                '⚠️ تأكد من إرسال هذه المعلومات للمستخدم المخول بشكل آمن');

            closeModal('createCredentialsModal');
        }

        function viewCredentials() {
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            if (credentials.length === 0) {
                showModal('viewCredentialsModal', 'أوراق الاعتماد', '<p style="text-align: center; color: #666; padding: 40px;">لا توجد أوراق اعتماد مُنشأة</p>');
                return;
            }

            var content = '<div style="max-height: 500px; overflow-y: auto;">';
            credentials.forEach(function(cred, index) {
                var clinic = clinics.find(function(c) { return c.id === cred.clinicId; });
                var clinicName = clinic ? clinic.name : 'عيادة محذوفة';
                var statusColor = cred.isActive ? '#27ae60' : '#e74c3c';
                var statusText = cred.isActive ? 'نشط' : 'معطل';
                var roleText = {
                    'admin': 'مدير العيادة',
                    'settings': 'إعدادات فقط',
                    'readonly': 'قراءة فقط'
                }[cred.role] || cred.role;

                content += `
                    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                            <div>
                                <h4 style="color: #2c3e50; margin: 0 0 5px 0;">👤 ${cred.username}</h4>
                                <p style="color: #3498db; margin: 0 0 5px 0;">🏥 ${clinicName}</p>
                                <p style="color: #9b59b6; margin: 0 0 5px 0;">🔑 ${roleText}</p>
                                ${cred.notes ? `<p style="color: #666; margin: 0; font-size: 14px;">📝 ${cred.notes}</p>` : ''}
                            </div>
                            <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${statusText}</span>
                        </div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                            <p style="margin: 2px 0;">📅 تاريخ الإنشاء: ${formatDateLTR(new Date(cred.createdAt))}</p>
                            ${cred.lastUsed ? `<p style="margin: 2px 0;">🕒 آخر استخدام: ${formatDateLTR(new Date(cred.lastUsed))}</p>` : '<p style="margin: 2px 0;">🕒 لم يتم الاستخدام بعد</p>'}
                        </div>
                        <div style="text-align: left;">
                            <button onclick="showCredentialPassword('${cred.id}')" style="background: #3498db; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">👁️ عرض كلمة المرور</button>
                            <button onclick="toggleCredentialStatus('${cred.id}')" style="background: ${cred.isActive ? '#f39c12' : '#27ae60'}; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">${cred.isActive ? '⏸️ تعطيل' : '▶️ تفعيل'}</button>
                            <button onclick="deleteCredential('${cred.id}')" style="background: #e74c3c; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin: 2px; font-size: 12px;">🗑️ حذف</button>
                        </div>
                    </div>
                `;
            });
            content += '</div>';

            showModal('viewCredentialsModal', 'أوراق الاعتماد (' + credentials.length + ')', content);
        }

        function showCredentialPassword(credId) {
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var cred = credentials.find(function(c) { return c.id === credId; });

            if (cred) {
                if (confirm('⚠️ هل أنت متأكد من عرض كلمة المرور؟\nتأكد من عدم وجود أشخاص غير مخولين حولك.')) {
                    alert('🔑 كلمة المرور لـ ' + cred.username + ':\n\n' + cred.password + '\n\n⚠️ لا تشارك هذه المعلومات مع أي شخص غير مخول');
                }
            }
        }

        function toggleCredentialStatus(credId) {
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var credIndex = credentials.findIndex(function(c) { return c.id === credId; });

            if (credIndex !== -1) {
                credentials[credIndex].isActive = !credentials[credIndex].isActive;
                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                alert('✅ تم ' + (credentials[credIndex].isActive ? 'تفعيل' : 'تعطيل') + ' أوراق الاعتماد بنجاح');
                viewCredentials(); // إعادة تحميل القائمة
            }
        }

        function deleteCredential(credId) {
            if (confirm('⚠️ هل أنت متأكد من حذف أوراق الاعتماد هذه؟\nلن يتمكن المستخدم من الوصول لإعدادات العيادة بعد الحذف.')) {
                var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
                credentials = credentials.filter(function(c) { return c.id !== credId; });
                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                alert('✅ تم حذف أوراق الاعتماد بنجاح');
                viewCredentials(); // إعادة تحميل القائمة
            }
        }

        function manageCredentialsByClinic() {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');

            if (clinics.length === 0) {
                alert('⚠️ لا توجد عيادات مسجلة\nيجب تسجيل عيادات أولاً لإدارة أوراق الاعتماد');
                return;
            }

            var clinicOptions = '';
            clinics.forEach(function(clinic) {
                clinicOptions += '<option value="' + clinic.id + '">' + clinic.name + ' - د. ' + clinic.doctorName + '</option>';
            });

            showModal('manageCredentialsModal', 'إدارة أوراق الاعتماد حسب العيادة', `
                <div style="margin-bottom: 25px;">
                    <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">اختر العيادة:</label>
                    <select id="selectedClinic" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;" onchange="loadClinicCredentials()">
                        <option value="">-- اختر العيادة --</option>
                        ${clinicOptions}
                    </select>
                </div>

                <div id="clinicCredentialsContainer" style="display: none;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0 0 15px 0;">📋 أوراق الاعتماد للعيادة المختارة:</h4>
                        <div id="clinicCredentialsList"></div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="resetClinicPassword()" class="btn btn-warning" style="margin: 5px;">🔄 إعادة تعيين كلمة المرور</button>
                        <button onclick="editClinicCredentials()" class="btn" style="margin: 5px;">✏️ تعديل أوراق الاعتماد</button>
                        <button onclick="deleteClinicCredentials()" class="btn btn-danger" style="margin: 5px;">🗑️ حذف أوراق الاعتماد</button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <button onclick="closeModal('manageCredentialsModal')" class="btn" style="background: #6c757d;">❌ إغلاق</button>
                </div>
            `);
        }

        function loadClinicCredentials() {
            var selectedClinicId = document.getElementById('selectedClinic').value;
            var container = document.getElementById('clinicCredentialsContainer');
            var credentialsList = document.getElementById('clinicCredentialsList');

            if (!selectedClinicId) {
                container.style.display = 'none';
                return;
            }

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var clinicCredentials = credentials.filter(function(cred) { return cred.clinicId === selectedClinicId; });

            if (clinicCredentials.length === 0) {
                credentialsList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">لا توجد أوراق اعتماد لهذه العيادة</p>';
            } else {
                var html = '';
                clinicCredentials.forEach(function(cred) {
                    var statusColor = cred.isActive ? '#27ae60' : '#e74c3c';
                    var statusText = cred.isActive ? 'نشط' : 'معطل';
                    var roleText = {
                        'admin': 'مدير العيادة',
                        'settings': 'إعدادات فقط',
                        'readonly': 'قراءة فقط'
                    }[cred.role] || cred.role;

                    html += `
                        <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h5 style="margin: 0 0 5px 0; color: #2c3e50;">👤 ${cred.username}</h5>
                                    <p style="margin: 0 0 5px 0; color: #9b59b6; font-size: 14px;">🔑 ${roleText}</p>
                                    <p style="margin: 0; color: #666; font-size: 12px;">📅 ${formatDateLTR(new Date(cred.createdAt))}</p>
                                </div>
                                <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${statusText}</span>
                            </div>
                        </div>
                    `;
                });
                credentialsList.innerHTML = html;
            }

            container.style.display = 'block';
        }

        function resetClinicPassword() {
            var selectedClinicId = document.getElementById('selectedClinic').value;
            if (!selectedClinicId) {
                alert('⚠️ يرجى اختيار عيادة أولاً');
                return;
            }

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var clinicCredentials = credentials.filter(function(cred) { return cred.clinicId === selectedClinicId; });

            if (clinicCredentials.length === 0) {
                alert('⚠️ لا توجد أوراق اعتماد لهذه العيادة');
                return;
            }

            if (confirm('⚠️ هل أنت متأكد من إعادة تعيين كلمات المرور لهذه العيادة؟\nسيتم توليد كلمات مرور جديدة لجميع مستخدمي هذه العيادة.')) {
                var resetList = '';

                credentials.forEach(function(cred) {
                    if (cred.clinicId === selectedClinicId) {
                        var newPassword = generateRandomPasswordString();
                        cred.password = newPassword;
                        resetList += cred.username + ': ' + newPassword + '\n';
                    }
                });

                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
                var clinic = clinics.find(function(c) { return c.id === selectedClinicId; });
                var clinicName = clinic ? clinic.name : 'العيادة المختارة';

                alert('✅ تم إعادة تعيين كلمات المرور لـ ' + clinicName + '!\n\nكلمات المرور الجديدة:\n\n' + resetList + '\n⚠️ تأكد من إرسال كلمات المرور الجديدة للمستخدمين');

                loadClinicCredentials(); // إعادة تحميل القائمة
            }
        }

        function editClinicCredentials() {
            var selectedClinicId = document.getElementById('selectedClinic').value;
            if (!selectedClinicId) {
                alert('⚠️ يرجى اختيار عيادة أولاً');
                return;
            }

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var clinicCredentials = credentials.filter(function(cred) { return cred.clinicId === selectedClinicId; });

            if (clinicCredentials.length === 0) {
                alert('⚠️ لا توجد أوراق اعتماد لهذه العيادة');
                return;
            }

            // عرض نافذة تعديل أوراق الاعتماد
            var credentialOptions = '';
            clinicCredentials.forEach(function(cred) {
                credentialOptions += '<option value="' + cred.id + '">' + cred.username + ' (' + cred.role + ')</option>';
            });

            showModal('editCredentialModal', 'تعديل أوراق الاعتماد', `
                <form id="editCredentialForm" onsubmit="saveEditedCredential(event)">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر المستخدم:</label>
                        <select id="editCredentialSelect" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" onchange="loadCredentialForEdit()">
                            <option value="">-- اختر المستخدم --</option>
                            ${credentialOptions}
                        </select>
                    </div>

                    <div id="editCredentialFields" style="display: none;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستخدم *</label>
                                <input type="text" id="editUsername" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">كلمة المرور الجديدة</label>
                                <div style="position: relative;">
                                    <input type="password" id="editPassword" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="اتركها فارغة للاحتفاظ بالحالية">
                                    <button type="button" onclick="togglePasswordVisibility('editPassword')" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">👁️</button>
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">مستوى الصلاحية</label>
                                <select id="editRole" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                    <option value="admin">مدير العيادة (كامل الصلاحيات)</option>
                                    <option value="settings">إعدادات فقط</option>
                                    <option value="readonly">قراءة فقط</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">حالة الحساب</label>
                                <select id="editActive" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                    <option value="true">نشط</option>
                                    <option value="false">معطل</option>
                                </select>
                            </div>
                            <div style="grid-column: 1 / -1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات</label>
                                <textarea id="editNotes" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;"></textarea>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ حفظ التعديلات</button>
                            <button type="button" onclick="generateRandomPasswordForEdit()" style="background: #3498db; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">🎲 توليد كلمة مرور جديدة</button>
                        </div>
                    </div>
                </form>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="closeModal('editCredentialModal')" style="background: #6c757d; color: white; padding: 10px 25px; border: none; border-radius: 5px; cursor: pointer;">❌ إغلاق</button>
                </div>
            `);
        }

        function loadCredentialForEdit() {
            var credentialId = document.getElementById('editCredentialSelect').value;
            var fieldsContainer = document.getElementById('editCredentialFields');

            if (!credentialId) {
                fieldsContainer.style.display = 'none';
                return;
            }

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var credential = credentials.find(function(cred) { return cred.id === credentialId; });

            if (credential) {
                document.getElementById('editUsername').value = credential.username;
                document.getElementById('editPassword').value = '';
                document.getElementById('editRole').value = credential.role;
                document.getElementById('editActive').value = credential.isActive.toString();
                document.getElementById('editNotes').value = credential.notes || '';

                fieldsContainer.style.display = 'block';
            }
        }

        function generateRandomPasswordForEdit() {
            var newPassword = generateRandomPasswordString();
            document.getElementById('editPassword').value = newPassword;
            alert('✅ تم توليد كلمة مرور جديدة!\n\nكلمة المرور: ' + newPassword + '\n\n⚠️ تأكد من حفظها في مكان آمن');
        }

        function saveEditedCredential(event) {
            event.preventDefault();

            var credentialId = document.getElementById('editCredentialSelect').value;
            var newUsername = document.getElementById('editUsername').value;
            var newPassword = document.getElementById('editPassword').value;
            var newRole = document.getElementById('editRole').value;
            var newActive = document.getElementById('editActive').value === 'true';
            var newNotes = document.getElementById('editNotes').value;

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var credentialIndex = credentials.findIndex(function(cred) { return cred.id === credentialId; });

            if (credentialIndex !== -1) {
                // التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
                if (credentials[credentialIndex].username !== newUsername) {
                    var existingUser = credentials.find(function(cred) { return cred.username === newUsername && cred.id !== credentialId; });
                    if (existingUser) {
                        alert('⚠️ اسم المستخدم موجود مسبقاً!\nيرجى اختيار اسم مستخدم آخر.');
                        return;
                    }
                }

                // تحديث البيانات
                credentials[credentialIndex].username = newUsername;
                if (newPassword) {
                    credentials[credentialIndex].password = newPassword;
                }
                credentials[credentialIndex].role = newRole;
                credentials[credentialIndex].isActive = newActive;
                credentials[credentialIndex].notes = newNotes;

                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                alert('✅ تم تحديث أوراق الاعتماد بنجاح!');
                closeModal('editCredentialModal');
                loadClinicCredentials(); // إعادة تحميل القائمة
            }
        }

        function deleteClinicCredentials() {
            var selectedClinicId = document.getElementById('selectedClinic').value;
            if (!selectedClinicId) {
                alert('⚠️ يرجى اختيار عيادة أولاً');
                return;
            }

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var clinicCredentials = credentials.filter(function(cred) { return cred.clinicId === selectedClinicId; });

            if (clinicCredentials.length === 0) {
                alert('⚠️ لا توجد أوراق اعتماد لهذه العيادة');
                return;
            }

            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var clinic = clinics.find(function(c) { return c.id === selectedClinicId; });
            var clinicName = clinic ? clinic.name : 'العيادة المختارة';

            if (confirm('⚠️ هل أنت متأكد من حذف جميع أوراق الاعتماد لـ ' + clinicName + '؟\nلن يتمكن أي مستخدم من هذه العيادة من الوصول لإعدادات العيادة بعد الحذف.')) {
                var remainingCredentials = credentials.filter(function(cred) { return cred.clinicId !== selectedClinicId; });
                localStorage.setItem('systemCredentials', JSON.stringify(remainingCredentials));

                alert('✅ تم حذف جميع أوراق الاعتماد لـ ' + clinicName + ' بنجاح');
                loadClinicCredentials(); // إعادة تحميل القائمة
                updateStats(); // تحديث الإحصائيات
            }
        }

        function revokeAllCredentials() {
            if (confirm('⚠️ هل أنت متأكد من إلغاء جميع أوراق الاعتماد لكل العيادات؟\nسيتم حذف جميع أوراق الاعتماد ولن يتمكن أي مستخدم من الوصول لإعدادات العيادات.')) {
                localStorage.removeItem('systemCredentials');
                alert('✅ تم إلغاء جميع أوراق الاعتماد بنجاح');
                updateStats(); // تحديث الإحصائيات
            }
        }

        function generateRandomPasswordString() {
            var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            var password = '';
            for (var i = 0; i < 12; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return password;
        }

        // دوال مساعدة
        function loadClinicsInSelect(selectId) {
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var select = document.getElementById(selectId);

            clinics.forEach(function(clinic) {
                var option = document.createElement('option');
                option.value = clinic.id;
                option.textContent = clinic.name + ' - د. ' + clinic.doctorName;
                select.appendChild(option);
            });
        }

        function showModal(modalId, title, content) {
            // إنشاء المودال إذا لم يكن موجوداً
            var existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            var modal = document.createElement('div');
            modal.id = modalId;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 800px; max-height: 80vh; overflow-y: auto; margin: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #e9ecef; padding-bottom: 15px;">
                        <h2 style="margin: 0; color: #2c3e50; font-family: 'Cairo', sans-serif;">${title}</h2>
                        <button onclick="closeModal('${modalId}')" style="background: #e74c3c; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 18px;">×</button>
                    </div>
                    <div>${content}</div>
                </div>
            `;

            document.body.appendChild(modal);

            // إغلاق المودال عند النقر خارجه
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modalId);
                }
            });
        }

        function closeModal(modalId) {
            var modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        }

        function editCredential(credId) {
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var credential = credentials.find(function(cred) { return cred.id === credId; });

            if (!credential) {
                alert('⚠️ لم يتم العثور على أوراق الاعتماد');
                return;
            }

            showModal('editSingleCredentialModal', 'تعديل أوراق الاعتماد', `
                <form id="editSingleCredentialForm" onsubmit="saveSingleEditedCredential(event, '${credId}')">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستخدم *</label>
                            <input type="text" id="editSingleUsername" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${credential.username}">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">كلمة المرور الجديدة</label>
                            <div style="position: relative;">
                                <input type="password" id="editSinglePassword" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="اتركها فارغة للاحتفاظ بالحالية">
                                <button type="button" onclick="togglePasswordVisibility('editSinglePassword')" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">👁️</button>
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مستوى الصلاحية</label>
                            <select id="editSingleRole" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="admin" ${credential.role === 'admin' ? 'selected' : ''}>مدير العيادة (كامل الصلاحيات)</option>
                                <option value="settings" ${credential.role === 'settings' ? 'selected' : ''}>إعدادات فقط</option>
                                <option value="readonly" ${credential.role === 'readonly' ? 'selected' : ''}>قراءة فقط</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">حالة الحساب</label>
                            <select id="editSingleActive" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="true" ${credential.isActive ? 'selected' : ''}>نشط</option>
                                <option value="false" ${!credential.isActive ? 'selected' : ''}>معطل</option>
                            </select>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات</label>
                            <textarea id="editSingleNotes" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">${credential.notes || ''}</textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">✅ حفظ التعديلات</button>
                        <button type="button" onclick="generateRandomPasswordForSingleEdit()" style="background: #3498db; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">🎲 توليد كلمة مرور جديدة</button>
                        <button type="button" onclick="closeModal('editSingleCredentialModal')" style="background: #6c757d; color: white; padding: 10px 25px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">❌ إلغاء</button>
                    </div>
                </form>
            `);
        }

        function generateRandomPasswordForSingleEdit() {
            var newPassword = generateRandomPasswordString();
            document.getElementById('editSinglePassword').value = newPassword;
            alert('✅ تم توليد كلمة مرور جديدة!\n\nكلمة المرور: ' + newPassword + '\n\n⚠️ تأكد من حفظها في مكان آمن');
        }

        function saveSingleEditedCredential(event, credId) {
            event.preventDefault();

            var newUsername = document.getElementById('editSingleUsername').value;
            var newPassword = document.getElementById('editSinglePassword').value;
            var newRole = document.getElementById('editSingleRole').value;
            var newActive = document.getElementById('editSingleActive').value === 'true';
            var newNotes = document.getElementById('editSingleNotes').value;

            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var credentialIndex = credentials.findIndex(function(cred) { return cred.id === credId; });

            if (credentialIndex !== -1) {
                // التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
                if (credentials[credentialIndex].username !== newUsername) {
                    var existingUser = credentials.find(function(cred) { return cred.username === newUsername && cred.id !== credId; });
                    if (existingUser) {
                        alert('⚠️ اسم المستخدم موجود مسبقاً!\nيرجى اختيار اسم مستخدم آخر.');
                        return;
                    }
                }

                // تحديث البيانات
                credentials[credentialIndex].username = newUsername;
                if (newPassword) {
                    credentials[credentialIndex].password = newPassword;
                }
                credentials[credentialIndex].role = newRole;
                credentials[credentialIndex].isActive = newActive;
                credentials[credentialIndex].notes = newNotes;

                localStorage.setItem('systemCredentials', JSON.stringify(credentials));

                alert('✅ تم تحديث أوراق الاعتماد بنجاح!');
                closeModal('editSingleCredentialModal');
                viewCredentials(); // إعادة تحميل القائمة
                updateStats(); // تحديث الإحصائيات
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            // تحديث إحصائيات العيادات
            var clinics = JSON.parse(localStorage.getItem('registeredClinics') || '[]');
            var activeClinics = clinics.filter(function(clinic) { return clinic.status === 'نشط'; });

            // تحديث الإحصائيات المالية
            var incomes = JSON.parse(localStorage.getItem('adminIncomes') || '[]');
            var expenses = JSON.parse(localStorage.getItem('adminExpenses') || '[]');
            var totalRevenue = incomes.reduce(function(sum, income) { return sum + (income.amount || 0); }, 0);

            // تحديث إحصائيات أوراق الاعتماد
            var credentials = JSON.parse(localStorage.getItem('systemCredentials') || '[]');
            var activeCredentials = credentials.filter(function(cred) { return cred.isActive; });

            // تحديث العرض
            document.getElementById('total-clinics').textContent = clinics.length;
            document.getElementById('total-doctors').textContent = activeClinics.length;
            document.getElementById('total-patients').textContent = clinics.length * 25; // تقدير
            document.getElementById('total-revenue').textContent = totalRevenue.toLocaleString();
            document.getElementById('total-credentials').textContent = activeCredentials.length;
        }

        // تحميل الصفحة
        window.onload = function() {
            updateStats();
        };
    </script>
</body>
</html>