# Generated by Django 5.2.3 on 2025-06-20 18:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('nutrition_plans', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_date', models.DateTimeField(verbose_name='تاريخ ووقت الموعد')),
                ('duration_minutes', models.PositiveIntegerField(default=30, verbose_name='مدة الموعد (دقيقة)')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('confirmed', 'مؤكدة'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('no_show', 'لم يحضر')], default='scheduled', max_length=20, verbose_name='حالة الموعد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('reminder_sent', models.BooleanField(default=False, verbose_name='تم إرسال التذكير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('doctor', models.ForeignKey(limit_choices_to={'user_type': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to=settings.AUTH_USER_MODEL, verbose_name='الطبيب')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='patients.patient', verbose_name='المريض')),
            ],
            options={
                'verbose_name': 'موعد',
                'verbose_name_plural': 'المواعيد',
                'ordering': ['appointment_date'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='patients.patient', verbose_name='المريض')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('message_content', models.TextField(verbose_name='محتوى الرسالة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('sent', 'مرسلة'), ('delivered', 'تم التسليم'), ('read', 'مقروءة'), ('failed', 'فشل الإرسال')], default='pending', max_length=20, verbose_name='حالة الرسالة')),
                ('whatsapp_message_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف رسالة الواتساب')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')),
                ('nutrition_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_messages', to='nutrition_plans.nutritionplan', verbose_name='الخطة الغذائية')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_messages', to='patients.patient', verbose_name='المريض')),
            ],
            options={
                'verbose_name': 'رسالة واتساب',
                'verbose_name_plural': 'رسائل الواتساب',
                'ordering': ['-sent_at'],
            },
        ),
    ]
