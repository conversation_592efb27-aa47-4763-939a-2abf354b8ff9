import requests
import json
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from twilio.rest import Client
from .models import WhatsAppMessage
import logging

logger = logging.getLogger(__name__)


class WhatsAppService:
    """خدمة إرسال رسائل الواتساب"""
    
    def __init__(self):
        self.use_twilio = bool(settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN)
        self.use_whatsapp_api = bool(settings.WHATSAPP_ACCESS_TOKEN and settings.WHATSAPP_PHONE_NUMBER_ID)
        
        if self.use_twilio:
            self.twilio_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        
    def send_message(self, patient, message_content, nutrition_plan=None):
        """إرسال رسالة واتساب للمريض"""
        
        # التحقق من وجود رقم واتساب
        phone_number = patient.user.whatsapp_number or patient.user.phone_number
        if not phone_number:
            logger.error(f"لا يوجد رقم واتساب للمريض {patient.user.get_full_name()}")
            return False, "لا يوجد رقم واتساب للمريض"
        
        # تنسيق رقم الهاتف
        formatted_phone = self._format_phone_number(phone_number)
        
        # إنشاء سجل الرسالة
        whatsapp_message = WhatsAppMessage.objects.create(
            patient=patient,
            nutrition_plan=nutrition_plan,
            phone_number=formatted_phone,
            message_content=message_content,
            status='pending'
        )
        
        # محاولة الإرسال
        success = False
        error_message = ""
        
        if self.use_whatsapp_api:
            success, error_message = self._send_via_whatsapp_api(whatsapp_message)
        elif self.use_twilio:
            success, error_message = self._send_via_twilio(whatsapp_message)
        else:
            error_message = "لم يتم تكوين خدمة الواتساب"
            logger.error("WhatsApp service not configured")
        
        # تحديث حالة الرسالة
        if success:
            whatsapp_message.status = 'sent'
        else:
            whatsapp_message.status = 'failed'
            whatsapp_message.error_message = error_message
        
        whatsapp_message.save()
        
        return success, error_message
    
    def _send_via_whatsapp_api(self, whatsapp_message):
        """إرسال عبر WhatsApp Business API"""
        try:
            url = f"{settings.WHATSAPP_API_URL}{settings.WHATSAPP_PHONE_NUMBER_ID}/messages"
            
            headers = {
                'Authorization': f'Bearer {settings.WHATSAPP_ACCESS_TOKEN}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'messaging_product': 'whatsapp',
                'to': whatsapp_message.phone_number,
                'type': 'text',
                'text': {
                    'body': whatsapp_message.message_content
                }
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                if 'messages' in response_data and response_data['messages']:
                    whatsapp_message.whatsapp_message_id = response_data['messages'][0]['id']
                    whatsapp_message.save()
                    logger.info(f"WhatsApp message sent successfully to {whatsapp_message.phone_number}")
                    return True, ""
                else:
                    error_msg = "لم يتم إرسال الرسالة - استجابة غير متوقعة"
                    logger.error(f"Unexpected WhatsApp API response: {response_data}")
                    return False, error_msg
            else:
                error_msg = f"فشل في إرسال الرسالة - كود الخطأ: {response.status_code}"
                logger.error(f"WhatsApp API error: {response.status_code} - {response.text}")
                return False, error_msg
                
        except requests.exceptions.RequestException as e:
            error_msg = f"خطأ في الاتصال: {str(e)}"
            logger.error(f"WhatsApp API connection error: {e}")
            return False, error_msg
        except Exception as e:
            error_msg = f"خطأ غير متوقع: {str(e)}"
            logger.error(f"Unexpected error in WhatsApp API: {e}")
            return False, error_msg
    
    def _send_via_twilio(self, whatsapp_message):
        """إرسال عبر Twilio"""
        try:
            message = self.twilio_client.messages.create(
                body=whatsapp_message.message_content,
                from_=f'whatsapp:{settings.TWILIO_PHONE_NUMBER}',
                to=f'whatsapp:{whatsapp_message.phone_number}'
            )
            
            whatsapp_message.whatsapp_message_id = message.sid
            whatsapp_message.save()
            
            logger.info(f"Twilio WhatsApp message sent successfully to {whatsapp_message.phone_number}")
            return True, ""
            
        except Exception as e:
            error_msg = f"خطأ في إرسال الرسالة عبر Twilio: {str(e)}"
            logger.error(f"Twilio error: {e}")
            return False, error_msg
    
    def _format_phone_number(self, phone_number):
        """تنسيق رقم الهاتف للواتساب"""
        # إزالة المسافات والرموز الخاصة
        phone = ''.join(filter(str.isdigit, phone_number))
        
        # إضافة رمز الدولة إذا لم يكن موجوداً
        if not phone.startswith('964'):  # رمز العراق
            if phone.startswith('0'):
                phone = '964' + phone[1:]
            else:
                phone = '964' + phone
        
        return phone
    
    def send_nutrition_plan(self, patient, nutrition_plan):
        """إرسال الخطة الغذائية للمريض"""
        
        # إنشاء محتوى الرسالة
        message_content = self._create_nutrition_plan_message(patient, nutrition_plan)
        
        return self.send_message(patient, message_content, nutrition_plan)
    
    def _create_nutrition_plan_message(self, patient, nutrition_plan):
        """إنشاء محتوى رسالة الخطة الغذائية"""
        
        message = f"""🌟 خطتك الغذائية الجديدة 🌟

مرحباً {patient.user.first_name}،

تم إنشاء خطة غذائية جديدة لك:

📋 *{nutrition_plan.title}*

📊 الأهداف اليومية:
• السعرات: {nutrition_plan.target_calories} سعرة
• البروتين: {nutrition_plan.target_protein}غ
• الكربوهيدرات: {nutrition_plan.target_carbs}غ  
• الدهون: {nutrition_plan.target_fat}غ

📅 فترة الخطة:
من {nutrition_plan.start_date} إلى {nutrition_plan.end_date}

"""

        if nutrition_plan.instructions:
            message += f"""📝 تعليمات خاصة:
{nutrition_plan.instructions}

"""

        message += f"""💡 نصائح مهمة:
• اتبع الخطة بدقة للحصول على أفضل النتائج
• اشرب 8-10 أكواب ماء يومياً
• مارس الرياضة بانتظام
• سجل وزنك أسبوعياً

📱 يمكنك الوصول للخطة التفصيلية عبر تطبيق العيادة

🩺 د. {nutrition_plan.doctor.get_full_name()}
{nutrition_plan.doctor.doctor_profile.clinic_name if hasattr(nutrition_plan.doctor, 'doctor_profile') else ''}

للاستفسارات، تواصل معنا على نفس هذا الرقم.

نتمنى لك رحلة صحية موفقة! 💪"""

        return message
    
    def send_appointment_reminder(self, appointment):
        """إرسال تذكير بالموعد"""
        
        message_content = f"""⏰ تذكير بالموعد

مرحباً {appointment.patient.user.first_name}،

لديك موعد مع د. {appointment.doctor.get_full_name()}

📅 التاريخ: {appointment.appointment_date.strftime('%Y-%m-%d')}
🕐 الوقت: {appointment.appointment_date.strftime('%H:%M')}
⏱️ المدة: {appointment.duration_minutes} دقيقة

📍 العيادة: {appointment.doctor.doctor_profile.clinic_name if hasattr(appointment.doctor, 'doctor_profile') else 'العيادة'}

يرجى الحضور قبل 15 دقيقة من الموعد.

للإلغاء أو التأجيل، يرجى التواصل معنا.

نتطلع لرؤيتك! 😊"""

        return self.send_message(appointment.patient, message_content)
    
    def send_weight_reminder(self, patient):
        """إرسال تذكير بتسجيل الوزن"""
        
        message_content = f"""⚖️ تذكير تسجيل الوزن

مرحباً {patient.user.first_name}،

حان وقت تسجيل وزنك الأسبوعي! 

📊 تسجيل الوزن بانتظام يساعد في:
• متابعة تقدمك
• تعديل الخطة الغذائية حسب الحاجة
• تحفيزك للاستمرار

📱 يمكنك تسجيل وزنك عبر تطبيق العيادة أو إرساله لنا هنا.

استمر في العمل الرائع! 💪

🩺 د. {patient.doctor.get_full_name()}"""

        return self.send_message(patient, message_content)


# إنشاء instance واحد للاستخدام
whatsapp_service = WhatsAppService()
