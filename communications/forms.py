from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Message, Appointment
from patients.models import Patient


class MessageForm(forms.ModelForm):
    """نموذج إرسال رسالة"""
    
    class Meta:
        model = Message
        fields = ['subject', 'content']
        widgets = {
            'subject': forms.TextInput(attrs={'class': 'form-control'}),
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 6}),
        }


class AppointmentForm(forms.ModelForm):
    """نموذج إضافة/تعديل موعد"""
    
    class Meta:
        model = Appointment
        fields = ['patient', 'appointment_date', 'duration_minutes', 'notes']
        widgets = {
            'patient': forms.Select(attrs={'class': 'form-select'}),
            'appointment_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'duration_minutes': forms.NumberInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        doctor = kwargs.pop('doctor', None)
        super().__init__(*args, **kwargs)
        
        if doctor:
            # تحديد المرضى التابعين للطبيب فقط
            self.fields['patient'].queryset = Patient.objects.filter(doctor=doctor)
        
        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.pk:
            from django.utils import timezone
            self.fields['appointment_date'].initial = timezone.now().strftime('%Y-%m-%dT%H:%M')


class WhatsAppMessageForm(forms.Form):
    """نموذج إرسال رسالة واتساب"""
    
    message_content = forms.CharField(
        label=_('محتوى الرسالة'),
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': _('اكتب رسالتك هنا...')
        }),
        max_length=1000,
        help_text=_('الحد الأقصى 1000 حرف')
    )


class MessageSearchForm(forms.Form):
    """نموذج البحث في الرسائل"""
    
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('البحث في الرسائل...')
        })
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', _('جميع الحالات')),
            ('read', _('مقروءة')),
            ('unread', _('غير مقروءة')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class AppointmentSearchForm(forms.Form):
    """نموذج البحث في المواعيد"""
    
    status = forms.ChoiceField(
        choices=[('', _('جميع الحالات'))] + Appointment.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class WhatsAppMessageSearchForm(forms.Form):
    """نموذج البحث في رسائل الواتساب"""
    
    status = forms.ChoiceField(
        choices=[
            ('', _('جميع الحالات')),
            ('pending', _('في الانتظار')),
            ('sent', _('مرسلة')),
            ('delivered', _('تم التسليم')),
            ('read', _('مقروءة')),
            ('failed', _('فشل الإرسال')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    patient = forms.ModelChoiceField(
        queryset=Patient.objects.none(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        empty_label=_('جميع المرضى')
    )

    def __init__(self, *args, **kwargs):
        doctor = kwargs.pop('doctor', None)
        super().__init__(*args, **kwargs)
        
        if doctor:
            self.fields['patient'].queryset = Patient.objects.filter(doctor=doctor)
