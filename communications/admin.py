from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Message, WhatsAppMessage, Appointment


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """إدارة الرسائل"""
    list_display = ('subject', 'sender', 'recipient', 'patient', 'is_read', 'created_at')
    list_filter = ('is_read', 'created_at', 'patient__doctor')
    search_fields = ('subject', 'sender__first_name', 'recipient__first_name', 'patient__user__first_name')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('معلومات الرسالة'), {
            'fields': ('sender', 'recipient', 'patient', 'subject')
        }),
        (_('المحتوى'), {
            'fields': ('content',)
        }),
        (_('حالة القراءة'), {
            'fields': ('is_read', 'read_at')
        }),
    )

    readonly_fields = ('created_at', 'read_at')


@admin.register(WhatsAppMessage)
class WhatsAppMessageAdmin(admin.ModelAdmin):
    """إدارة رسائل الواتساب"""
    list_display = ('patient', 'phone_number', 'status', 'sent_at', 'delivered_at')
    list_filter = ('status', 'sent_at')
    search_fields = ('patient__user__first_name', 'patient__user__last_name', 'phone_number')
    date_hierarchy = 'sent_at'

    fieldsets = (
        (_('معلومات الرسالة'), {
            'fields': ('patient', 'nutrition_plan', 'phone_number')
        }),
        (_('المحتوى'), {
            'fields': ('message_content',)
        }),
        (_('حالة الإرسال'), {
            'fields': ('status', 'whatsapp_message_id', 'error_message')
        }),
        (_('التواريخ'), {
            'fields': ('sent_at', 'delivered_at')
        }),
    )

    readonly_fields = ('sent_at', 'delivered_at')


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    """إدارة المواعيد"""
    list_display = ('patient', 'doctor', 'appointment_date', 'duration_minutes', 'status', 'reminder_sent')
    list_filter = ('status', 'appointment_date', 'doctor', 'reminder_sent')
    search_fields = ('patient__user__first_name', 'patient__user__last_name', 'doctor__first_name')
    date_hierarchy = 'appointment_date'

    fieldsets = (
        (_('معلومات الموعد'), {
            'fields': ('patient', 'doctor', 'appointment_date', 'duration_minutes')
        }),
        (_('الحالة'), {
            'fields': ('status', 'reminder_sent')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
