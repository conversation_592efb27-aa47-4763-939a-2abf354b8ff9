from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import Message, WhatsAppMessage, Appointment
from .forms import MessageForm, AppointmentForm, WhatsAppMessageForm
from .services import whatsapp_service
from patients.models import Patient


@login_required
def message_list(request):
    """قائمة الرسائل"""
    if request.user.user_type == 'doctor':
        # رسائل الطبيب (المرسلة والمستقبلة)
        messages_qs = Message.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ).order_by('-created_at')
    elif request.user.user_type == 'patient':
        # رسائل المريض
        try:
            patient = request.user.patient_profile
            messages_qs = Message.objects.filter(
                patient=patient
            ).order_by('-created_at')
        except:
            messages_qs = Message.objects.none()
    else:
        messages_qs = Message.objects.none()

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        messages_qs = messages_qs.filter(
            Q(subject__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(sender__first_name__icontains=search_query) |
            Q(recipient__first_name__icontains=search_query)
        )

    # الترقيم
    paginator = Paginator(messages_qs, 10)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)

    context = {
        'messages': messages_page,
        'search_query': search_query,
    }
    return render(request, 'communications/message_list.html', context)


@login_required
def send_message(request, patient_id):
    """إرسال رسالة للمريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = MessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.sender = request.user
            message.recipient = patient.user
            message.patient = patient
            message.save()

            messages.success(request, _('تم إرسال الرسالة بنجاح!'))
            return redirect('communications:message_list')
    else:
        form = MessageForm()

    context = {
        'form': form,
        'patient': patient,
    }
    return render(request, 'communications/send_message.html', context)


@login_required
def message_detail(request, message_id):
    """تفاصيل الرسالة"""
    if request.user.user_type == 'doctor':
        message = get_object_or_404(Message.objects.filter(
            Q(sender=request.user) | Q(recipient=request.user)
        ), id=message_id)
    elif request.user.user_type == 'patient':
        try:
            patient = request.user.patient_profile
            message = get_object_or_404(Message, id=message_id, patient=patient)
        except:
            messages.error(request, _('لم يتم العثور على الرسالة.'))
            return redirect('communications:message_list')
    else:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # تحديد الرسالة كمقروءة إذا كان المستقبل
    if request.user == message.recipient and not message.is_read:
        message.is_read = True
        message.read_at = timezone.now()
        message.save()

    context = {
        'message': message,
    }
    return render(request, 'communications/message_detail.html', context)


@login_required
def appointment_list(request):
    """قائمة المواعيد"""
    if request.user.user_type == 'doctor':
        appointments = Appointment.objects.filter(doctor=request.user)
    elif request.user.user_type == 'patient':
        try:
            patient = request.user.patient_profile
            appointments = Appointment.objects.filter(patient=patient)
        except:
            appointments = Appointment.objects.none()
    else:
        appointments = Appointment.objects.none()

    # التصفية حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # التصفية حسب التاريخ
    date_filter = request.GET.get('date')
    if date_filter:
        appointments = appointments.filter(appointment_date__date=date_filter)

    appointments = appointments.order_by('appointment_date')

    # الترقيم
    paginator = Paginator(appointments, 10)
    page_number = request.GET.get('page')
    appointments_page = paginator.get_page(page_number)

    context = {
        'appointments': appointments_page,
        'status_filter': status_filter,
        'date_filter': date_filter,
    }
    return render(request, 'communications/appointment_list.html', context)


@login_required
def add_appointment(request):
    """إضافة موعد جديد"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    if request.method == 'POST':
        form = AppointmentForm(request.POST, doctor=request.user)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.doctor = request.user
            appointment.save()

            messages.success(request, _('تم إضافة الموعد بنجاح!'))
            return redirect('communications:appointment_list')
    else:
        form = AppointmentForm(doctor=request.user)

    context = {
        'form': form,
    }
    return render(request, 'communications/add_appointment.html', context)


@login_required
def appointment_detail(request, appointment_id):
    """تفاصيل الموعد"""
    if request.user.user_type == 'doctor':
        appointment = get_object_or_404(Appointment, id=appointment_id, doctor=request.user)
    elif request.user.user_type == 'patient':
        try:
            patient = request.user.patient_profile
            appointment = get_object_or_404(Appointment, id=appointment_id, patient=patient)
        except:
            messages.error(request, _('لم يتم العثور على الموعد.'))
            return redirect('communications:appointment_list')
    else:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    context = {
        'appointment': appointment,
    }
    return render(request, 'communications/appointment_detail.html', context)


@login_required
def edit_appointment(request, appointment_id):
    """تعديل الموعد"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    appointment = get_object_or_404(Appointment, id=appointment_id, doctor=request.user)

    if request.method == 'POST':
        form = AppointmentForm(request.POST, instance=appointment, doctor=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث الموعد بنجاح!'))
            return redirect('communications:appointment_detail', appointment_id=appointment.id)
    else:
        form = AppointmentForm(instance=appointment, doctor=request.user)

    context = {
        'form': form,
        'appointment': appointment,
    }
    return render(request, 'communications/edit_appointment.html', context)


@login_required
def cancel_appointment(request, appointment_id):
    """إلغاء الموعد"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    appointment = get_object_or_404(Appointment, id=appointment_id, doctor=request.user)

    if request.method == 'POST':
        appointment.status = 'cancelled'
        appointment.save()
        messages.success(request, _('تم إلغاء الموعد بنجاح!'))
        return redirect('communications:appointment_list')

    context = {
        'appointment': appointment,
    }
    return render(request, 'communications/cancel_appointment.html', context)


@login_required
def whatsapp_message_list(request):
    """قائمة رسائل الواتساب"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # رسائل الواتساب للمرضى التابعين للطبيب
    whatsapp_messages = WhatsAppMessage.objects.filter(
        patient__doctor=request.user
    ).order_by('-sent_at')

    # التصفية حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        whatsapp_messages = whatsapp_messages.filter(status=status_filter)

    # الترقيم
    paginator = Paginator(whatsapp_messages, 10)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)

    context = {
        'whatsapp_messages': messages_page,
        'status_filter': status_filter,
    }
    return render(request, 'communications/whatsapp_message_list.html', context)


@login_required
def send_whatsapp_message(request, patient_id):
    """إرسال رسالة واتساب للمريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = WhatsAppMessageForm(request.POST)
        if form.is_valid():
            message_content = form.cleaned_data['message_content']

            # إرسال الرسالة
            success, error_message = whatsapp_service.send_message(patient, message_content)

            if success:
                messages.success(request, _('تم إرسال رسالة الواتساب بنجاح!'))
                return redirect('communications:whatsapp_message_list')
            else:
                messages.error(request, _('فشل في إرسال الرسالة: {}').format(error_message))
    else:
        form = WhatsAppMessageForm()

    context = {
        'form': form,
        'patient': patient,
    }
    return render(request, 'communications/send_whatsapp_message.html', context)
