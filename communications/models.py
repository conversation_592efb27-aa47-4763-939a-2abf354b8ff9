from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class Message(models.Model):
    """الرسائل بين الطبيب والمريض"""
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name=_('المرسل')
    )
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_messages',
        verbose_name=_('المستقبل')
    )
    patient = models.ForeignKey(
        'patients.Patient',
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name=_('المريض')
    )

    subject = models.CharField(
        max_length=200,
        verbose_name=_('الموضوع')
    )
    content = models.TextField(
        verbose_name=_('المحتوى')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('مقروءة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرسال')
    )
    read_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ القراءة')
    )

    class Meta:
        verbose_name = _('رسالة')
        verbose_name_plural = _('الرسائل')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.subject} - من {self.sender} إلى {self.recipient}"


class WhatsAppMessage(models.Model):
    """رسائل الواتساب المرسلة"""
    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('sent', _('مرسلة')),
        ('delivered', _('تم التسليم')),
        ('read', _('مقروءة')),
        ('failed', _('فشل الإرسال')),
    ]

    patient = models.ForeignKey(
        'patients.Patient',
        on_delete=models.CASCADE,
        related_name='whatsapp_messages',
        verbose_name=_('المريض')
    )
    nutrition_plan = models.ForeignKey(
        'nutrition_plans.NutritionPlan',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='whatsapp_messages',
        verbose_name=_('الخطة الغذائية')
    )

    phone_number = models.CharField(
        max_length=20,
        verbose_name=_('رقم الهاتف')
    )
    message_content = models.TextField(
        verbose_name=_('محتوى الرسالة')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الرسالة')
    )

    # معرف الرسالة من WhatsApp API
    whatsapp_message_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('معرف رسالة الواتساب')
    )

    error_message = models.TextField(
        blank=True,
        verbose_name=_('رسالة الخطأ')
    )

    sent_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرسال')
    )
    delivered_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ التسليم')
    )

    class Meta:
        verbose_name = _('رسالة واتساب')
        verbose_name_plural = _('رسائل الواتساب')
        ordering = ['-sent_at']

    def __str__(self):
        return f"واتساب إلى {self.patient} - {self.status}"


class Appointment(models.Model):
    """المواعيد"""
    STATUS_CHOICES = [
        ('scheduled', _('مجدولة')),
        ('confirmed', _('مؤكدة')),
        ('completed', _('مكتملة')),
        ('cancelled', _('ملغية')),
        ('no_show', _('لم يحضر')),
    ]

    patient = models.ForeignKey(
        'patients.Patient',
        on_delete=models.CASCADE,
        related_name='appointments',
        verbose_name=_('المريض')
    )
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='appointments',
        limit_choices_to={'user_type': 'doctor'},
        verbose_name=_('الطبيب')
    )

    appointment_date = models.DateTimeField(
        verbose_name=_('تاريخ ووقت الموعد')
    )
    duration_minutes = models.PositiveIntegerField(
        default=30,
        verbose_name=_('مدة الموعد (دقيقة)')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الموعد')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    reminder_sent = models.BooleanField(
        default=False,
        verbose_name=_('تم إرسال التذكير')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('موعد')
        verbose_name_plural = _('المواعيد')
        ordering = ['appointment_date']

    def __str__(self):
        return f"{self.patient} - {self.appointment_date.strftime('%Y-%m-%d %H:%M')}"
