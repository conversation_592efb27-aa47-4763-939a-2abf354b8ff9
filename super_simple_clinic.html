<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام العيادات</title>
</head>
<body style="font-family: Arial; direction: rtl; margin: 0;">
    
    <div style="display: flex;">
        <!-- Sidebar -->
        <div style="width: 250px; background: #2c3e50; color: white; height: 100vh; padding: 20px 0;">
            <h2 style="text-align: center; color: white; margin-bottom: 30px;">🏥 نظام العيادات</h2>
            
            <div style="padding: 15px 20px; background: #3498db; margin-bottom: 5px; cursor: pointer;" onclick="showDashboard()">
                📊 لوحة التحكم
            </div>
            
            <div style="padding: 15px 20px; background: #34495e; margin-bottom: 5px; cursor: pointer;" onclick="showPatients()">
                👥 إدارة المرضى
            </div>
            
            <div style="padding: 15px 20px; background: #34495e; margin-bottom: 5px; cursor: pointer;" onclick="showNutrition()">
                🍽️ الخطط الغذائية
            </div>
            
            <div style="padding: 15px 20px; background: #34495e; margin-bottom: 5px; cursor: pointer;" onclick="showPrescriptions()">
                💊 الوصفات الطبية
            </div>
            
            <div style="padding: 15px 20px; background: #34495e; margin-bottom: 5px; cursor: pointer;" onclick="showAccounting()">
                💰 إدارة الحسابات
            </div>
        </div>
        
        <!-- Content -->
        <div style="flex: 1; padding: 30px; background: #f5f5f5;">
            
            <!-- Dashboard -->
            <div id="dashboard" style="background: white; padding: 30px; border-radius: 10px;">
                <h1>📊 لوحة التحكم</h1>
                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold;">
                    ✅ مرحباً بك في نظام إدارة العيادات! النظام يعمل بشكل مثالي!
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #3498db; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('المرضى: 25 مريض')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">25</div>
                        <div>إجمالي المرضى</div>
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('الخطط: 12 خطة')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">12</div>
                        <div>خطط نشطة</div>
                    </div>
                    <div style="background: #27ae60; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('المواعيد: 8 مواعيد')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">8</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div style="background: #f39c12; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('الرسائل: 45 رسالة')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">45</div>
                        <div>رسائل جديدة</div>
                    </div>
                </div>
                
                <h3>🎯 ميزات النظام:</h3>
                <ul>
                    <li>✅ إدارة شاملة للمرضى</li>
                    <li>✅ كتابة الوصفات الطبية</li>
                    <li>✅ نظام محاسبي بالدينار العراقي</li>
                    <li>✅ إدارة الخطط الغذائية</li>
                </ul>
            </div>
            
            <!-- Patients -->
            <div id="patients" style="background: white; padding: 30px; border-radius: 10px; display: none;">
                <h1>👥 إدارة المرضى</h1>
                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold;">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>
                
                <h3>🔧 الوظائف المتاحة:</h3>
                <ul>
                    <li>إضافة مرضى جدد</li>
                    <li>تعديل بيانات المرضى</li>
                    <li>عرض التاريخ الطبي</li>
                    <li>متابعة تقدم المرضى</li>
                </ul>
                
                <button style="background: #27ae60; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px;" onclick="alert('إضافة مريض جديد')">
                    ➕ إضافة مريض جديد
                </button>
                <button style="background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px;" onclick="alert('عرض قائمة المرضى')">
                    📋 عرض قائمة المرضى
                </button>
            </div>
            
            <!-- Nutrition -->
            <div id="nutrition" style="background: white; padding: 30px; border-radius: 10px; display: none;">
                <h1>🍽️ الخطط الغذائية</h1>
                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold;">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #e74c3c; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('خطط إنقاص الوزن: 12 خطة')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">12</div>
                        <div>خطط إنقاص الوزن</div>
                    </div>
                    <div style="background: #27ae60; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('خطط زيادة الوزن: 8 خطط')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">8</div>
                        <div>خطط زيادة الوزن</div>
                    </div>
                    <div style="background: #f39c12; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('خطط الحفاظ على الوزن: 5 خطط')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">5</div>
                        <div>خطط الحفاظ على الوزن</div>
                    </div>
                </div>
                
                <button style="background: #27ae60; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px;" onclick="alert('إنشاء خطة جديدة')">
                    ➕ إنشاء خطة جديدة
                </button>
            </div>
            
            <!-- Prescriptions -->
            <div id="prescriptions" style="background: white; padding: 30px; border-radius: 10px; display: none;">
                <h1>💊 الوصفات الطبية</h1>
                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold;">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #3498db; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('وصفات اليوم: 8 وصفات')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">8</div>
                        <div>وصفات اليوم</div>
                    </div>
                    <div style="background: #9b59b6; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('إجمالي الوصفات: 156 وصفة')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">156</div>
                        <div>إجمالي الوصفات</div>
                    </div>
                    <div style="background: #1abc9c; color: white; padding: 20px; border-radius: 10px; text-align: center; cursor: pointer;" onclick="alert('وصفات مطبوعة: 142 وصفة')">
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 10px;">142</div>
                        <div>وصفات مطبوعة</div>
                    </div>
                </div>
                
                <button style="background: #27ae60; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px;" onclick="alert('كتابة وصفة جديدة')">
                    📝 كتابة وصفة جديدة
                </button>
                <button style="background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px;" onclick="printPrescription()">
                    🖨️ طباعة وصفة تجريبية
                </button>
            </div>
            
            <!-- Accounting -->
            <div id="accounting" style="background: white; padding: 30px; border-radius: 10px; display: none;">
                <h1>💰 إدارة الحسابات</h1>
                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold;">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #27ae60; color: white; padding: 25px; border-radius: 15px; text-align: center;">
                        <h3>💚 إجمالي الإيرادات</h3>
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 5px;">58,500,000</div>
                        <div style="font-size: 16px; opacity: 0.9;">د.ع</div>
                        <small style="opacity: 0.8;">+15% هذا الشهر</small>
                    </div>
                    <div style="background: #e74c3c; color: white; padding: 25px; border-radius: 15px; text-align: center;">
                        <h3>💸 إجمالي المصروفات</h3>
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 5px;">24,050,000</div>
                        <div style="font-size: 16px; opacity: 0.9;">د.ع</div>
                        <small style="opacity: 0.8;">-5% عن الشهر الماضي</small>
                    </div>
                    <div style="background: #f39c12; color: white; padding: 25px; border-radius: 15px; text-align: center;">
                        <h3>📈 صافي الربح</h3>
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 5px;">34,450,000</div>
                        <div style="font-size: 16px; opacity: 0.9;">د.ع</div>
                        <small style="opacity: 0.8;">+25% نمو ممتاز</small>
                    </div>
                </div>
                
                <h3>💼 الخدمات المالية:</h3>
                <ul>
                    <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                    <li>إدارة الفواتير والمدفوعات</li>
                    <li>تقارير مالية شهرية</li>
                    <li>حساب الضرائب المستحقة</li>
                </ul>
            </div>
            
        </div>
    </div>

    <script>
        function hideAll() {
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('patients').style.display = 'none';
            document.getElementById('nutrition').style.display = 'none';
            document.getElementById('prescriptions').style.display = 'none';
            document.getElementById('accounting').style.display = 'none';
        }
        
        function showDashboard() {
            hideAll();
            document.getElementById('dashboard').style.display = 'block';
            alert('✅ تم الانتقال إلى لوحة التحكم');
        }
        
        function showPatients() {
            hideAll();
            document.getElementById('patients').style.display = 'block';
            alert('✅ تم الانتقال إلى إدارة المرضى');
        }
        
        function showNutrition() {
            hideAll();
            document.getElementById('nutrition').style.display = 'block';
            alert('✅ تم الانتقال إلى الخطط الغذائية');
        }
        
        function showPrescriptions() {
            hideAll();
            document.getElementById('prescriptions').style.display = 'block';
            alert('✅ تم الانتقال إلى الوصفات الطبية');
        }
        
        function showAccounting() {
            hideAll();
            document.getElementById('accounting').style.display = 'block';
            alert('✅ تم الانتقال إلى إدارة الحسابات');
        }
        
        function printPrescription() {
            var printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body { font-family: Arial; padding: 20px; direction: rtl; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .patient { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                        .medicine { border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>🏥 عيادة التغذية العلاجية</h2>
                        <p>د. [اسم الطبيب] - أخصائي التغذية</p>
                    </div>
                    
                    <div class="patient">
                        <strong>المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>التاريخ:</strong> ${new Date().toLocaleDateString()}<br>
                        <strong>التشخيص:</strong> نقص فيتامين د
                    </div>
                    
                    <h3>الأدوية المطلوبة:</h3>
                    <div class="medicine">
                        <strong>1. فيتامين د3 1000 وحدة</strong><br>
                        الجرعة: حبة واحدة يومياً<br>
                        المدة: 3 أشهر
                    </div>
                    
                    <div class="medicine">
                        <strong>2. كالسيوم 500 ملغ</strong><br>
                        الجرعة: حبة واحدة مساءً<br>
                        المدة: شهر واحد
                    </div>
                    
                    <p><strong>التعليمات:</strong> التعرض لأشعة الشمس، شرب الحليب</p>
                    
                    <div style="margin-top: 50px;">
                        <p>توقيع الطبيب: _______________</p>
                    </div>
                    
                    <script>window.print();</script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
        
        alert('🎉 مرحباً بك في نظام إدارة العيادات!\n\nالـ Sidebar يعمل الآن!\n\nجرب النقر على الأزرار في الجانب الأيمن.');
    </script>
</body>
</html>
