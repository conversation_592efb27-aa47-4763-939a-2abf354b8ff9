<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول مبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="text-center mb-4">
            <h2>🏥 نظام إدارة العيادات</h2>
            <p class="text-muted">تسجيل دخول مبسط</p>
        </div>

        <div class="mb-4">
            <h5>حسابات تجريبية:</h5>
            <div class="alert alert-info">
                <strong>طبيب:</strong><br>
                اسم المستخدم: <code>doctor</code><br>
                كلمة المرور: <code>doctor123</code>
            </div>
        </div>

        <form id="loginForm">
            <div class="mb-3">
                <label class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" value="doctor" required>
            </div>
            <div class="mb-3">
                <label class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" value="doctor123" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
        </form>

        <div class="mt-4">
            <h6>روابط مباشرة للاختبار:</h6>
            <div class="d-grid gap-2">
                <a href="/patients/" class="btn btn-outline-primary btn-sm">قائمة المرضى</a>
                <a href="/nutrition/" class="btn btn-outline-success btn-sm">الخطط الغذائية</a>
                <a href="/nutrition/foods/" class="btn btn-outline-info btn-sm">إدارة الأطعمة</a>
                <a href="/communications/appointments/" class="btn btn-outline-warning btn-sm">المواعيد</a>
                <a href="/communications/messages/" class="btn btn-outline-secondary btn-sm">الرسائل</a>
                <a href="/communications/whatsapp/" class="btn btn-outline-success btn-sm">رسائل الواتساب</a>
            </div>
        </div>

        <div class="mt-4 text-center">
            <small class="text-muted">
                ملاحظة: تم تعطيل CSRF مؤقتاً للتطوير
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // محاكاة تسجيل دخول
            if (username === 'doctor' && password === 'doctor123') {
                alert('تم تسجيل الدخول بنجاح! سيتم توجيهك إلى لوحة التحكم');
                window.location.href = '/doctor/dashboard/';
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // اختبار الروابط
        document.querySelectorAll('a[href^="/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('href');
                
                // فتح الرابط في نافذة جديدة للاختبار
                window.open(url, '_blank');
            });
        });
    </script>
</body>
</html>
