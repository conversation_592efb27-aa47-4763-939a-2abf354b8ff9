<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تغيير اللغة - نهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار تغيير اللغة - الحل النهائي</h1>
        
        <div class="alert alert-info">
            <h5>معلومات اللغة الحالية:</h5>
            <p><strong>كود اللغة:</strong> {{ LANGUAGE_CODE }}</p>
            <p><strong>اللغة من الجلسة:</strong> {{ request.session.django_language|default:"غير محدد" }}</p>
            <p><strong>اللغة من Cookie:</strong> <span id="cookie-lang">جاري التحقق...</span></p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>تغيير اللغة - طريقة مبسطة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button onclick="setLanguageSimple('ar')" class="btn btn-primary btn-lg w-100 mb-3">
                            🇮🇶 العربية
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button onclick="setLanguageSimple('en')" class="btn btn-success btn-lg w-100 mb-3">
                            🇺🇸 English
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button onclick="setLanguageSimple('ku')" class="btn btn-warning btn-lg w-100 mb-3">
                            🟡🔴🟢 کوردی
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>اختبار النصوص المترجمة</h5>
            </div>
            <div class="card-body">
                <p>{% load i18n %}{% trans "مرحباً بك في نظام إدارة العيادة" %}</p>
                <p>{% trans "هذا نص تجريبي للترجمة" %}</p>
                <p>{% trans "الطبيب" %}: {% trans "دكتور" %}</p>
                <p>{% trans "المريض" %}: {% trans "مريض" %}</p>
                <p>{% trans "الخطة الغذائية" %}: {% trans "خطة غذائية" %}</p>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="/doctor/dashboard/" class="btn btn-secondary">العودة إلى لوحة التحكم</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة مبسطة لتغيير اللغة
        function setLanguageSimple(lang) {
            console.log('تغيير اللغة إلى:', lang);
            
            // طريقة 1: حفظ في cookie
            document.cookie = `django_language=${lang}; path=/; max-age=31536000; SameSite=Lax`;
            
            // طريقة 2: حفظ في localStorage
            localStorage.setItem('django_language', lang);
            
            // طريقة 3: إرسال طلب للخادم
            fetch('/set-language/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `language=${lang}&next=${encodeURIComponent(window.location.pathname)}`
            }).then(() => {
                // إعادة تحميل الصفحة
                window.location.reload();
            }).catch(error => {
                console.error('خطأ في تغيير اللغة:', error);
                // إعادة تحميل حتى لو فشل الطلب
                window.location.reload();
            });
        }
        
        // عرض اللغة من cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const cookieLang = getCookie('django_language');
            document.getElementById('cookie-lang').textContent = cookieLang || 'غير محدد';
            
            console.log('جميع cookies:', document.cookie);
            console.log('localStorage:', localStorage.getItem('django_language'));
        });
    </script>
</body>
</html>
