{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة موعد جديد - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-plus me-2"></i>إضافة موعد جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'communications:appointment_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للمواعيد
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>بيانات الموعد
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.patient.id_for_label }}" class="form-label">المريض *</label>
                            {{ form.patient|add_class:"form-select" }}
                            {% if form.patient.errors %}
                                <div class="text-danger small mt-1">{{ form.patient.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.appointment_date.id_for_label }}" class="form-label">تاريخ ووقت الموعد *</label>
                            {{ form.appointment_date|add_class:"form-control" }}
                            {% if form.appointment_date.errors %}
                                <div class="text-danger small mt-1">{{ form.appointment_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">مدة الموعد (بالدقائق) *</label>
                            {{ form.duration_minutes|add_class:"form-control" }}
                            {% if form.duration_minutes.errors %}
                                <div class="text-danger small mt-1">{{ form.duration_minutes.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">المدة الافتراضية: 30 دقيقة</div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'communications:appointment_list' %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الموعد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
