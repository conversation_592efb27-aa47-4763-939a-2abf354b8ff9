{% extends 'base.html' %}
{% load i18n %}

{% block title %}قائمة الرسائل{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-envelope me-2"></i>قائمة الرسائل</h2>
                {% if user.user_type == 'doctor' %}
                    <a href="{% url 'patients:patient_list' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إرسال رسالة جديدة
                    </a>
                {% endif %}
            </div>

            <!-- البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث في الرسائل..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الرسائل -->
            <div class="card">
                <div class="card-body">
                    {% if messages %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الموضوع</th>
                                        <th>المرسل</th>
                                        <th>المستقبل</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in messages %}
                                        <tr class="{% if not message.is_read and message.recipient == user %}table-warning{% endif %}">
                                            <td>
                                                <strong>{{ message.subject }}</strong>
                                                <br>
                                                <small class="text-muted">{{ message.content|truncatewords:10 }}</small>
                                            </td>
                                            <td>{{ message.sender.get_full_name|default:message.sender.username }}</td>
                                            <td>{{ message.recipient.get_full_name|default:message.recipient.username }}</td>
                                            <td>{{ message.created_at|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                {% if message.is_read %}
                                                    <span class="badge bg-success">مقروءة</span>
                                                {% else %}
                                                    <span class="badge bg-warning">غير مقروءة</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'communications:message_detail' message.id %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- الترقيم -->
                        {% if messages.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if messages.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ messages.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابق</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in messages.paginator.page_range %}
                                        {% if messages.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if messages.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ messages.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالي</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رسائل</h5>
                            {% if user.user_type == 'doctor' %}
                                <p class="text-muted">يمكنك إرسال رسالة جديدة من قائمة المرضى</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
