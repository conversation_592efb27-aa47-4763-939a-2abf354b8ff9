{% extends 'base.html' %}

{% block title %}قائمة المواعيد - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar me-2"></i>المواعيد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.user_type == 'doctor' %}
            <a href="{% url 'communications:add_appointment' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة موعد جديد
            </a>
        {% endif %}
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex gap-2">
            <select name="status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>مجدول</option>
                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
            </select>
            <input type="date" name="date" class="form-control" value="{{ date_filter }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-filter me-1"></i>تصفية
            </button>
        </form>
    </div>
</div>

<!-- Appointments List -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        {% if appointments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المريض</th>
                            <th>التاريخ والوقت</th>
                            <th>المدة</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appointment in appointments %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ appointment.patient.user.first_name|first }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ appointment.patient.user.get_full_name }}</div>
                                        <small class="text-muted">{{ appointment.patient.user.phone_number }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>{{ appointment.appointment_date|date:"Y-m-d" }}</div>
                                <small class="text-muted">{{ appointment.appointment_date|time:"H:i" }}</small>
                            </td>
                            <td>{{ appointment.duration_minutes }} دقيقة</td>
                            <td>
                                <span class="badge bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% else %}danger{% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                            </td>
                            <td>{{ appointment.notes|truncatechars:30|default:"-" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'communications:appointment_detail' appointment.id %}" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if user.user_type == 'doctor' and appointment.status == 'scheduled' %}
                                        <a href="{% url 'communications:edit_appointment' appointment.id %}" class="btn btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'communications:cancel_appointment' appointment.id %}" class="btn btn-outline-danger">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if appointments.has_other_pages %}
                <nav aria-label="تنقل المواعيد">
                    <ul class="pagination justify-content-center">
                        {% if appointments.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ appointments.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for num in appointments.paginator.page_range %}
                            {% if appointments.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > appointments.number|add:'-3' and num < appointments.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if appointments.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ appointments.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواعيد</h5>
                <p class="text-muted">لم يتم العثور على أي مواعيد بالمعايير المحددة.</p>
                {% if user.user_type == 'doctor' %}
                    <a href="{% url 'communications:add_appointment' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة موعد جديد
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}
</style>
{% endblock %}
