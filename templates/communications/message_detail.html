{% extends 'base.html' %}
{% load i18n %}

{% block title %}تفاصيل الرسالة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-envelope me-2"></i>تفاصيل الرسالة</h2>
                <a href="{% url 'communications:message_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="mb-0">{{ message.subject }}</h5>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if message.is_read %}
                                <span class="badge bg-success">مقروءة</span>
                            {% else %}
                                <span class="badge bg-warning">غير مقروءة</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الرسالة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <strong>المرسل:</strong> {{ message.sender.get_full_name|default:message.sender.username }}
                            <br>
                            <small class="text-muted">{{ message.sender.email }}</small>
                        </div>
                        <div class="col-md-6">
                            <strong>المستقبل:</strong> {{ message.recipient.get_full_name|default:message.recipient.username }}
                            <br>
                            <small class="text-muted">{{ message.recipient.email }}</small>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <strong>تاريخ الإرسال:</strong> {{ message.created_at|date:"Y-m-d H:i" }}
                        </div>
                        {% if message.is_read %}
                            <div class="col-md-6">
                                <strong>تاريخ القراءة:</strong> {{ message.read_at|date:"Y-m-d H:i" }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- محتوى الرسالة -->
                    <div class="border-top pt-4">
                        <h6>محتوى الرسالة:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ message.content|linebreaks }}
                        </div>
                    </div>

                    <!-- إجراءات -->
                    {% if user.user_type == 'doctor' and message.patient %}
                        <div class="border-top pt-4 mt-4">
                            <h6>إجراءات سريعة:</h6>
                            <div class="btn-group" role="group">
                                <a href="{% url 'patients:patient_detail' message.patient.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-user me-1"></i>عرض المريض
                                </a>
                                <a href="{% url 'communications:send_message' message.patient.id %}" class="btn btn-outline-success">
                                    <i class="fas fa-reply me-1"></i>رد على الرسالة
                                </a>
                                <a href="{% url 'nutrition_plans:create_nutrition_plan' message.patient.id %}" class="btn btn-outline-info">
                                    <i class="fas fa-utensils me-1"></i>إنشاء خطة غذائية
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
