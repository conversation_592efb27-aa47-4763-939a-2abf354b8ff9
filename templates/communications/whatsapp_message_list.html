{% extends 'base.html' %}
{% load i18n %}

{% block title %}رسائل الواتساب{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fab fa-whatsapp me-2 text-success"></i>رسائل الواتساب</h2>
                <a href="{% url 'patients:patient_list' %}" class="btn btn-success">
                    <i class="fab fa-whatsapp me-1"></i>إرسال رسالة جديدة
                </a>
            </div>

            <!-- التصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">تصفية حسب الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="sent" {% if status_filter == 'sent' %}selected{% endif %}>مرسلة</option>
                                <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>تم التسليم</option>
                                <option value="read" {% if status_filter == 'read' %}selected{% endif %}>مقروءة</option>
                                <option value="failed" {% if status_filter == 'failed' %}selected{% endif %}>فشل الإرسال</option>
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-filter me-1"></i>تصفية
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة رسائل الواتساب -->
            <div class="card">
                <div class="card-body">
                    {% if whatsapp_messages %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المريض</th>
                                        <th>رقم الهاتف</th>
                                        <th>الرسالة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in whatsapp_messages %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        {{ message.patient.user.first_name|first|upper }}
                                                    </div>
                                                    <div>
                                                        <strong>{{ message.patient.user.get_full_name|default:message.patient.user.username }}</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ message.phone_number }}</td>
                                            <td>
                                                <div class="message-preview">
                                                    {{ message.message_content|truncatewords:15 }}
                                                </div>
                                            </td>
                                            <td>{{ message.sent_at|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                {% if message.status == 'sent' %}
                                                    <span class="badge bg-primary">مرسلة</span>
                                                {% elif message.status == 'delivered' %}
                                                    <span class="badge bg-info">تم التسليم</span>
                                                {% elif message.status == 'read' %}
                                                    <span class="badge bg-success">مقروءة</span>
                                                {% elif message.status == 'failed' %}
                                                    <span class="badge bg-danger">فشل الإرسال</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ message.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'patients:patient_detail' message.patient.id %}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض المريض">
                                                    <i class="fas fa-user"></i>
                                                </a>
                                                {% if message.status == 'failed' %}
                                                    <a href="{% url 'communications:send_whatsapp_message' message.patient.id %}" 
                                                       class="btn btn-sm btn-outline-success" title="إعادة الإرسال">
                                                        <i class="fas fa-redo"></i>
                                                    </a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- الترقيم -->
                        {% if whatsapp_messages.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if whatsapp_messages.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ whatsapp_messages.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in whatsapp_messages.paginator.page_range %}
                                        {% if whatsapp_messages.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if whatsapp_messages.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ whatsapp_messages.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">لا توجد رسائل واتساب</h5>
                            <p class="text-muted">يمكنك إرسال رسالة واتساب جديدة من قائمة المرضى</p>
                            <a href="{% url 'patients:patient_list' %}" class="btn btn-success">
                                <i class="fab fa-whatsapp me-1"></i>إرسال رسالة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}

.message-preview {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
{% endblock %}
