{% extends 'base.html' %}

{% block title %}{{ plan.title }} - تفاصيل الخطة الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-utensils me-2"></i>{{ plan.title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if user.user_type == 'doctor' %}
                <a href="{% url 'nutrition_plans:add_meal' plan.id %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>إضافة وجبة
                </a>
                <a href="#edit-plan" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>تعديل (قريباً)
                </a>
                <a href="{% url 'nutrition_plans:send_plan_whatsapp' plan.id %}" class="btn btn-primary">
                    <i class="fab fa-whatsapp me-2"></i>إرسال واتساب
                </a>
            {% endif %}
        </div>
        <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للخطط
        </a>
    </div>
</div>

<!-- Plan Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-{% if plan.status == 'active' %}success{% elif plan.status == 'inactive' %}warning{% else %}secondary{% endif %} text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الخطة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>{{ plan.patient.user.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>د. {{ plan.doctor.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ البداية:</strong></td>
                                <td>{{ plan.start_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ النهاية:</strong></td>
                                <td>{{ plan.end_date }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'inactive' %}warning{% else %}secondary{% endif %} fs-6">
                                        {{ plan.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>عدد الوجبات:</strong></td>
                                <td>{{ meals.count }} وجبة</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ plan.created_at|date:"Y-m-d" }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ plan.updated_at|date:"Y-m-d" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if plan.instructions %}
                <div class="mt-3">
                    <h6 class="text-success border-bottom pb-2">التعليمات الخاصة:</h6>
                    <p class="text-muted">{{ plan.instructions }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Nutritional Targets -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bullseye me-2"></i>الأهداف الغذائية
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <div class="h4 mb-0 text-primary">{{ plan.target_calories }}</div>
                            <small class="text-muted">سعرة حرارية</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ plan.target_protein }}</div>
                        <small class="text-muted">غ بروتين</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 mb-0 text-warning">{{ plan.target_carbs }}</div>
                            <small class="text-muted">غ كربوهيدرات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-info">{{ plan.target_fat }}</div>
                        <small class="text-muted">غ دهون</small>
                    </div>
                </div>
                
                <hr>
                
                <h6 class="text-primary mb-2">الإجمالي الحالي:</h6>
                <div class="row text-center small">
                    <div class="col-6 mb-2">
                        <div class="border-end">
                            <div class="fw-bold">{{ total_calories|floatformat:0 }}</div>
                            <small class="text-muted">سعرة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="fw-bold">{{ total_protein|floatformat:1 }}</div>
                        <small class="text-muted">غ بروتين</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fw-bold">{{ total_carbs|floatformat:1 }}</div>
                            <small class="text-muted">غ كربوهيدرات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold">{{ total_fat|floatformat:1 }}</div>
                        <small class="text-muted">غ دهون</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Meals -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-utensils me-2"></i>الوجبات اليومية المقترحة
            </h5>
            {% if user.user_type == 'doctor' %}
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>يمكن تعديل جميع الوجبات والأطعمة
            </small>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        {% if user.user_type == 'doctor' %}
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>وجبات مقترحة:</strong> تم إنشاء هذه الوجبات تلقائياً كاقتراح أولي. يمكنك تعديل أي وجبة أو إضافة/حذف أطعمة حسب احتياجات المريض.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        {% if meals %}
            <div class="row">
                {% for meal in meals %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border h-100">
                        <div class="card-header bg-{% if meal.meal_type == 'breakfast' %}warning{% elif meal.meal_type == 'lunch' %}success{% elif meal.meal_type == 'dinner' %}info{% else %}secondary{% endif %} text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">{{ meal.name }}</h6>
                                {% if user.user_type == 'doctor' %}
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'nutrition_plans:edit_meal' meal.id %}" class="btn btn-light btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'nutrition_plans:delete_meal' meal.id %}" class="btn btn-light btn-sm text-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                            <small>{{ meal.get_meal_type_display }} - {{ meal.time_to_eat|time:"H:i" }}</small>
                        </div>
                        <div class="card-body">
                            {% if meal.meal_items.all %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الطعام</th>
                                                <th>الكمية</th>
                                                <th>السعرات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for meal_item in meal.meal_items.all %}
                                            <tr>
                                                <td>{{ meal_item.food.name_ar }}</td>
                                                <td>{{ meal_item.quantity }}غ</td>
                                                <td>{{ meal_item.calories|floatformat:0 }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- إجمالي الوجبة -->
                                <div class="alert alert-info">
                                    <strong>إجمالي الوجبة:</strong> {{ meal.total_calories|floatformat:0 }} سعرة
                                </div>
                                
                                <div class="mt-3 p-2 bg-light rounded">
                                    <div class="row text-center small">
                                        <div class="col-6">
                                            <div class="fw-bold text-primary">{{ meal.total_calories|floatformat:0 }}</div>
                                            <small class="text-muted">سعرة</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="fw-bold text-success">{{ meal.total_protein|floatformat:1 }}</div>
                                            <small class="text-muted">غ بروتين</small>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <p class="text-muted text-center">لا توجد أطعمة في هذه الوجبة</p>
                                {% if user.user_type == 'doctor' %}
                                <div class="text-center">
                                    <a href="{% url 'nutrition_plans:edit_meal' meal.id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>إضافة أطعمة
                                    </a>
                                </div>
                                {% endif %}
                            {% endif %}
                            
                            {% if meal.instructions %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ meal.instructions }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وجبات</h5>
                <p class="text-muted">لم يتم إضافة أي وجبات لهذه الخطة بعد.</p>
                {% if user.user_type == 'doctor' %}
                <a href="{% url 'nutrition_plans:add_meal' plan.id %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>إضافة وجبة
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
