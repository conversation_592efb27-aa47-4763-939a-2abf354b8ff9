{% extends 'base.html' %}

{% block title %}إرسال الخطة عبر الواتساب - {{ plan.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fab fa-whatsapp me-2 text-success"></i>إرسال الخطة عبر الواتساب
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للخطة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fab fa-whatsapp me-2"></i>تأكيد إرسال الخطة الغذائية
                </h5>
            </div>
            <div class="card-body p-4">
                <!-- Plan Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">معلومات الخطة:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>{{ plan.title }}</td>
                            </tr>
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>{{ plan.patient.user.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>فترة الخطة:</strong></td>
                                <td>{{ plan.start_date }} - {{ plan.end_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>السعرات المستهدفة:</strong></td>
                                <td>{{ plan.target_calories }} سعرة</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-success mb-3">معلومات الإرسال:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>رقم الواتساب:</strong></td>
                                <td>
                                    {% if plan.patient.user.whatsapp_number %}
                                        {{ plan.patient.user.whatsapp_number }}
                                    {% elif plan.patient.user.phone_number %}
                                        {{ plan.patient.user.phone_number }}
                                    {% else %}
                                        <span class="text-danger">غير متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حالة الخطة:</strong></td>
                                <td>
                                    <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'inactive' %}warning{% else %}secondary{% endif %}">
                                        {{ plan.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Message Preview -->
                <div class="mb-4">
                    <h6 class="text-info mb-3">معاينة الرسالة:</h6>
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="bg-white rounded p-3 shadow-sm" style="max-width: 400px;">
                                        <div class="small text-muted mb-2">
                                            <i class="fas fa-clock me-1"></i>الآن
                                        </div>
                                        <div style="white-space: pre-line; font-size: 14px;">🌟 خطتك الغذائية الجديدة 🌟

مرحباً {{ plan.patient.user.first_name }}،

تم إنشاء خطة غذائية جديدة لك:

📋 *{{ plan.title }}*

📊 الأهداف اليومية:
• السعرات: {{ plan.target_calories }} سعرة
• البروتين: {{ plan.target_protein }}غ
• الكربوهيدرات: {{ plan.target_carbs }}غ  
• الدهون: {{ plan.target_fat }}غ

📅 فترة الخطة:
من {{ plan.start_date }} إلى {{ plan.end_date }}

{% if plan.instructions %}📝 تعليمات خاصة:
{{ plan.instructions }}

{% endif %}💡 نصائح مهمة:
• اتبع الخطة بدقة للحصول على أفضل النتائج
• اشرب 8-10 أكواب ماء يومياً
• مارس الرياضة بانتظام
• سجل وزنك أسبوعياً

📱 يمكنك الوصول للخطة التفصيلية عبر تطبيق العيادة

🩺 د. {{ plan.doctor.get_full_name }}
{% if plan.doctor.doctor_profile %}{{ plan.doctor.doctor_profile.clinic_name }}{% endif %}

للاستفسارات، تواصل معنا على نفس هذا الرقم.

نتمنى لك رحلة صحية موفقة! 💪</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Confirmation -->
                {% if plan.patient.user.whatsapp_number or plan.patient.user.phone_number %}
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تأكيد الإرسال:</strong> سيتم إرسال الخطة الغذائية إلى رقم الواتساب المسجل للمريض.
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>إرسال عبر الواتساب
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>خطأ:</strong> لا يوجد رقم واتساب مسجل للمريض. يرجى تحديث بيانات المريض أولاً.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'patients:edit_patient' plan.patient.id %}" class="btn btn-warning me-md-2">
                            <i class="fas fa-edit me-2"></i>تحديث بيانات المريض
                        </a>
                        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للخطة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 1rem;
}

.bg-light .card {
    border-radius: 0.5rem;
}

.whatsapp-message {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}
</style>
{% endblock %}
