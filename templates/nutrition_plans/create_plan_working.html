{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إنشاء خطة غذائية{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>إنشاء خطة غذائية جديدة</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>بيانات الخطة الغذائية</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label class="form-label">المريض *</label>
                            <select name="patient" class="form-select" required id="patient-select">
                                <option value="">-- اختر مريض --</option>
                                {% for p in patients_debug %}
                                    <option value="{{ p.id }}">
                                        {{ p.user.first_name }} {{ p.user.last_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">عنوان الخطة *</label>
                            <input type="text" name="title" class="form-control" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ البداية *</label>
                                <input type="date" name="start_date" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ النهاية *</label>
                                <input type="date" name="end_date" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">السعرات الحرارية *</label>
                                <input type="number" name="target_calories" class="form-control" value="2000" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البروتين (غ) *</label>
                                <input type="number" name="target_protein" class="form-control" value="150" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكربوهيدرات (غ) *</label>
                                <input type="number" name="target_carbs" class="form-control" value="250" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الدهون (غ) *</label>
                                <input type="number" name="target_fat" class="form-control" value="65" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">حالة الخطة *</label>
                            <select name="status" class="form-select" required>
                                <option value="active">نشطة</option>
                                <option value="draft">مسودة</option>
                                <option value="completed">مكتملة</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">تعليمات خاصة</label>
                            <textarea name="instructions" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                            <button type="submit" class="btn btn-success">إنشاء الخطة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6>معلومات المريض</h6>
                </div>
                <div class="card-body" id="patient-info">
                    <h6 id="patient-name">اختر مريض</h6>
                    <p><strong>الجنس:</strong> <span id="patient-gender">غير محدد</span></p>
                    <p><strong>الطول:</strong> <span id="patient-height">غير محدد</span> سم</p>
                    <p><strong>الوزن:</strong> <span id="patient-weight">غير محدد</span> كغ</p>
                    <p><strong>الهدف:</strong> <span id="patient-goal">غير محدد</span></p>
                </div>
            </div>
            
            <!-- معلومات تصحيح -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>معلومات التصحيح</h6>
                </div>
                <div class="card-body">
                    <p><strong>عدد المرضى:</strong> {{ patients_count }}</p>
                    {% if patients_debug %}
                        <p><strong>المرضى المتاحون:</strong></p>
                        <ul>
                            {% for p in patients_debug %}
                                <li>{{ p.user.first_name }} {{ p.user.last_name }} ({{ p.user.username }})</li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-danger">لا توجد مرضى متاحين</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patientSelect = document.getElementById('patient-select');
    
    // بيانات المرضى
    const patientsData = {
        {% for p in patients_debug %}
        '{{ p.id }}': {
            'name': '{{ p.user.first_name }} {{ p.user.last_name }}',
            'gender': '{{ p.get_gender_display|default:"غير محدد" }}',
            'height': '{{ p.height|default:"غير محدد" }}',
            'weight': '{{ p.current_weight|default:"غير محدد" }}',
            'goal': '{{ p.get_health_goal_display|default:"غير محدد" }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };
    
    console.log('بيانات المرضى:', patientsData);
    
    if (patientSelect) {
        patientSelect.addEventListener('change', function() {
            const selectedPatientId = this.value;
            console.log('تم اختيار المريض:', selectedPatientId);
            
            if (selectedPatientId && patientsData[selectedPatientId]) {
                const patient = patientsData[selectedPatientId];
                console.log('بيانات المريض:', patient);
                
                document.getElementById('patient-name').textContent = patient.name;
                document.getElementById('patient-gender').textContent = patient.gender;
                document.getElementById('patient-height').textContent = patient.height;
                document.getElementById('patient-weight').textContent = patient.weight;
                document.getElementById('patient-goal').textContent = patient.goal;
            } else {
                document.getElementById('patient-name').textContent = 'اختر مريض';
                document.getElementById('patient-gender').textContent = 'غير محدد';
                document.getElementById('patient-height').textContent = 'غير محدد';
                document.getElementById('patient-weight').textContent = 'غير محدد';
                document.getElementById('patient-goal').textContent = 'غير محدد';
            }
        });
    }
});
</script>
{% endblock %}
