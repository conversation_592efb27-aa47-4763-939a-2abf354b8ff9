{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تعديل الوجبة - {{ meal.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>تعديل الوجبة - {{ meal.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'nutrition_plans:nutrition_plan_detail' meal.nutrition_plan.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للخطة
        </a>
    </div>
</div>

<div class="row">
    <!-- Meal Details -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-utensils me-2"></i>بيانات الوجبة
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ meal_form.name.id_for_label }}" class="form-label">اسم الوجبة</label>
                        {{ meal_form.name|add_class:"form-control" }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ meal_form.meal_type.id_for_label }}" class="form-label">نوع الوجبة</label>
                            {{ meal_form.meal_type|add_class:"form-select" }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ meal_form.day_number.id_for_label }}" class="form-label">رقم اليوم</label>
                            {{ meal_form.day_number|add_class:"form-control" }}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ meal_form.time_to_eat.id_for_label }}" class="form-label">وقت التناول</label>
                        {{ meal_form.time_to_eat|add_class:"form-control" }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ meal_form.instructions.id_for_label }}" class="form-label">تعليمات التحضير</label>
                        {{ meal_form.instructions|add_class:"form-control" }}
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Add Food -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>إضافة طعام للوجبة
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="food_id" class="form-label">اختر الطعام</label>
                        <select name="food_id" id="food_id" class="form-select" required>
                            <option value="">-- اختر طعام --</option>
                            {% for food in foods %}
                            <option value="{{ food.id }}" data-calories="{{ food.calories_per_100g }}" data-protein="{{ food.protein_per_100g }}" data-carbs="{{ food.carbs_per_100g }}" data-fat="{{ food.fat_per_100g }}">
                                {{ food.name_ar }} ({{ food.calories_per_100g|floatformat:0 }} سعرة/100غ)
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">الكمية (غرام)</label>
                        <input type="number" name="quantity" id="quantity" class="form-control" min="1" max="2000" step="1" required>
                        <div class="form-text">
                            💡 كميات مقترحة: 
                            <span class="badge bg-light text-dark me-1" onclick="setQuantity(50)" style="cursor: pointer;">50غ</span>
                            <span class="badge bg-light text-dark me-1" onclick="setQuantity(100)" style="cursor: pointer;">100غ</span>
                            <span class="badge bg-light text-dark me-1" onclick="setQuantity(150)" style="cursor: pointer;">150غ</span>
                            <span class="badge bg-light text-dark me-1" onclick="setQuantity(200)" style="cursor: pointer;">200غ</span>
                        </div>
                    </div>
                    
                    <!-- معاينة القيم الغذائية -->
                    <div id="nutrition-preview" class="mb-3" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator me-2"></i>معاينة القيم الغذائية</h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="fw-bold text-primary" id="preview-calories">0</div>
                                    <small>سعرة</small>
                                </div>
                                <div class="col-3">
                                    <div class="fw-bold text-success" id="preview-protein">0</div>
                                    <small>غ بروتين</small>
                                </div>
                                <div class="col-3">
                                    <div class="fw-bold text-warning" id="preview-carbs">0</div>
                                    <small>غ كربوهيدرات</small>
                                </div>
                                <div class="col-3">
                                    <div class="fw-bold text-info" id="preview-fat">0</div>
                                    <small>غ دهون</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <input type="text" name="notes" id="notes" class="form-control" placeholder="ملاحظات اختيارية">
                    </div>
                    
                    <button type="submit" name="add_food" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة الطعام
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Current Meal Items -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>أطعمة الوجبة الحالية
        </h5>
    </div>
    <div class="card-body">
        {% if meal_items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الطعام</th>
                            <th>الكمية</th>
                            <th>السعرات</th>
                            <th>البروتين</th>
                            <th>الكربوهيدرات</th>
                            <th>الدهون</th>
                            <th>ملاحظات</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in meal_items %}
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ item.food.name_ar }}</div>
                                    <small class="text-muted">{{ item.food.name_en }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold">{{ item.quantity }}</span> غ
                            </td>
                            <td>
                                <span class="text-primary fw-bold">{{ item.calories|floatformat:1 }}</span>
                                <small class="text-muted">سعرة</small>
                            </td>
                            <td>
                                <span class="text-success fw-bold">{{ item.protein|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <span class="text-warning fw-bold">{{ item.carbs|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <span class="text-info fw-bold">{{ item.fat|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                {% if item.notes %}
                                    <small class="text-muted">{{ item.notes }}</small>
                                {% else %}
                                    <span class="text-muted">--</span>
                                {% endif %}
                            </td>
                            <td>
                                <form method="post" style="display: inline;">
                                    {% csrf_token %}
                                    <input type="hidden" name="item_id" value="{{ item.id }}">
                                    <button type="submit" name="delete_item" class="btn btn-outline-danger btn-sm" 
                                            onclick="return confirm('هل أنت متأكد من حذف هذا الطعام؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th>الإجمالي</th>
                            <th>--</th>
                            <th>
                                <span class="text-primary fw-bold">{{ meal.total_calories|floatformat:1 }}</span>
                                <small class="text-muted">سعرة</small>
                            </th>
                            <th>
                                <span class="text-success fw-bold">{{ meal.total_protein|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </th>
                            <th>
                                <span class="text-warning fw-bold">{{ meal.total_carbs|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </th>
                            <th>
                                <span class="text-info fw-bold">{{ meal.total_fat|floatformat:1 }}</span>
                                <small class="text-muted">غ</small>
                            </th>
                            <th>--</th>
                            <th>--</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أطعمة في هذه الوجبة</h5>
                <p class="text-muted">ابدأ بإضافة أطعمة للوجبة من القائمة أعلاه.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
// دالة لتحديد الكمية
function setQuantity(amount) {
    document.getElementById('quantity').value = amount;
    updateNutritionPreview();
}

// دالة لتحديث معاينة القيم الغذائية
function updateNutritionPreview() {
    const foodSelect = document.getElementById('food_id');
    const quantityInput = document.getElementById('quantity');
    const selectedOption = foodSelect.options[foodSelect.selectedIndex];
    const preview = document.getElementById('nutrition-preview');
    
    if (selectedOption && selectedOption.value && quantityInput.value) {
        const quantity = parseFloat(quantityInput.value) || 0;
        const calories = parseFloat(selectedOption.dataset.calories) || 0;
        const protein = parseFloat(selectedOption.dataset.protein) || 0;
        const carbs = parseFloat(selectedOption.dataset.carbs) || 0;
        const fat = parseFloat(selectedOption.dataset.fat) || 0;
        
        // حساب القيم للكمية المحددة
        const totalCalories = (calories * quantity / 100).toFixed(1);
        const totalProtein = (protein * quantity / 100).toFixed(1);
        const totalCarbs = (carbs * quantity / 100).toFixed(1);
        const totalFat = (fat * quantity / 100).toFixed(1);
        
        // تحديث العرض
        document.getElementById('preview-calories').textContent = totalCalories;
        document.getElementById('preview-protein').textContent = totalProtein;
        document.getElementById('preview-carbs').textContent = totalCarbs;
        document.getElementById('preview-fat').textContent = totalFat;
        
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

// إضافة مستمعي الأحداث
document.getElementById('food_id').addEventListener('change', updateNutritionPreview);
document.getElementById('quantity').addEventListener('input', updateNutritionPreview);
</script>
{% endblock %}
