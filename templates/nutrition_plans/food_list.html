{% extends 'base.html' %}

{% block title %}قائمة الأطعمة - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-apple-alt me-2"></i>قائمة الأطعمة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="#add-food" class="btn btn-success me-2">
            <i class="fas fa-plus me-2"></i>إضافة طعام جديد
        </a>
        <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-utensils me-2"></i>الخطط الغذائية
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                {{ search_form.search }}
            </div>
            <div class="col-md-4">
                {{ search_form.category }}
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Foods List -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>قائمة الأطعمة ({{ foods.paginator.count }} طعام)
        </h5>
    </div>
    <div class="card-body p-0">
        {% if foods %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الطعام</th>
                            <th>الفئة</th>
                            <th>السعرات/100غ</th>
                            <th>البروتين/100غ</th>
                            <th>الكربوهيدرات/100غ</th>
                            <th>الدهون/100غ</th>
                            <th>الألياف/100غ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for food in foods %}
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ food.name_ar }}</div>
                                    <small class="text-muted">{{ food.name_en }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{% if food.category == 'protein' %}primary{% elif food.category == 'grains' %}warning{% elif food.category == 'vegetables' %}success{% elif food.category == 'fruits' %}info{% elif food.category == 'dairy' %}secondary{% else %}dark{% endif %}">
                                    {{ food.get_category_display }}
                                </span>
                            </td>
                            <td>
                                <span class="fw-bold text-primary">{{ food.calories_per_100g }}</span>
                                <small class="text-muted">سعرة</small>
                            </td>
                            <td>
                                <span class="fw-bold text-success">{{ food.protein_per_100g }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <span class="fw-bold text-warning">{{ food.carbs_per_100g }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <span class="fw-bold text-info">{{ food.fat_per_100g }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <span class="fw-bold text-secondary">{{ food.fiber_per_100g }}</span>
                                <small class="text-muted">غ</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="#view-food" class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#edit-food" class="btn btn-outline-warning btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#delete-food" class="btn btn-outline-danger btn-sm" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if foods.has_other_pages %}
                <div class="card-footer bg-white border-top-0">
                    <nav aria-label="تنقل الأطعمة">
                        <ul class="pagination justify-content-center mb-0">
                            {% if foods.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ foods.previous_page_number }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for num in foods.paginator.page_range %}
                                {% if foods.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > foods.number|add:'-3' and num < foods.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if foods.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ foods.next_page_number }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-apple-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أطعمة</h5>
                <p class="text-muted">لم يتم العثور على أي أطعمة. ابدأ بإضافة أطعمة جديدة.</p>
                <a href="#add-food" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>إضافة طعام جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-apple-alt fa-2x mb-2"></i>
                <h4>{{ foods.paginator.count }}</h4>
                <small>إجمالي الأطعمة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-leaf fa-2x mb-2"></i>
                <h4>{{ foods.paginator.count }}</h4>
                <small>خضروات وفواكه</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-drumstick-bite fa-2x mb-2"></i>
                <h4>{{ foods.paginator.count }}</h4>
                <small>بروتينات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-bread-slice fa-2x mb-2"></i>
                <h4>{{ foods.paginator.count }}</h4>
                <small>حبوب ونشويات</small>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
