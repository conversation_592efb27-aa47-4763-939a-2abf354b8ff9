<!DOCTYPE html>
<html>
<head>
    <title>اختبار المرضى</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; }
        select { padding: 10px; font-size: 16px; width: 300px; }
        .patient-info { background: #e8f4fd; padding: 15px; margin: 10px 0; border: 1px solid #007bff; }
    </style>
</head>
<body>
    <h1>اختبار عرض المرضى</h1>
    
    <div class="debug">
        <h3>معلومات التصحيح:</h3>
        <p><strong>عدد المرضى:</strong> {{ patients_count }}</p>
        <p><strong>patients_debug موجود؟</strong> {% if patients_debug %}نعم{% else %}لا{% endif %}</p>
        <p><strong>المريض المحدد:</strong> {% if patient %}{{ patient.user.get_full_name }} (ID: {{ patient.id }}){% else %}لا يوجد{% endif %}</p>
    </div>
    
    <div class="debug">
        <h3>قائمة المرضى من patients_debug:</h3>
        {% if patients_debug %}
            {% for p in patients_debug %}
                <p>{{ forloop.counter }}. ID: {{ p.id }} - الاسم: "{{ p.user.get_full_name }}" - المستخدم: "{{ p.user.username }}" - الاسم الأول: "{{ p.user.first_name }}" - الاسم الأخير: "{{ p.user.last_name }}"</p>
            {% endfor %}
        {% else %}
            <p style="color: red;">لا توجد مرضى في patients_debug!</p>
        {% endif %}
    </div>
    
    <h3>القائمة المنسدلة:</h3>
    <select id="patient-select" onchange="updatePatientInfo()">
        <option value="">-- اختر مريض --</option>
        {% if patients_debug %}
            {% for p in patients_debug %}
                <option value="{{ p.id }}" data-name="{{ p.user.get_full_name|default:p.user.username }}" data-gender="{{ p.get_gender_display|default:'غير محدد' }}" data-height="{{ p.height|default:'غير محدد' }}" data-weight="{{ p.current_weight|default:'غير محدد' }}" data-goal="{{ p.get_health_goal_display|default:'غير محدد' }}">
                    {% if p.user.first_name and p.user.last_name %}
                        {{ p.user.first_name }} {{ p.user.last_name }}
                    {% elif p.user.first_name %}
                        {{ p.user.first_name }}
                    {% else %}
                        {{ p.user.username }}
                    {% endif %}
                </option>
            {% endfor %}
        {% else %}
            <option value="" disabled>لا توجد مرضى متاحين</option>
        {% endif %}
    </select>
    
    <div class="patient-info" id="patient-info">
        <h3>معلومات المريض المختار:</h3>
        <p><strong>الاسم:</strong> <span id="selected-name">لم يتم الاختيار</span></p>
        <p><strong>الجنس:</strong> <span id="selected-gender">غير محدد</span></p>
        <p><strong>الطول:</strong> <span id="selected-height">غير محدد</span> سم</p>
        <p><strong>الوزن:</strong> <span id="selected-weight">غير محدد</span> كغ</p>
        <p><strong>الهدف:</strong> <span id="selected-goal">غير محدد</span></p>
    </div>
    
    <script>
    function updatePatientInfo() {
        const select = document.getElementById('patient-select');
        const selectedOption = select.options[select.selectedIndex];
        
        if (selectedOption.value) {
            document.getElementById('selected-name').textContent = selectedOption.dataset.name;
            document.getElementById('selected-gender').textContent = selectedOption.dataset.gender;
            document.getElementById('selected-height').textContent = selectedOption.dataset.height;
            document.getElementById('selected-weight').textContent = selectedOption.dataset.weight;
            document.getElementById('selected-goal').textContent = selectedOption.dataset.goal;
        } else {
            document.getElementById('selected-name').textContent = 'لم يتم الاختيار';
            document.getElementById('selected-gender').textContent = 'غير محدد';
            document.getElementById('selected-height').textContent = 'غير محدد';
            document.getElementById('selected-weight').textContent = 'غير محدد';
            document.getElementById('selected-goal').textContent = 'غير محدد';
        }
    }
    
    console.log('عدد الخيارات في القائمة:', document.getElementById('patient-select').options.length);
    </script>
</body>
</html>
