{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إنشاء خطة غذائية - {{ patient.user.get_full_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-utensils me-2"></i>إنشاء خطة غذائية جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للخطط
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-utensils me-2"></i>بيانات الخطة الغذائية
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post" action="">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">عنوان الخطة *</label>
                            {{ form.title|add_class:"form-control" }}
                            {% if form.title.errors %}
                                <div class="text-danger small mt-1">{{ form.title.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.patient.id_for_label }}" class="form-label">المريض *</label>
                            {{ form.patient|add_class:"form-select" }}
                            {% if form.patient.errors %}
                                <div class="text-danger small mt-1">{{ form.patient.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية *</label>
                            {{ form.start_date|add_class:"form-control" }}
                            {% if form.start_date.errors %}
                                <div class="text-danger small mt-1">{{ form.start_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية *</label>
                            {{ form.end_date|add_class:"form-control" }}
                            {% if form.end_date.errors %}
                                <div class="text-danger small mt-1">{{ form.end_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">حالة الخطة *</label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="text-danger small mt-1">{{ form.status.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Nutritional Targets -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-bullseye me-2"></i>الأهداف الغذائية اليومية
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_calories.id_for_label }}" class="form-label">السعرات الحرارية *</label>
                            <div class="input-group">
                                {{ form.target_calories|add_class:"form-control" }}
                                <span class="input-group-text">سعرة</span>
                            </div>
                            {% if form.target_calories.errors %}
                                <div class="text-danger small mt-1">{{ form.target_calories.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_protein.id_for_label }}" class="form-label">البروتين *</label>
                            <div class="input-group">
                                {{ form.target_protein|add_class:"form-control" }}
                                <span class="input-group-text">غرام</span>
                            </div>
                            {% if form.target_protein.errors %}
                                <div class="text-danger small mt-1">{{ form.target_protein.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_carbs.id_for_label }}" class="form-label">الكربوهيدرات *</label>
                            <div class="input-group">
                                {{ form.target_carbs|add_class:"form-control" }}
                                <span class="input-group-text">غرام</span>
                            </div>
                            {% if form.target_carbs.errors %}
                                <div class="text-danger small mt-1">{{ form.target_carbs.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_fat.id_for_label }}" class="form-label">الدهون *</label>
                            <div class="input-group">
                                {{ form.target_fat|add_class:"form-control" }}
                                <span class="input-group-text">غرام</span>
                            </div>
                            {% if form.target_fat.errors %}
                                <div class="text-danger small mt-1">{{ form.target_fat.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-clipboard-list me-2"></i>التعليمات والملاحظات
                            </h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.instructions.id_for_label }}" class="form-label">تعليمات خاصة</label>
                            {{ form.instructions|add_class:"form-control" }}
                            {% if form.instructions.errors %}
                                <div class="text-danger small mt-1">{{ form.instructions.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">أضف أي تعليمات خاصة أو ملاحظات للمريض</div>
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success" onclick="console.log('تم النقر على زر الإرسال');">
                            <i class="fas fa-save me-2"></i>إنشاء الخطة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Patient Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>معلومات المريض
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if patient.user.profile_picture %}
                        <img src="{{ patient.user.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="صورة المريض">
                    {% else %}
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ patient.user.first_name|first }}
                        </div>
                    {% endif %}
                    <h6 class="mt-2 mb-0">{{ patient.user.get_full_name }}</h6>
                    <small class="text-muted">{{ patient.user.get_user_type_display }}</small>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>
                            {% if patient.user.date_of_birth %}
                                {{ patient.user.date_of_birth|date:"Y-m-d" }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>{{ patient.get_gender_display|default:"غير محدد" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الطول:</strong></td>
                        <td>{{ patient.height|default:"غير محدد" }} سم</td>
                    </tr>
                    <tr>
                        <td><strong>الوزن الحالي:</strong></td>
                        <td>{{ patient.current_weight|default:"غير محدد" }} كغ</td>
                    </tr>
                    <tr>
                        <td><strong>الهدف الصحي:</strong></td>
                        <td>{{ patient.get_health_goal_display|default:"غير محدد" }}</td>
                    </tr>
                </table>
                
                <div class="d-grid">
                    <a href="{% url 'patients:patient_detail' patient.id %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض الملف الكامل
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Nutrition Tips -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>نصائح غذائية
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        احرص على توزيع السعرات على مدار اليوم
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اشرب 8-10 أكواب ماء يومياً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تناول البروتين في كل وجبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أكثر من الخضروات والفواكه
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        تجنب السكريات المضافة
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('النموذج يتم إرساله...');

            // التحقق من الحقول المطلوبة
            const title = document.querySelector('input[name="title"]');
            const patient = document.querySelector('select[name="patient"]');
            const startDate = document.querySelector('input[name="start_date"]');
            const endDate = document.querySelector('input[name="end_date"]');

            if (!title.value.trim()) {
                alert('يرجى إدخال عنوان الخطة');
                e.preventDefault();
                return false;
            }

            if (!patient.value) {
                alert('يرجى اختيار المريض');
                e.preventDefault();
                return false;
            }

            if (!startDate.value) {
                alert('يرجى إدخال تاريخ البداية');
                e.preventDefault();
                return false;
            }

            if (!endDate.value) {
                alert('يرجى إدخال تاريخ النهاية');
                e.preventDefault();
                return false;
            }

            console.log('جميع الحقول صحيحة، سيتم إرسال النموذج');
        });
    }
});
</script>
{% endblock %}
