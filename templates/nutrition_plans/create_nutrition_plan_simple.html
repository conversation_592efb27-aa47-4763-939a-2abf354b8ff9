{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إنشاء خطة غذائية - {{ patient.user.get_full_name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>إنشاء خطة غذائية جديدة</h1>
    <p>للمريض: {{ patient.user.get_full_name }}</p>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>بيانات الخطة الغذائية</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">عنوان الخطة *</label>
                            {{ form.title|add_class:"form-control" }}
                            {% if form.title.errors %}
                                <div class="text-danger">{{ form.title.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.patient.id_for_label }}" class="form-label">المريض *</label>

                            <!-- نموذج مخصص لعرض المرضى -->
                            <select name="patient" class="form-select" required id="patient-select">
                                <option value="">-- اختر مريض --</option>
                                {% if patients_debug %}
                                    {% for p in patients_debug %}
                                        <option value="{{ p.id }}" {% if patient and patient.id == p.id %}selected{% endif %}>
                                            {% if p.user.first_name and p.user.last_name %}
                                                {{ p.user.first_name }} {{ p.user.last_name }}
                                            {% elif p.user.first_name %}
                                                {{ p.user.first_name }}
                                            {% else %}
                                                {{ p.user.username }}
                                            {% endif %}
                                        </option>
                                    {% endfor %}
                                {% else %}
                                    <option value="" disabled>لا توجد مرضى متاحين</option>
                                {% endif %}
                            </select>

                            {% if form.patient.errors %}
                                <div class="text-danger">{{ form.patient.errors.0 }}</div>
                            {% endif %}

                            <!-- معلومات تصحيح -->
                            <div class="mt-2 p-2 bg-light border rounded">
                                <small>
                                    <strong>تصحيح:</strong><br>
                                    عدد المرضى: {{ patients_count }}<br>
                                    patients_debug موجود؟ {% if patients_debug %}نعم{% else %}لا{% endif %}<br>
                                    {% if patients_debug %}
                                        المرضى:
                                        {% for p in patients_debug %}
                                            <br>{{ forloop.counter }}. ID: {{ p.id }} - الاسم: "{{ p.user.get_full_name }}" - المستخدم: "{{ p.user.username }}"
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-danger">لا توجد مرضى في patients_debug!</span>
                                    {% endif %}
                                </small>
                            </div>




                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية *</label>
                                {{ form.start_date|add_class:"form-control" }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية *</label>
                                {{ form.end_date|add_class:"form-control" }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.target_calories.id_for_label }}" class="form-label">السعرات الحرارية *</label>
                                {{ form.target_calories|add_class:"form-control" }}
                                {% if form.target_calories.errors %}
                                    <div class="text-danger">{{ form.target_calories.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.target_protein.id_for_label }}" class="form-label">البروتين *</label>
                                {{ form.target_protein|add_class:"form-control" }}
                                {% if form.target_protein.errors %}
                                    <div class="text-danger">{{ form.target_protein.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.target_carbs.id_for_label }}" class="form-label">الكربوهيدرات *</label>
                                {{ form.target_carbs|add_class:"form-control" }}
                                {% if form.target_carbs.errors %}
                                    <div class="text-danger">{{ form.target_carbs.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.target_fat.id_for_label }}" class="form-label">الدهون *</label>
                                {{ form.target_fat|add_class:"form-control" }}
                                {% if form.target_fat.errors %}
                                    <div class="text-danger">{{ form.target_fat.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">حالة الخطة *</label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="text-danger">{{ form.status.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.instructions.id_for_label }}" class="form-label">تعليمات خاصة</label>
                            {{ form.instructions|add_class:"form-control" }}
                            {% if form.instructions.errors %}
                                <div class="text-danger">{{ form.instructions.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                            <button type="submit" class="btn btn-success">إنشاء الخطة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6>معلومات المريض</h6>
                </div>
                <div class="card-body" id="patient-info">
                    <div id="patient-details">
                        <h6 id="patient-name">{{ patient.user.get_full_name|default:"اختر مريض" }}</h6>
                        <p><strong>الجنس:</strong> <span id="patient-gender">{{ patient.get_gender_display|default:"غير محدد" }}</span></p>
                        <p><strong>الطول:</strong> <span id="patient-height">{{ patient.height|default:"غير محدد" }}</span> سم</p>
                        <p><strong>الوزن:</strong> <span id="patient-weight">{{ patient.current_weight|default:"غير محدد" }}</span> كغ</p>
                        <p><strong>الهدف:</strong> <span id="patient-goal">{{ patient.get_health_goal_display|default:"غير محدد" }}</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patientSelect = document.getElementById('patient-select');

    // بيانات المرضى
    const patientsData = {
        {% for p in patients_debug %}
        '{{ p.id }}': {
            'name': '{{ p.user.get_full_name|default:p.user.username }}',
            'gender': '{{ p.get_gender_display|default:"غير محدد" }}',
            'height': '{{ p.height|default:"غير محدد" }}',
            'weight': '{{ p.current_weight|default:"غير محدد" }}',
            'goal': '{{ p.get_health_goal_display|default:"غير محدد" }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };

    // تحديث بيانات المريض عند الاختيار
    if (patientSelect) {
        console.log('تم العثور على قائمة المرضى');
        console.log('بيانات المرضى:', patientsData);

        patientSelect.addEventListener('change', function() {
            const selectedPatientId = this.value;
            console.log('تم اختيار المريض:', selectedPatientId);

            if (selectedPatientId) {
                // استخدام البيانات المحلية أولاً للاستجابة السريعة
                if (patientsData[selectedPatientId]) {
                    const patient = patientsData[selectedPatientId];
                    console.log('بيانات المريض المحلية:', patient);
                    updatePatientInfo(patient);
                }

                // جلب البيانات المحدثة عبر AJAX
                fetch(`/nutrition/get-patient-info/${selectedPatientId}/`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('بيانات المريض من الخادم:', data);
                        if (!data.error) {
                            updatePatientInfo(data);
                        }
                    })
                    .catch(error => {
                        console.log('خطأ في جلب بيانات المريض:', error);
                        // الاعتماد على البيانات المحلية في حالة الخطأ
                        if (patientsData[selectedPatientId]) {
                            updatePatientInfo(patientsData[selectedPatientId]);
                        }
                    });
            } else {
                console.log('لم يتم اختيار مريض');
                // إعادة تعيين القيم الافتراضية
                updatePatientInfo({
                    name: 'اختر مريض',
                    gender: 'غير محدد',
                    height: 'غير محدد',
                    weight: 'غير محدد',
                    goal: 'غير محدد'
                });
            }
        });
    } else {
        console.log('لم يتم العثور على قائمة المرضى');
    }

    // دالة لتحديث معلومات المريض
    function updatePatientInfo(patient) {
        console.log('تحديث معلومات المريض:', patient);

        const nameElement = document.getElementById('patient-name');
        const genderElement = document.getElementById('patient-gender');
        const heightElement = document.getElementById('patient-height');
        const weightElement = document.getElementById('patient-weight');
        const goalElement = document.getElementById('patient-goal');

        if (nameElement) nameElement.textContent = patient.name;
        if (genderElement) genderElement.textContent = patient.gender;
        if (heightElement) heightElement.textContent = patient.height;
        if (weightElement) weightElement.textContent = patient.weight;
        if (goalElement) goalElement.textContent = patient.goal;

        console.log('تم تحديث العناصر');
    }
});
</script>
{% endblock %}
