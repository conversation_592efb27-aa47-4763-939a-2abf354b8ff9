{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة وجبة - {{ plan.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>إضافة وجبة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للخطة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-utensils me-2"></i>بيانات الوجبة
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>معلومات الوجبة
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">اسم الوجبة *</label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.meal_type.id_for_label }}" class="form-label">نوع الوجبة *</label>
                            {{ form.meal_type|add_class:"form-select" }}
                            {% if form.meal_type.errors %}
                                <div class="text-danger small mt-1">{{ form.meal_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.day_number.id_for_label }}" class="form-label">رقم اليوم *</label>
                            {{ form.day_number|add_class:"form-control" }}
                            {% if form.day_number.errors %}
                                <div class="text-danger small mt-1">{{ form.day_number.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">اليوم في الخطة الغذائية (1-365)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.time_to_eat.id_for_label }}" class="form-label">وقت تناول الوجبة</label>
                            {{ form.time_to_eat|add_class:"form-control" }}
                            {% if form.time_to_eat.errors %}
                                <div class="text-danger small mt-1">{{ form.time_to_eat.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">الوقت المقترح لتناول الوجبة</div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-clipboard-list me-2"></i>تعليمات التحضير
                            </h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.instructions.id_for_label }}" class="form-label">تعليمات التحضير</label>
                            {{ form.instructions|add_class:"form-control" }}
                            {% if form.instructions.errors %}
                                <div class="text-danger small mt-1">{{ form.instructions.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">أضف تعليمات تحضير الوجبة أو ملاحظات مهمة</div>
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>إضافة الوجبة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Plan Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-utensils me-2"></i>معلومات الخطة
                </h6>
            </div>
            <div class="card-body">
                <h6 class="fw-bold">{{ plan.title }}</h6>
                <p class="text-muted mb-2">{{ plan.patient.user.get_full_name }}</p>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>من:</strong></td>
                        <td>{{ plan.start_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <td><strong>إلى:</strong></td>
                        <td>{{ plan.end_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <td><strong>السعرات المستهدفة:</strong></td>
                        <td>{{ plan.target_calories }} سعرة</td>
                    </tr>
                    <tr>
                        <td><strong>البروتين المستهدف:</strong></td>
                        <td>{{ plan.target_protein }} غ</td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'inactive' %}warning{% else %}secondary{% endif %}">
                                {{ plan.get_status_display }}
                            </span>
                        </td>
                    </tr>
                </table>
                
                <div class="d-grid">
                    <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض الخطة الكاملة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Meal Types Guide -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>أنواع الوجبات
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-sun text-warning me-2"></i>
                        <strong>إفطار:</strong> 7:00 - 9:00 ص
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-cookie-bite text-info me-2"></i>
                        <strong>وجبة خفيفة صباحية:</strong> 10:00 - 11:00 ص
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-utensils text-primary me-2"></i>
                        <strong>غداء:</strong> 12:00 - 2:00 م
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-apple-alt text-success me-2"></i>
                        <strong>وجبة خفيفة بعد الظهر:</strong> 3:00 - 4:00 م
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-moon text-dark me-2"></i>
                        <strong>عشاء:</strong> 6:00 - 8:00 م
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-cookie text-secondary me-2"></i>
                        <strong>وجبة خفيفة مسائية:</strong> 9:00 - 10:00 م
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>نصائح للوجبات
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر أسماء واضحة للوجبات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد الوقت المناسب لكل وجبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف تعليمات تحضير مفصلة
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        راعي توزيع الوجبات على مدار اليوم
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
