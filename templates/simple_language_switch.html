<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير اللغة - الحل البسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .language-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .language-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .language-btn.active {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h3>🌐 اختيار اللغة / Language Selection</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>اللغة الحالية / Current Language:</strong> 
                            <span id="current-lang">العربية (ar)</span>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-md-4">
                                <button onclick="switchLanguage('ar')" 
                                        class="language-btn btn btn-outline-primary btn-lg w-100 h-100" 
                                        data-lang="ar">
                                    <div class="text-center">
                                        <div style="font-size: 3rem;">🇮🇶</div>
                                        <h5>العربية</h5>
                                        <small>Arabic</small>
                                    </div>
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="switchLanguage('en')" 
                                        class="language-btn btn btn-outline-success btn-lg w-100 h-100" 
                                        data-lang="en">
                                    <div class="text-center">
                                        <div style="font-size: 3rem;">🇺🇸</div>
                                        <h5>English</h5>
                                        <small>الإنجليزية</small>
                                    </div>
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button onclick="switchLanguage('ku')" 
                                        class="language-btn btn btn-outline-warning btn-lg w-100 h-100" 
                                        data-lang="ku">
                                    <div class="text-center">
                                        <div style="font-size: 3rem;">🟡🔴🟢</div>
                                        <h5>کوردی</h5>
                                        <small>Kurdish</small>
                                    </div>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <div class="alert alert-light">
                                <strong>معلومات تقنية / Technical Info:</strong><br>
                                Cookie: <code id="cookie-info">جاري التحقق...</code><br>
                                LocalStorage: <code id="storage-info">جاري التحقق...</code>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="/doctor/dashboard/" class="btn btn-secondary">
                                العودة إلى لوحة التحكم / Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات اللغة
        const languages = {
            'ar': { name: 'العربية', code: 'ar', dir: 'rtl' },
            'en': { name: 'English', code: 'en', dir: 'ltr' },
            'ku': { name: 'کوردی', code: 'ku', dir: 'rtl' }
        };

        // دالة تغيير اللغة
        function switchLanguage(langCode) {
            console.log('تغيير اللغة إلى:', langCode);
            
            // تحديث الواجهة فوراً
            updateUI(langCode);
            
            // حفظ في جميع الأماكن الممكنة
            saveLanguage(langCode);
            
            // تطبيق التغييرات
            applyLanguageChanges(langCode);
            
            // إعادة تحميل بعد ثانية واحدة
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // تحديث الواجهة
        function updateUI(langCode) {
            const lang = languages[langCode];
            
            // تحديث عرض اللغة الحالية
            document.getElementById('current-lang').textContent = `${lang.name} (${lang.code})`;
            
            // تحديث اتجاه الصفحة
            document.documentElement.setAttribute('lang', lang.code);
            document.documentElement.setAttribute('dir', lang.dir);
            
            // تحديث الأزرار النشطة
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === langCode) {
                    btn.classList.add('active');
                }
            });
        }

        // حفظ اللغة
        function saveLanguage(langCode) {
            // حفظ في localStorage
            localStorage.setItem('django_language', langCode);
            localStorage.setItem('selected_language', langCode);
            
            // حفظ في sessionStorage
            sessionStorage.setItem('django_language', langCode);
            
            // حفظ في cookie بطرق متعددة
            const expires = new Date();
            expires.setFullYear(expires.getFullYear() + 1);
            
            // طريقة 1
            document.cookie = `django_language=${langCode}; path=/; expires=${expires.toUTCString()}; SameSite=Lax`;
            
            // طريقة 2
            document.cookie = `language=${langCode}; path=/; expires=${expires.toUTCString()}; SameSite=Lax`;
            
            // طريقة 3
            document.cookie = `LANGUAGE_CODE=${langCode}; path=/; expires=${expires.toUTCString()}; SameSite=Lax`;
            
            console.log('تم حفظ اللغة في جميع الأماكن');
        }

        // تطبيق تغييرات اللغة
        function applyLanguageChanges(langCode) {
            const lang = languages[langCode];
            
            // تحديث عنوان الصفحة
            if (langCode === 'ar') {
                document.title = 'تغيير اللغة - نظام إدارة العيادات';
            } else if (langCode === 'en') {
                document.title = 'Language Selection - Clinic Management System';
            } else if (langCode === 'ku') {
                document.title = 'هەڵبژاردنی زمان - سیستەمی بەڕێوەبردنی کلینیک';
            }
        }

        // قراءة cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // تحديث معلومات التصحيح
        function updateDebugInfo() {
            const cookieLang = getCookie('django_language') || getCookie('language') || getCookie('LANGUAGE_CODE');
            const storageLang = localStorage.getItem('django_language') || localStorage.getItem('selected_language');
            
            document.getElementById('cookie-info').textContent = cookieLang || 'غير محدد';
            document.getElementById('storage-info').textContent = storageLang || 'غير محدد';
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث معلومات التصحيح
            updateDebugInfo();
            
            // تحديث كل ثانية
            setInterval(updateDebugInfo, 1000);
            
            // تحديد اللغة الحالية
            const currentLang = getCookie('django_language') || 'ar';
            updateUI(currentLang);
            
            console.log('تم تحميل صفحة تغيير اللغة');
            console.log('جميع cookies:', document.cookie);
        });
    </script>
</body>
</html>
