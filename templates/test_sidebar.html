<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الـ Sidebar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 0.125rem 0 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .test-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.2s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-working { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-testing { background-color: #ffc107; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-heartbeat me-2"></i>
                نظام إدارة العيادات الغذائية - اختبار الـ Sidebar
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="testLink('/doctor/dashboard/', this)">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                <span class="status-indicator status-working"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/patients/', this)">
                                <i class="fas fa-users me-2"></i>إدارة المرضى
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/nutrition/', this)">
                                <i class="fas fa-utensils me-2"></i>الخطط الغذائية
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/nutrition/foods/', this)">
                                <i class="fas fa-apple-alt me-2"></i>إدارة الأطعمة
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/communications/appointments/', this)">
                                <i class="fas fa-calendar me-2"></i>المواعيد
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/communications/messages/', this)">
                                <i class="fas fa-envelope me-2"></i>الرسائل
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="testLink('/communications/whatsapp/', this)">
                                <i class="fab fa-whatsapp me-2"></i>رسائل الواتساب
                                <span class="status-indicator status-testing"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">اختبار الـ Sidebar</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testAllLinks()">
                            <i class="fas fa-play me-1"></i>اختبار جميع الروابط
                        </button>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <div class="col-12">
                        <div class="test-card">
                            <h5><i class="fas fa-info-circle me-2 text-primary"></i>معلومات الاختبار</h5>
                            <p>هذه الصفحة لاختبار جميع روابط الـ Sidebar. انقر على أي رابط في الـ Sidebar لاختباره.</p>
                            <div class="row">
                                <div class="col-md-4">
                                    <span class="status-indicator status-working"></span> يعمل بشكل صحيح
                                </div>
                                <div class="col-md-4">
                                    <span class="status-indicator status-testing"></span> قيد الاختبار
                                </div>
                                <div class="col-md-4">
                                    <span class="status-indicator status-error"></span> لا يعمل
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results Display -->
                <div class="row">
                    <div class="col-12">
                        <div class="test-card">
                            <h5><i class="fas fa-clipboard-list me-2 text-success"></i>نتائج الاختبار</h5>
                            <div id="test-results">
                                <p class="text-muted">انقر على الروابط في الـ Sidebar لبدء الاختبار...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="test-card">
                            <h5><i class="fas fa-rocket me-2 text-warning"></i>إجراءات سريعة</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="openInNewTab('/patients/')">
                                    <i class="fas fa-users me-1"></i>فتح المرضى
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="openInNewTab('/nutrition/')">
                                    <i class="fas fa-utensils me-1"></i>فتح الخطط الغذائية
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="openInNewTab('/nutrition/foods/')">
                                    <i class="fas fa-apple-alt me-1"></i>فتح الأطعمة
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="openInNewTab('/communications/appointments/')">
                                    <i class="fas fa-calendar me-1"></i>فتح المواعيد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = [];

        function testLink(url, element) {
            // إزالة active من جميع الروابط
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // إضافة active للرابط المحدد
            element.classList.add('active');
            
            // تغيير الحالة إلى قيد الاختبار
            const indicator = element.querySelector('.status-indicator');
            indicator.className = 'status-indicator status-testing';
            
            // اختبار الرابط
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        indicator.className = 'status-indicator status-working';
                        addTestResult(url, 'نجح', 'success');
                        
                        // فتح في نافذة جديدة
                        window.open(url, '_blank');
                    } else {
                        indicator.className = 'status-indicator status-error';
                        addTestResult(url, `فشل (${response.status})`, 'danger');
                    }
                })
                .catch(error => {
                    indicator.className = 'status-indicator status-error';
                    addTestResult(url, `خطأ: ${error.message}`, 'danger');
                });
        }

        function addTestResult(url, status, type) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const result = {
                url: url,
                status: status,
                type: type,
                time: timestamp
            };
            
            testResults.unshift(result);
            updateTestResultsDisplay();
        }

        function updateTestResultsDisplay() {
            const container = document.getElementById('test-results');
            
            if (testResults.length === 0) {
                container.innerHTML = '<p class="text-muted">لا توجد نتائج اختبار بعد...</p>';
                return;
            }
            
            let html = '<div class="list-group">';
            testResults.slice(0, 10).forEach(result => {
                html += `
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${result.url}</h6>
                            <small>${result.time}</small>
                        </div>
                        <span class="badge bg-${result.type}">${result.status}</span>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        function testAllLinks() {
            const links = document.querySelectorAll('.sidebar .nav-link');
            let delay = 0;
            
            links.forEach(link => {
                setTimeout(() => {
                    link.click();
                }, delay);
                delay += 1000; // تأخير ثانية واحدة بين كل اختبار
            });
        }

        function openInNewTab(url) {
            window.open(url, '_blank');
        }

        // تحديث الوقت كل ثانية
        setInterval(() => {
            document.querySelector('.navbar-brand').innerHTML = `
                <i class="fas fa-heartbeat me-2"></i>
                نظام إدارة العيادات الغذائية - اختبار الـ Sidebar
                <small class="text-light ms-2">${new Date().toLocaleTimeString('ar-SA')}</small>
            `;
        }, 1000);
    </script>
</body>
</html>
