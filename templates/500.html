{% extends 'base.html' %}

{% block title %}خطأ في الخادم - 500{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center" style="min-height: 70vh;">
    <div class="text-center">
        <div class="error-page">
            <!-- Error Number -->
            <div class="error-number mb-4">
                <h1 class="display-1 text-gradient" style="font-size: 8rem; font-weight: 900;">500</h1>
            </div>
            
            <!-- Error Message -->
            <div class="error-message mb-4">
                <h2 class="h3 text-muted mb-3">عذراً، حدث خطأ في الخادم!</h2>
                <p class="lead text-muted mb-4">
                    نواجه مشكلة تقنية مؤقتة. فريقنا يعمل على حل المشكلة. يرجى المحاولة مرة أخرى خلال دقائق قليلة.
                </p>
            </div>
            
            <!-- Illustration -->
            <div class="error-illustration mb-5">
                <i class="fas fa-tools fa-5x text-muted opacity-50"></i>
            </div>
            
            <!-- Action Buttons -->
            <div class="error-actions">
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <button onclick="location.reload()" class="btn btn-primary btn-lg">
                        <i class="fas fa-redo me-2"></i>إعادة المحاولة
                    </button>
                    
                    <a href="{% url 'home' %}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                </div>
                
                <div class="mt-4">
                    <button onclick="history.back()" class="btn btn-link text-muted">
                        <i class="fas fa-arrow-left me-2"></i>العودة للصفحة السابقة
                    </button>
                </div>
            </div>
            
            <!-- Status Section -->
            <div class="error-status mt-5 pt-4 border-top">
                <h5 class="text-muted mb-3">حالة النظام</h5>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="row g-3">
                            <div class="col-sm-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <div class="status-indicator bg-warning rounded-circle mx-auto mb-2" style="width: 12px; height: 12px;"></div>
                                        <h6 class="card-title mb-1">الخادم</h6>
                                        <small class="text-muted">قيد الصيانة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <div class="status-indicator bg-success rounded-circle mx-auto mb-2" style="width: 12px; height: 12px;"></div>
                                        <h6 class="card-title mb-1">قاعدة البيانات</h6>
                                        <small class="text-muted">تعمل بشكل طبيعي</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <div class="status-indicator bg-success rounded-circle mx-auto mb-2" style="width: 12px; height: 12px;"></div>
                                        <h6 class="card-title mb-1">الشبكة</h6>
                                        <small class="text-muted">تعمل بشكل طبيعي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Section -->
            <div class="error-contact mt-4">
                <p class="text-muted small">
                    إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني على 
                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    max-width: 600px;
    margin: 0 auto;
}

.error-number h1 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(240, 147, 251, 0.3);
}

.error-illustration {
    animation: shake 2s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.btn {
    border-radius: 0.75rem;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .error-number h1 {
        font-size: 6rem;
    }
    
    .error-illustration i {
        font-size: 3rem !important;
    }
}
</style>
{% endblock %}
