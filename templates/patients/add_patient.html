{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة مريض جديد - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patients:patient_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Personal Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-user me-2"></i>المعلومات الشخصية
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">الاسم الأول *</label>
                            {{ form.first_name|add_class:"form-control" }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small mt-1">{{ form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">اسم العائلة *</label>
                            {{ form.last_name|add_class:"form-control" }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small mt-1">{{ form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني *</label>
                            {{ form.email|add_class:"form-control" }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">تاريخ الميلاد</label>
                            {{ form.date_of_birth|add_class:"form-control" }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger small mt-1">{{ form.date_of_birth.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">رقم الهاتف</label>
                            {{ form.phone_number|add_class:"form-control" }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small mt-1">{{ form.phone_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.whatsapp_number.id_for_label }}" class="form-label">رقم الواتساب</label>
                            {{ form.whatsapp_number|add_class:"form-control" }}
                            {% if form.whatsapp_number.errors %}
                                <div class="text-danger small mt-1">{{ form.whatsapp_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Physical Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-weight me-2"></i>المعلومات الجسدية
                            </h5>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.gender.id_for_label }}" class="form-label">الجنس *</label>
                            {{ form.gender|add_class:"form-select" }}
                            {% if form.gender.errors %}
                                <div class="text-danger small mt-1">{{ form.gender.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.height.id_for_label }}" class="form-label">الطول (سم) *</label>
                            {{ form.height|add_class:"form-control" }}
                            {% if form.height.errors %}
                                <div class="text-danger small mt-1">{{ form.height.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.current_weight.id_for_label }}" class="form-label">الوزن الحالي (كغ) *</label>
                            {{ form.current_weight|add_class:"form-control" }}
                            {% if form.current_weight.errors %}
                                <div class="text-danger small mt-1">{{ form.current_weight.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.target_weight.id_for_label }}" class="form-label">الوزن المستهدف (كغ)</label>
                            {{ form.target_weight|add_class:"form-control" }}
                            {% if form.target_weight.errors %}
                                <div class="text-danger small mt-1">{{ form.target_weight.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.activity_level.id_for_label }}" class="form-label">مستوى النشاط *</label>
                            {{ form.activity_level|add_class:"form-select" }}
                            {% if form.activity_level.errors %}
                                <div class="text-danger small mt-1">{{ form.activity_level.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.health_goal.id_for_label }}" class="form-label">الهدف الصحي *</label>
                            {{ form.health_goal|add_class:"form-select" }}
                            {% if form.health_goal.errors %}
                                <div class="text-danger small mt-1">{{ form.health_goal.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Medical Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-heartbeat me-2"></i>المعلومات الطبية
                            </h5>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.medical_conditions.id_for_label }}" class="form-label">الأمراض المزمنة</label>
                            {{ form.medical_conditions|add_class:"form-control" }}
                            {% if form.medical_conditions.errors %}
                                <div class="text-danger small mt-1">{{ form.medical_conditions.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">اذكر أي أمراض مزمنة مثل السكري، ضغط الدم، إلخ.</div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.allergies.id_for_label }}" class="form-label">الحساسيات الغذائية</label>
                            {{ form.allergies|add_class:"form-control" }}
                            {% if form.allergies.errors %}
                                <div class="text-danger small mt-1">{{ form.allergies.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">اذكر أي حساسيات غذائية معروفة.</div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.medications.id_for_label }}" class="form-label">الأدوية الحالية</label>
                            {{ form.medications|add_class:"form-control" }}
                            {% if form.medications.errors %}
                                <div class="text-danger small mt-1">{{ form.medications.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">اذكر الأدوية التي يتناولها المريض حالياً.</div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات إضافية</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">أي ملاحظات أو معلومات إضافية مهمة.</div>
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'patients:patient_list' %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المريض
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من دقة البيانات المدخلة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رقم الواتساب مهم لإرسال الخطط
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        المعلومات الطبية ضرورية للخطة
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن تعديل البيانات لاحقاً
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
