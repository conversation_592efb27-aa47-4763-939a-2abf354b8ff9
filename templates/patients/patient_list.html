{% extends 'base.html' %}

{% block title %}قائمة المرضى - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>إدارة المرضى
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patients:add_patient' %}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="البحث بالاسم أو البريد الإلكتروني...">
            </div>
            <div class="col-md-3">
                <label for="gender" class="form-label">الجنس</label>
                <select class="form-select" id="gender" name="gender">
                    <option value="">جميع الأجناس</option>
                    <option value="M" {% if gender_filter == 'M' %}selected{% endif %}>ذكر</option>
                    <option value="F" {% if gender_filter == 'F' %}selected{% endif %}>أنثى</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="goal" class="form-label">الهدف الصحي</label>
                <select class="form-select" id="goal" name="goal">
                    <option value="">جميع الأهداف</option>
                    <option value="lose_weight" {% if goal_filter == 'lose_weight' %}selected{% endif %}>إنقاص الوزن</option>
                    <option value="gain_weight" {% if goal_filter == 'gain_weight' %}selected{% endif %}>زيادة الوزن</option>
                    <option value="maintain_weight" {% if goal_filter == 'maintain_weight' %}selected{% endif %}>المحافظة على الوزن</option>
                    <option value="build_muscle" {% if goal_filter == 'build_muscle' %}selected{% endif %}>بناء العضلات</option>
                    <option value="improve_health" {% if goal_filter == 'improve_health' %}selected{% endif %}>تحسين الصحة العامة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Patients List -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom-0">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>قائمة المرضى ({{ patients.paginator.count }} مريض)
        </h5>
    </div>
    <div class="card-body p-0">
        {% if patients %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المريض</th>
                            <th>العمر</th>
                            <th>الجنس</th>
                            <th>الوزن الحالي</th>
                            <th>BMI</th>
                            <th>الهدف</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                        {% if patient.user.first_name %}
                                            {{ patient.user.first_name|first|upper }}
                                        {% else %}
                                            {{ patient.user.username|first|upper }}
                                        {% endif %}
                                    </div>
                                    <div>
                                        <div class="fw-bold">
                                            {% if patient.user.first_name and patient.user.last_name %}
                                                {{ patient.user.first_name }} {{ patient.user.last_name }}
                                            {% elif patient.user.first_name %}
                                                {{ patient.user.first_name }}
                                            {% else %}
                                                {{ patient.user.username }}
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">{{ patient.user.email|default:"لا يوجد إيميل" }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if patient.user.date_of_birth %}
                                    {{ patient.user.date_of_birth|date:"Y-m-d" }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                    {{ patient.get_gender_display }}
                                </span>
                            </td>
                            <td>{{ patient.current_weight }} كغ</td>
                            <td>
                                {% if patient.bmi %}
                                    <span class="badge bg-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                        {{ patient.bmi }}
                                    </span>
                                {% else %}
                                    غير محسوب
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ patient.get_health_goal_display }}</small>
                            </td>
                            <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'patients:patient_detail' patient.id %}" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'patients:edit_patient' patient.id %}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'nutrition_plans:create_nutrition_plan' patient.id %}"
                                       class="btn btn-outline-success" title="إنشاء خطة غذائية">
                                        <i class="fas fa-utensils"></i>
                                    </a>
                                    <a href="{% url 'patients:patient_progress' patient.id %}" 
                                       class="btn btn-outline-info" title="تقرير التقدم">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if patients.has_other_pages %}
                <div class="card-footer bg-white border-top-0">
                    <nav aria-label="Patient pagination">
                        <ul class="pagination justify-content-center mb-0">
                            {% if patients.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ patients.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                        السابق
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in patients.paginator.page_range %}
                                {% if patients.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > patients.number|add:'-3' and num < patients.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                            {{ num }}
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if patients.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ patients.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                        التالي
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                <h5>لا يوجد مرضى</h5>
                <p class="text-muted">لم يتم العثور على أي مرضى. ابدأ بإضافة مريض جديد.</p>
                <a href="{% url 'patients:add_patient' %}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.bg-pink {
    background-color: #e83e8c !important;
}
</style>
{% endblock %}
