{% extends 'base.html' %}

{% block title %}تقرير التقدم - {{ patient.user.get_full_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-line me-2"></i>تقرير التقدم - {{ patient.user.get_full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patients:patient_detail' patient.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للملف
        </a>
    </div>
</div>

<div class="row">
    <!-- Patient Info Card -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>معلومات المريض
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if patient.user.profile_picture %}
                        <img src="{{ patient.user.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="صورة المريض">
                    {% else %}
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ patient.user.first_name|first }}
                        </div>
                    {% endif %}
                    <h6 class="mt-2 mb-0">{{ patient.user.get_full_name }}</h6>
                    <small class="text-muted">{{ patient.user.get_user_type_display }}</small>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>
                            {% if patient.user.date_of_birth %}
                                {{ patient.user.date_of_birth|timesince|truncatewords:1 }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>{{ patient.get_gender_display|default:"غير محدد" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الطول:</strong></td>
                        <td>{{ patient.height|default:"غير محدد" }} سم</td>
                    </tr>
                    <tr>
                        <td><strong>الوزن الحالي:</strong></td>
                        <td>{{ patient.current_weight|default:"غير محدد" }} كغ</td>
                    </tr>
                    <tr>
                        <td><strong>الهدف:</strong></td>
                        <td>{{ patient.get_health_goal_display|default:"غير محدد" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Progress Charts -->
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>تطور الوزن
                </h5>
            </div>
            <div class="card-body">
                {% if weight_records %}
                    <canvas id="weightChart" width="400" height="200"></canvas>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-weight fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد سجلات وزن</h5>
                        <p class="text-muted">لم يتم تسجيل أي قياسات وزن بعد.</p>
                        <a href="{% url 'patients:add_weight_record' patient.id %}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>إضافة قياس جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-weight fa-2x mb-2"></i>
                <h4>{{ patient.current_weight|default:"--" }}</h4>
                <small>الوزن الحالي (كغ)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-bullseye fa-2x mb-2"></i>
                <h4>{{ patient.target_weight|default:"--" }}</h4>
                <small>الوزن المستهدف (كغ)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calculator fa-2x mb-2"></i>
                <h4>
                    {% if patient.current_weight and patient.height %}
                        {{ patient.bmi|floatformat:1 }}
                    {% else %}
                        --
                    {% endif %}
                </h4>
                <small>مؤشر كتلة الجسم</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h4>{{ weight_records.count }}</h4>
                <small>عدد القياسات</small>
            </div>
        </div>
    </div>
</div>

<!-- Weight Records Table -->
{% if weight_records %}
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>سجل قياسات الوزن
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ</th>
                        <th>الوزن (كغ)</th>
                        <th>مؤشر كتلة الجسم</th>
                        <th>التغيير</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in weight_records %}
                    <tr>
                        <td>{{ record.date_recorded|date:"Y-m-d" }}</td>
                        <td>
                            <span class="fw-bold">{{ record.weight }}</span>
                        </td>
                        <td>
                            {% if patient.height %}
                                {{ record.bmi|floatformat:1 }}
                            {% else %}
                                --
                            {% endif %}
                        </td>
                        <td>
                            {% if record.weight_change %}
                                <span class="badge bg-{% if record.weight_change > 0 %}danger{% else %}success{% endif %}">
                                    {% if record.weight_change > 0 %}+{% endif %}{{ record.weight_change|floatformat:1 }} كغ
                                </span>
                            {% else %}
                                <span class="text-muted">--</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if record.notes %}
                                <small class="text-muted">{{ record.notes|truncatechars:50 }}</small>
                            {% else %}
                                <span class="text-muted">--</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Chart.js Script -->
{% if weight_records %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('weightChart').getContext('2d');
    
    const weightData = [
        {% for record in weight_records %}
        {
            x: '{{ record.date_recorded|date:"Y-m-d" }}',
            y: {{ record.weight }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'الوزن (كغ)',
                data: weightData,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'تطور الوزن عبر الزمن'
                },
                legend: {
                    display: true
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        parser: 'YYYY-MM-DD',
                        displayFormats: {
                            day: 'MMM DD'
                        }
                    },
                    title: {
                        display: true,
                        text: 'التاريخ'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'الوزن (كغ)'
                    }
                }
            }
        }
    });
});
</script>
{% endif %}

<style>
.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
