{% extends 'base.html' %}

{% block title %}{{ patient.user.get_full_name }} - تفاصيل المريض{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>{{ patient.user.get_full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'patients:edit_patient' patient.id %}" class="btn btn-outline-warning">
                <i class="fas fa-edit me-2"></i>تعديل البيانات
            </a>
            <a href="{% url 'patients:add_weight_record' patient.id %}" class="btn btn-outline-primary">
                <i class="fas fa-weight me-2"></i>إضافة وزن
            </a>
            <a href="{% url 'patients:add_inbody_result' patient.id %}" class="btn btn-outline-success">
                <i class="fas fa-chart-bar me-2"></i>إضافة InBody
            </a>
        </div>
        <a href="{% url 'patients:patient_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- Patient Information -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>معلومات المريض
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if patient.user.profile_picture %}
                        <img src="{{ patient.user.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="صورة المريض">
                    {% else %}
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ patient.user.first_name|first }}{{ patient.user.last_name|first }}
                        </div>
                    {% endif %}
                    <h5 class="mt-2 mb-0">{{ patient.user.get_full_name }}</h5>
                    <small class="text-muted">{{ patient.user.email }}</small>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>
                            <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                {{ patient.get_gender_display }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>
                            {% if patient.user.date_of_birth %}
                                {{ patient.user.date_of_birth|date:"Y-m-d" }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الهاتف:</strong></td>
                        <td>{{ patient.user.phone_number|default:"غير محدد" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الواتساب:</strong></td>
                        <td>{{ patient.user.whatsapp_number|default:"غير محدد" }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Health Goals -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-success text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-target me-2"></i>الأهداف والنشاط
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>الهدف الصحي:</strong></td>
                        <td>{{ patient.get_health_goal_display }}</td>
                    </tr>
                    <tr>
                        <td><strong>مستوى النشاط:</strong></td>
                        <td>{{ patient.get_activity_level_display }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Physical Information -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-weight me-2"></i>المعلومات الجسدية
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary mb-1">{{ patient.height }}</h4>
                            <small class="text-muted">الطول (سم)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-success mb-1">{{ patient.current_weight }}</h4>
                            <small class="text-muted">الوزن الحالي (كغ)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-warning mb-1">{{ patient.target_weight|default:"غير محدد" }}</h4>
                            <small class="text-muted">الوزن المستهدف (كغ)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            {% if patient.bmi %}
                                <h4 class="mb-1 text-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                    {{ patient.bmi }}
                                </h4>
                                <small class="text-muted">مؤشر كتلة الجسم</small>
                                <div class="mt-1">
                                    <small class="badge bg-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                        {{ patient.bmi_category }}
                                    </small>
                                </div>
                            {% else %}
                                <h4 class="text-muted mb-1">غير محسوب</h4>
                                <small class="text-muted">مؤشر كتلة الجسم</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Latest InBody Result -->
        {% if latest_inbody %}
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>آخر نتيجة InBody - {{ latest_inbody.test_date|date:"Y-m-d" }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-primary">{{ latest_inbody.muscle_mass }} كغ</h5>
                            <small class="text-muted">كتلة العضلات</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-danger">{{ latest_inbody.body_fat_mass }} كغ</h5>
                            <small class="text-muted">كتلة الدهون</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-warning">{{ latest_inbody.body_fat_percentage }}%</h5>
                            <small class="text-muted">نسبة الدهون</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-info">{{ latest_inbody.basal_metabolic_rate|default:"غير محدد" }}</h5>
                            <small class="text-muted">معدل الأيض الأساسي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Weight Records -->
        {% if weight_records %}
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>سجلات الوزن
                </h6>
                <a href="{% url 'patients:patient_progress' patient.id %}" class="btn btn-sm btn-light">
                    عرض التقرير الكامل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوزن</th>
                                <th>التغيير</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in weight_records %}
                            <tr>
                                <td>{{ record.recorded_date|date:"Y-m-d" }}</td>
                                <td>{{ record.weight }} كغ</td>
                                <td>
                                    <span class="text-muted">-</span>
                                </td>
                                <td>{{ record.notes|default:"-"|truncatechars:30 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Medical Information -->
{% if patient.medical_conditions or patient.allergies or patient.medications or patient.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>المعلومات الطبية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if patient.medical_conditions %}
                    <div class="col-md-6 mb-3">
                        <h6 class="text-danger">الأمراض المزمنة:</h6>
                        <p class="text-muted">{{ patient.medical_conditions }}</p>
                    </div>
                    {% endif %}
                    
                    {% if patient.allergies %}
                    <div class="col-md-6 mb-3">
                        <h6 class="text-warning">الحساسيات الغذائية:</h6>
                        <p class="text-muted">{{ patient.allergies }}</p>
                    </div>
                    {% endif %}
                    
                    {% if patient.medications %}
                    <div class="col-md-6 mb-3">
                        <h6 class="text-info">الأدوية الحالية:</h6>
                        <p class="text-muted">{{ patient.medications }}</p>
                    </div>
                    {% endif %}
                    
                    {% if patient.notes %}
                    <div class="col-md-6 mb-3">
                        <h6 class="text-secondary">ملاحظات إضافية:</h6>
                        <p class="text-muted">{{ patient.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
.bg-pink {
    background-color: #e83e8c !important;
}
</style>
{% endblock %}
