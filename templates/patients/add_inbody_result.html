{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة نتيجة InBody - {{ patient.user.get_full_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>إضافة نتيجة InBody
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patients:patient_detail' patient.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للملف
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>بيانات InBody
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-info border-bottom pb-2 mb-3">
                                <i class="fas fa-calendar me-2"></i>معلومات القياس
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date_measured.id_for_label }}" class="form-label">تاريخ القياس *</label>
                            {{ form.date_measured|add_class:"form-control" }}
                            {% if form.date_measured.errors %}
                                <div class="text-danger small mt-1">{{ form.date_measured.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-info border-bottom pb-2 mb-3">
                                <i class="fas fa-weight me-2"></i>تركيب الجسم
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.weight.id_for_label }}" class="form-label">الوزن *</label>
                            <div class="input-group">
                                {{ form.weight|add_class:"form-control" }}
                                <span class="input-group-text">كغ</span>
                            </div>
                            {% if form.weight.errors %}
                                <div class="text-danger small mt-1">{{ form.weight.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.muscle_mass.id_for_label }}" class="form-label">الكتلة العضلية *</label>
                            <div class="input-group">
                                {{ form.muscle_mass|add_class:"form-control" }}
                                <span class="input-group-text">كغ</span>
                            </div>
                            {% if form.muscle_mass.errors %}
                                <div class="text-danger small mt-1">{{ form.muscle_mass.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.body_fat_percentage.id_for_label }}" class="form-label">نسبة الدهون *</label>
                            <div class="input-group">
                                {{ form.body_fat_percentage|add_class:"form-control" }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.body_fat_percentage.errors %}
                                <div class="text-danger small mt-1">{{ form.body_fat_percentage.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.body_water_percentage.id_for_label }}" class="form-label">نسبة الماء</label>
                            <div class="input-group">
                                {{ form.body_water_percentage|add_class:"form-control" }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.body_water_percentage.errors %}
                                <div class="text-danger small mt-1">{{ form.body_water_percentage.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.bone_mass.id_for_label }}" class="form-label">كتلة العظام</label>
                            <div class="input-group">
                                {{ form.bone_mass|add_class:"form-control" }}
                                <span class="input-group-text">كغ</span>
                            </div>
                            {% if form.bone_mass.errors %}
                                <div class="text-danger small mt-1">{{ form.bone_mass.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.basal_metabolic_rate.id_for_label }}" class="form-label">معدل الأيض الأساسي</label>
                            <div class="input-group">
                                {{ form.basal_metabolic_rate|add_class:"form-control" }}
                                <span class="input-group-text">سعرة</span>
                            </div>
                            {% if form.basal_metabolic_rate.errors %}
                                <div class="text-danger small mt-1">{{ form.basal_metabolic_rate.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-info border-bottom pb-2 mb-3">
                                <i class="fas fa-notes-medical me-2"></i>ملاحظات إضافية
                            </h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">أضف أي ملاحظات أو تفاصيل إضافية حول القياس</div>
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'patients:patient_detail' patient.id %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-2"></i>حفظ النتيجة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Patient Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>معلومات المريض
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if patient.user.profile_picture %}
                        <img src="{{ patient.user.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="صورة المريض">
                    {% else %}
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ patient.user.first_name|first }}
                        </div>
                    {% endif %}
                    <h6 class="mt-2 mb-0">{{ patient.user.get_full_name }}</h6>
                    <small class="text-muted">{{ patient.user.get_user_type_display }}</small>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>
                            {% if patient.user.date_of_birth %}
                                {{ patient.user.date_of_birth|timesince|truncatewords:1 }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>{{ patient.get_gender_display|default:"غير محدد" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الطول:</strong></td>
                        <td>{{ patient.height|default:"غير محدد" }} سم</td>
                    </tr>
                    <tr>
                        <td><strong>الوزن الحالي:</strong></td>
                        <td>{{ patient.current_weight|default:"غير محدد" }} كغ</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- InBody Tips -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>نصائح InBody
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        قم بالقياس في نفس الوقت يومياً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تجنب الأكل قبل القياس بساعتين
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تجنب التمرين قبل القياس
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من جفاف القدمين واليدين
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
