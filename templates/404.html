{% extends 'base.html' %}

{% block title %}الصفحة غير موجودة - 404{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center" style="min-height: 70vh;">
    <div class="text-center">
        <div class="error-page">
            <!-- Error Number -->
            <div class="error-number mb-4">
                <h1 class="display-1 text-gradient" style="font-size: 8rem; font-weight: 900;">404</h1>
            </div>
            
            <!-- Error Message -->
            <div class="error-message mb-4">
                <h2 class="h3 text-muted mb-3">عذراً، الصفحة غير موجودة!</h2>
                <p class="lead text-muted mb-4">
                    لا يمكننا العثور على الصفحة التي تبحث عنها. ربما تم حذفها أو نقلها أو أن الرابط غير صحيح.
                </p>
            </div>
            
            <!-- Illustration -->
            <div class="error-illustration mb-5">
                <i class="fas fa-search fa-5x text-muted opacity-50"></i>
            </div>
            
            <!-- Action Buttons -->
            <div class="error-actions">
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="{% url 'home' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                    
                    {% if user.is_authenticated %}
                        {% if user.user_type == 'doctor' %}
                            <a href="{% url 'doctor_dashboard' %}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                        {% elif user.user_type == 'patient' %}
                            <a href="{% url 'patient_dashboard' %}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                        {% endif %}
                    {% else %}
                        <a href="{% url 'login' %}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    {% endif %}
                </div>
                
                <div class="mt-4">
                    <button onclick="history.back()" class="btn btn-link text-muted">
                        <i class="fas fa-arrow-left me-2"></i>العودة للصفحة السابقة
                    </button>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="error-help mt-5 pt-4 border-top">
                <h5 class="text-muted mb-3">هل تحتاج مساعدة؟</h5>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-question-circle text-info mb-2"></i>
                                        <h6 class="card-title mb-1">الأسئلة الشائعة</h6>
                                        <small class="text-muted">ابحث في قاعدة المعرفة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-headset text-success mb-2"></i>
                                        <h6 class="card-title mb-1">الدعم الفني</h6>
                                        <small class="text-muted">تواصل مع فريق الدعم</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    max-width: 600px;
    margin: 0 auto;
}

.error-number h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

.error-illustration {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.btn {
    border-radius: 0.75rem;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .error-number h1 {
        font-size: 6rem;
    }
    
    .error-illustration i {
        font-size: 3rem !important;
    }
}
</style>
{% endblock %}
