{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تسجيل الدخول - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center py-5">
        <div class="col-lg-5 col-md-7">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center fs-2 mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h2 class="card-title text-primary mb-2">تسجيل الدخول</h2>
                        <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            {{ form.username|add_class:"form-control form-control-lg" }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.username.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            {{ form.password|add_class:"form-control form-control-lg" }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.password.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="{% url 'password_reset' %}" class="text-muted text-decoration-none small">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Register Links -->
            <div class="text-center mt-4">
                <p class="text-muted mb-3">ليس لديك حساب؟</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a href="{% url 'doctor_register' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user-md me-2"></i>تسجيل كطبيب
                    </a>
                    <a href="{% url 'patient_register' %}" class="btn btn-outline-success">
                        <i class="fas fa-user me-2"></i>تسجيل كمريض
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 1rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    border-radius: 0.5rem;
}

.btn-outline-primary:hover,
.btn-outline-success:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
}
</style>
{% endblock %}
