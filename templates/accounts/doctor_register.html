{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تسجيل طبيب جديد - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center py-5">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center fs-2 mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h2 class="card-title text-primary mb-2">تسجيل طبيب جديد</h2>
                        <p class="text-muted">أدخل بياناتك المهنية والشخصية</p>
                    </div>
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>المعلومات الشخصية
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">الاسم الأول *</label>
                                {{ form.first_name|add_class:"form-control" }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">اسم العائلة *</label>
                                {{ form.last_name|add_class:"form-control" }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">اسم المستخدم *</label>
                                {{ form.username|add_class:"form-control" }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">{{ form.username.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني *</label>
                                {{ form.email|add_class:"form-control" }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">رقم الهاتف</label>
                                {{ form.phone_number|add_class:"form-control" }}
                                {% if form.phone_number.errors %}
                                    <div class="text-danger small mt-1">{{ form.phone_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.whatsapp_number.id_for_label }}" class="form-label">رقم الواتساب</label>
                                {{ form.whatsapp_number|add_class:"form-control" }}
                                {% if form.whatsapp_number.errors %}
                                    <div class="text-danger small mt-1">{{ form.whatsapp_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">تاريخ الميلاد</label>
                                {{ form.date_of_birth|add_class:"form-control" }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger small mt-1">{{ form.date_of_birth.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Professional Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-stethoscope me-2"></i>المعلومات المهنية
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.specialization.id_for_label }}" class="form-label">التخصص *</label>
                                {{ form.specialization|add_class:"form-control" }}
                                {% if form.specialization.errors %}
                                    <div class="text-danger small mt-1">{{ form.specialization.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.license_number.id_for_label }}" class="form-label">رقم الترخيص *</label>
                                {{ form.license_number|add_class:"form-control" }}
                                {% if form.license_number.errors %}
                                    <div class="text-danger small mt-1">{{ form.license_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.clinic_name.id_for_label }}" class="form-label">اسم العيادة *</label>
                                {{ form.clinic_name|add_class:"form-control" }}
                                {% if form.clinic_name.errors %}
                                    <div class="text-danger small mt-1">{{ form.clinic_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.years_of_experience.id_for_label }}" class="form-label">سنوات الخبرة *</label>
                                {{ form.years_of_experience|add_class:"form-control" }}
                                {% if form.years_of_experience.errors %}
                                    <div class="text-danger small mt-1">{{ form.years_of_experience.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.clinic_address.id_for_label }}" class="form-label">عنوان العيادة *</label>
                                {{ form.clinic_address|add_class:"form-control" }}
                                {% if form.clinic_address.errors %}
                                    <div class="text-danger small mt-1">{{ form.clinic_address.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.bio.id_for_label }}" class="form-label">نبذة شخصية</label>
                                {{ form.bio|add_class:"form-control" }}
                                {% if form.bio.errors %}
                                    <div class="text-danger small mt-1">{{ form.bio.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Password -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </h5>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">كلمة المرور *</label>
                                {{ form.password1|add_class:"form-control" }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small mt-1">{{ form.password1.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">تأكيد كلمة المرور *</label>
                                {{ form.password2|add_class:"form-control" }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small mt-1">{{ form.password2.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إنشاء حساب الطبيب
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="{% url 'login' %}" class="text-primary text-decoration-none">
                                    سجل دخولك من هنا
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
