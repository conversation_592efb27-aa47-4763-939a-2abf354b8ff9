{% extends 'base.html' %}

{% block title %}لوحة تحكم الطبيب - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<!-- Loading Indicator -->
<div id="loading-indicator" class="d-none">
    <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
</div>

<div id="dashboard-content">
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'simple_patients' %}" class="btn btn-sm btn-primary">
                <i class="fas fa-user-plus me-1"></i>إضافة مريض جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h2 mb-0">{{ total_patients }}</div>
                        <div class="text-white-75">إجمالي المرضى</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h2 mb-0">{{ active_plans }}</div>
                        <div class="text-white-75">خطط نشطة</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-utensils fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h2 mb-0">{{ draft_plans }}</div>
                        <div class="text-white-75">مسودات</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h2 mb-0">{{ completed_plans }}</div>
                        <div class="text-white-75">خطط مكتملة</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-0" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">{{ active_plans }}</div>
                        <div class="text-white-50">الخطط النشطة</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-utensils fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-0" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">15</div>
                        <div class="text-white-50">المواعيد هذا الأسبوع</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card border-0" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">8</div>
                        <div class="text-white-50">رسائل جديدة</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <a href="{% url 'simple_patients' %}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                            إضافة مريض جديد
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{% url 'simple_foods' %}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-apple-alt fa-2x mb-2 d-block"></i>
                            إدارة الأطعمة
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{% url 'simple_messages' %}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-calendar-plus fa-2x mb-2 d-block"></i>
                            إضافة موعد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>مواعيد اليوم
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center text-muted py-3">
                    <i class="fas fa-calendar-check fa-3x mb-3 text-muted"></i>
                    <p>لا توجد مواعيد اليوم</p>
                    <a href="{% url 'simple_messages' %}" class="btn btn-sm btn-outline-primary">
                        عرض جميع المواعيد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Patients -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2 text-primary"></i>المرضى الجدد
                </h5>
                <a href="{% url 'simple_patients' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_patients %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم المريض</th>
                                    <th>العمر</th>
                                    <th>الجنس</th>
                                    <th>الهدف</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in recent_patients %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {% if patient.user.first_name %}
                                                    {{ patient.user.first_name|first|upper }}
                                                {% else %}
                                                    {{ patient.user.username|first|upper }}
                                                {% endif %}
                                            </div>
                                            <div>
                                                <div class="fw-bold">
                                                    {% if patient.user.first_name and patient.user.last_name %}
                                                        {{ patient.user.first_name }} {{ patient.user.last_name }}
                                                    {% else %}
                                                        {{ patient.user.username }}
                                                    {% endif %}
                                                </div>
                                                <small class="text-muted">{{ patient.user.email|default:"لا يوجد إيميل" }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">غير محدد</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                            {{ patient.get_gender_display }}
                                        </span>
                                    </td>
                                    <td>{{ patient.get_health_goal_display }}</td>
                                    <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'simple_patients' %}" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/nutrition/" class="btn btn-outline-success btn-sm" title="إنشاء خطة غذائية">
                                                <i class="fas fa-utensils"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-plus fa-3x mb-3 text-muted"></i>
                        <h5>لا يوجد مرضى بعد</h5>
                        <p>ابدأ بإضافة مرضى جدد لعيادتك</p>
                        <a href="{% url 'simple_patients' %}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
</div> <!-- إغلاق dashboard-content -->

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* تحسين الأداء - تقليل الرسوم المتحركة على الأجهزة البطيئة */
@media (prefers-reduced-motion: reduce) {
    .card,
    .btn-outline-primary,
    .btn-outline-success,
    .btn-outline-info {
        transition: none;
    }

    .card:hover,
    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-info:hover {
        transform: none;
    }
}

/* تحسين الأداء للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء loading indicator عند تحميل الصفحة
    const loadingIndicator = document.getElementById('loading-indicator');
    const dashboardContent = document.getElementById('dashboard-content');

    if (loadingIndicator) {
        loadingIndicator.classList.add('d-none');
    }

    if (dashboardContent) {
        dashboardContent.classList.remove('d-none');
    }

    // تحسين الأداء - lazy loading للصور
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // تحسين الأداء - تأخير تحميل المحتوى غير الضروري
    setTimeout(() => {
        // تحميل المحتوى الإضافي هنا إذا لزم الأمر
        console.log('تم تحميل لوحة التحكم بنجاح');
    }, 100);
});

// تحسين الأداء - منع إعادة التحميل غير الضرورية
window.addEventListener('beforeunload', function() {
    // حفظ حالة الصفحة في localStorage إذا لزم الأمر
});
</script>
{% endblock %}
