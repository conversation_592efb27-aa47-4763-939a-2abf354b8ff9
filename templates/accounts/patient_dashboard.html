{% extends 'base.html' %}

{% block title %}لوحة تحكم المريض - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-tachometer-alt me-2"></i>مرحباً {{ patient.user.get_full_name }}
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="{% url 'profile' %}" class="btn btn-outline-primary">
                    <i class="fas fa-user-edit me-1"></i>تعديل الملف الشخصي
                </a>
            </div>
        </div>
    </div>

    <!-- Patient Stats -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stats-number">{{ patient.current_weight }}</div>
                            <div class="text-white-50">الوزن الحالي (كغ)</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-weight fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stats-number">{{ patient.target_weight|default:"غير محدد" }}</div>
                            <div class="text-white-50">الوزن المستهدف (كغ)</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullseye fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stats-number">
                                {% if patient.bmi %}
                                    {{ patient.bmi }}
                                {% else %}
                                    غير محسوب
                                {% endif %}
                            </div>
                            <div class="text-white-50">مؤشر كتلة الجسم</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stats-number">{{ recent_weights.count }}</div>
                            <div class="text-white-50">سجلات الوزن</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Nutrition Plan -->
    {% if active_plan %}
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-utensils me-2"></i>خطتك الغذائية النشطة
                    </h5>
                    <span class="badge bg-light text-success">نشطة</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <h4 class="text-success">{{ active_plan.title }}</h4>
                            <p class="text-muted mb-3">{{ active_plan.description }}</p>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <h5 class="text-primary mb-1">{{ active_plan.target_calories }}</h5>
                                        <small class="text-muted">السعرات اليومية</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <h5 class="text-success mb-1">{{ active_plan.target_protein }}غ</h5>
                                        <small class="text-muted">البروتين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <h5 class="text-warning mb-1">{{ active_plan.target_carbs }}غ</h5>
                                        <small class="text-muted">الكربوهيدرات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <h5 class="text-info mb-1">{{ active_plan.target_fat }}غ</h5>
                                        <small class="text-muted">الدهون</small>
                                    </div>
                                </div>
                            </div>
                            
                            {% if active_plan.instructions %}
                            <div class="alert alert-info" role="alert">
                                <h6 class="alert-heading">تعليمات خاصة:</h6>
                                {{ active_plan.instructions }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="text-center">
                                <h6 class="text-muted mb-3">فترة الخطة</h6>
                                <div class="mb-3">
                                    <span class="badge bg-primary">{{ active_plan.start_date }}</span>
                                    <i class="fas fa-arrow-right mx-2"></i>
                                    <span class="badge bg-primary">{{ active_plan.end_date }}</span>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="#nutrition_plan_detail" class="btn btn-success">
                                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                    </a>
                                    {% if active_plan.pdf_file %}
                                    <a href="{{ active_plan.pdf_file.url }}" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-download me-2"></i>تحميل PDF
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                    <h5>لا توجد خطة غذائية نشطة</h5>
                    <p class="text-muted">لم يتم إنشاء خطة غذائية لك بعد. يرجى التواصل مع طبيبك.</p>
                    <a href="#contact_doctor" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>التواصل مع الطبيب
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recent Weight Records -->
    <div class="row g-4">
        <div class="col-lg-8">
            {% if recent_weights %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2 text-primary"></i>سجلات الوزن الأخيرة
                    </h5>
                    <a href="#weight_progress" class="btn btn-sm btn-outline-primary">
                        عرض التقرير الكامل
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوزن</th>
                                    <th>التغيير</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recent_weights %}
                                <tr>
                                    <td>{{ record.recorded_date|date:"Y-m-d" }}</td>
                                    <td>{{ record.weight }} كغ</td>
                                    <td>
                                        <span class="text-muted">-</span>
                                    </td>
                                    <td>{{ record.notes|default:"-"|truncatechars:30 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-4">
                    <i class="fas fa-weight fa-3x text-muted mb-3"></i>
                    <h5>لا توجد سجلات وزن</h5>
                    <p class="text-muted">لم يتم تسجيل أي أوزان بعد.</p>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-lg-4">
            <!-- Doctor Information -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user-md me-2"></i>طبيبك المعالج
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem;">
                            {{ patient.doctor.first_name|first }}{{ patient.doctor.last_name|first }}
                        </div>
                    </div>
                    <h5 class="mb-1">د. {{ patient.doctor.get_full_name }}</h5>
                    {% if patient.doctor.doctor_profile %}
                        <p class="text-muted small">{{ patient.doctor.doctor_profile.specialization }}</p>
                        <p class="text-muted small">{{ patient.doctor.doctor_profile.clinic_name }}</p>
                    {% endif %}
                    
                    <div class="d-grid gap-2 mt-3">
                        <a href="#send_message" class="btn btn-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>إرسال رسالة
                        </a>
                        <a href="tel:{{ patient.doctor.phone_number }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-phone me-2"></i>اتصال
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Tips -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            اشرب 8 أكواب ماء يومياً
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تناول وجباتك في أوقات منتظمة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            مارس الرياضة 30 دقيقة يومياً
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            سجل وزنك أسبوعياً
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
