{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}الملف الشخصي - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.user_type == 'doctor' %}
            <a href="{% url 'doctor_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
            </a>
        {% elif user.user_type == 'patient' %}
            <a href="{% url 'patient_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
            </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Personal Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-user me-2"></i>المعلومات الشخصية
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.first_name.id_for_label }}" class="form-label">الاسم الأول *</label>
                            {{ user_form.first_name|add_class:"form-control" }}
                            {% if user_form.first_name.errors %}
                                <div class="text-danger small mt-1">{{ user_form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.last_name.id_for_label }}" class="form-label">اسم العائلة *</label>
                            {{ user_form.last_name|add_class:"form-control" }}
                            {% if user_form.last_name.errors %}
                                <div class="text-danger small mt-1">{{ user_form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.email.id_for_label }}" class="form-label">البريد الإلكتروني *</label>
                            {{ user_form.email|add_class:"form-control" }}
                            {% if user_form.email.errors %}
                                <div class="text-danger small mt-1">{{ user_form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.date_of_birth.id_for_label }}" class="form-label">تاريخ الميلاد</label>
                            {{ user_form.date_of_birth|add_class:"form-control" }}
                            {% if user_form.date_of_birth.errors %}
                                <div class="text-danger small mt-1">{{ user_form.date_of_birth.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.phone_number.id_for_label }}" class="form-label">رقم الهاتف</label>
                            {{ user_form.phone_number|add_class:"form-control" }}
                            {% if user_form.phone_number.errors %}
                                <div class="text-danger small mt-1">{{ user_form.phone_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ user_form.whatsapp_number.id_for_label }}" class="form-label">رقم الواتساب</label>
                            {{ user_form.whatsapp_number|add_class:"form-control" }}
                            {% if user_form.whatsapp_number.errors %}
                                <div class="text-danger small mt-1">{{ user_form.whatsapp_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ user_form.profile_picture.id_for_label }}" class="form-label">صورة الملف الشخصي</label>
                            {{ user_form.profile_picture|add_class:"form-control" }}
                            {% if user_form.profile_picture.errors %}
                                <div class="text-danger small mt-1">{{ user_form.profile_picture.errors.0 }}</div>
                            {% endif %}
                            {% if user.profile_picture %}
                                <div class="mt-2">
                                    <img src="{{ user.profile_picture.url }}" class="rounded" width="100" height="100" alt="الصورة الحالية">
                                    <small class="text-muted d-block">الصورة الحالية</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Doctor Professional Information -->
                    {% if user.user_type == 'doctor' and doctor_form %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-stethoscope me-2"></i>المعلومات المهنية
                            </h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ doctor_form.specialization.id_for_label }}" class="form-label">التخصص *</label>
                            {{ doctor_form.specialization|add_class:"form-control" }}
                            {% if doctor_form.specialization.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.specialization.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ doctor_form.license_number.id_for_label }}" class="form-label">رقم الترخيص *</label>
                            {{ doctor_form.license_number|add_class:"form-control" }}
                            {% if doctor_form.license_number.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.license_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ doctor_form.clinic_name.id_for_label }}" class="form-label">اسم العيادة *</label>
                            {{ doctor_form.clinic_name|add_class:"form-control" }}
                            {% if doctor_form.clinic_name.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.clinic_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ doctor_form.years_of_experience.id_for_label }}" class="form-label">سنوات الخبرة *</label>
                            {{ doctor_form.years_of_experience|add_class:"form-control" }}
                            {% if doctor_form.years_of_experience.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.years_of_experience.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ doctor_form.clinic_address.id_for_label }}" class="form-label">عنوان العيادة *</label>
                            {{ doctor_form.clinic_address|add_class:"form-control" }}
                            {% if doctor_form.clinic_address.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.clinic_address.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ doctor_form.bio.id_for_label }}" class="form-label">نبذة شخصية</label>
                            {{ doctor_form.bio|add_class:"form-control" }}
                            {% if doctor_form.bio.errors %}
                                <div class="text-danger small mt-1">{{ doctor_form.bio.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if user_form.non_field_errors or doctor_form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in user_form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                            {% if doctor_form %}
                                {% for error in doctor_form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        {% if user.user_type == 'doctor' %}
                            <a href="{% url 'doctor_dashboard' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        {% elif user.user_type == 'patient' %}
                            <a href="{% url 'patient_dashboard' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Account Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الحساب
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>اسم المستخدم:</strong></td>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <td><strong>نوع الحساب:</strong></td>
                        <td>
                            <span class="badge bg-{% if user.user_type == 'doctor' %}primary{% else %}success{% endif %}">
                                {{ user.get_user_type_display }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ user.date_joined|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر تسجيل دخول:</strong></td>
                        <td>{{ user.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول من قبل" }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Security -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>الأمان
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'password_reset' %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a>
                </div>
                
                <hr>
                
                <h6 class="text-warning mb-2">نصائح الأمان:</h6>
                <ul class="list-unstyled small">
                    <li class="mb-1">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم كلمة مرور قوية
                    </li>
                    <li class="mb-1">
                        <i class="fas fa-check text-success me-2"></i>
                        لا تشارك بيانات دخولك
                    </li>
                    <li class="mb-1">
                        <i class="fas fa-check text-success me-2"></i>
                        سجل خروجك بعد الانتهاء
                    </li>
                </ul>
            </div>
        </div>
        
        {% if user.user_type == 'patient' %}
        <!-- Patient Specific Info -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-success text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>معلومات طبية
                </h6>
            </div>
            <div class="card-body">
                {% if user.patient_profile %}
                    <p class="small text-muted mb-2">
                        <strong>الطبيب المعالج:</strong><br>
                        د. {{ user.patient_profile.doctor.get_full_name }}
                    </p>
                    <p class="small text-muted mb-2">
                        <strong>الهدف الصحي:</strong><br>
                        {{ user.patient_profile.get_health_goal_display }}
                    </p>
                    {% if user.patient_profile.bmi %}
                    <p class="small text-muted mb-0">
                        <strong>مؤشر كتلة الجسم:</strong><br>
                        {{ user.patient_profile.bmi }} ({{ user.patient_profile.bmi_category }})
                    </p>
                    {% endif %}
                {% else %}
                    <p class="text-muted small">لم يتم ربط حسابك بملف طبي بعد.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
