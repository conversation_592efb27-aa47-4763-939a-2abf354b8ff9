<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام العيادات - Safari</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            direction: rtl;
            margin: 0;
            background: #f5f5f5;
        }
        
        .container {
            display: -webkit-flex;
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            height: 100vh;
            padding: 20px 0;
            position: fixed;
            right: 0;
            top: 0;
        }
        
        .sidebar h2 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 0 20px;
        }
        
        .menu-item {
            display: block;
            color: white;
            padding: 15px 20px;
            margin-bottom: 5px;
            cursor: pointer;
            -webkit-user-select: none;
            user-select: none;
            border: none;
            background: #34495e;
            width: calc(100% - 40px);
            margin-left: 20px;
            margin-right: 20px;
            border-radius: 5px;
            font-size: 16px;
            text-align: right;
        }
        
        .menu-item:hover {
            background: #3498db;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .content {
            margin-right: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page.active {
            display: block;
        }
        
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            border: 1px solid #c3e6cb;
        }
        
        .cards {
            display: -webkit-flex;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            min-width: 200px;
            flex: 1;
        }
        
        .card:hover {
            background: #2980b9;
        }
        
        .card-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .financial-cards {
            display: -webkit-flex;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .financial-card {
            background: #27ae60;
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            min-width: 250px;
            flex: 1;
        }
        
        .financial-card.expense {
            background: #e74c3c;
        }
        
        .financial-card.profit {
            background: #f39c12;
        }
        
        .money-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .currency {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <div class="menu-item active" data-page="dashboard">
                📊 لوحة التحكم
            </div>
            
            <div class="menu-item" data-page="patients">
                👥 إدارة المرضى
            </div>
            
            <div class="menu-item" data-page="nutrition">
                🍽️ الخطط الغذائية
            </div>
            
            <div class="menu-item" data-page="prescriptions">
                💊 الوصفات الطبية
            </div>
            
            <div class="menu-item" data-page="accounting">
                💰 إدارة الحسابات
            </div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="page active">
                <h1>📊 لوحة التحكم</h1>
                <div class="success-msg">
                    ✅ مرحباً بك في نظام إدارة العيادات! النظام يعمل بشكل مثالي في Safari!
                </div>
                
                <div class="cards">
                    <div class="card" data-info="المرضى: 25 مريض مسجل">
                        <div class="card-number">25</div>
                        <div>إجمالي المرضى</div>
                    </div>
                    <div class="card" style="background: #e74c3c;" data-info="الخطط: 12 خطة نشطة">
                        <div class="card-number">12</div>
                        <div>خطط نشطة</div>
                    </div>
                    <div class="card" style="background: #27ae60;" data-info="المواعيد: 8 مواعيد اليوم">
                        <div class="card-number">8</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div class="card" style="background: #f39c12;" data-info="الرسائل: 45 رسالة جديدة">
                        <div class="card-number">45</div>
                        <div>رسائل جديدة</div>
                    </div>
                </div>
                
                <h3>🎯 ميزات النظام:</h3>
                <ul style="line-height: 2;">
                    <li>✅ إدارة شاملة للمرضى والملفات الطبية</li>
                    <li>✅ كتابة وطباعة الوصفات الطبية</li>
                    <li>✅ نظام محاسبي متكامل بالدينار العراقي</li>
                    <li>✅ إدارة الخطط الغذائية المتخصصة</li>
                    <li>✅ تكامل مع الواتساب لإرسال الرسائل</li>
                </ul>
            </div>
            
            <!-- Patients -->
            <div id="patients" class="page">
                <h1>👥 إدارة المرضى</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>
                
                <h3>🔧 الوظائف المتاحة:</h3>
                <ul style="line-height: 2;">
                    <li>إضافة مرضى جدد مع جميع البيانات</li>
                    <li>تعديل وتحديث بيانات المرضى</li>
                    <li>عرض التاريخ الطبي الكامل</li>
                    <li>متابعة تقدم المرضى</li>
                    <li>إنشاء خطط غذائية مخصصة</li>
                </ul>
                
                <button class="btn btn-success" data-action="إضافة مريض جديد">
                    ➕ إضافة مريض جديد
                </button>
                <button class="btn" data-action="عرض قائمة المرضى">
                    📋 عرض قائمة المرضى
                </button>
                <button class="btn" data-action="البحث عن مريض">
                    🔍 البحث عن مريض
                </button>
            </div>
            
            <!-- Nutrition -->
            <div id="nutrition" class="page">
                <h1>🍽️ الخطط الغذائية</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>
                
                <div class="cards">
                    <div class="card" style="background: #e74c3c;" data-info="خطط إنقاص الوزن: 12 خطة نشطة">
                        <div class="card-number">12</div>
                        <div>خطط إنقاص الوزن</div>
                    </div>
                    <div class="card" style="background: #27ae60;" data-info="خطط زيادة الوزن: 8 خطط نشطة">
                        <div class="card-number">8</div>
                        <div>خطط زيادة الوزن</div>
                    </div>
                    <div class="card" style="background: #f39c12;" data-info="خطط الحفاظ على الوزن: 5 خطط نشطة">
                        <div class="card-number">5</div>
                        <div>خطط الحفاظ على الوزن</div>
                    </div>
                </div>
                
                <button class="btn btn-success" data-action="إنشاء خطة غذائية جديدة">
                    ➕ إنشاء خطة جديدة
                </button>
                <button class="btn" id="show-templates">
                    📚 عرض القوالب الجاهزة
                </button>

                <!-- قسم القوالب الجاهزة -->
                <div id="templates-section" style="display: none; margin-top: 30px; background: #f8f9fa; padding: 25px; border-radius: 15px; border: 2px solid #e9ecef;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">📚 القوالب الغذائية الجاهزة</h3>

                    <div class="cards">
                        <div class="card" style="background: #e74c3c;" id="weight-loss-template">
                            <div style="font-size: 2rem; margin-bottom: 10px;">⚖️</div>
                            <h4>قالب إنقاص الوزن</h4>
                            <p style="font-size: 14px; margin-top: 10px;">1200-1500 سعرة حرارية</p>
                        </div>
                        <div class="card" style="background: #27ae60;" id="weight-gain-template">
                            <div style="font-size: 2rem; margin-bottom: 10px;">📈</div>
                            <h4>قالب زيادة الوزن</h4>
                            <p style="font-size: 14px; margin-top: 10px;">2500-3000 سعرة حرارية</p>
                        </div>
                        <div class="card" style="background: #f39c12;" id="maintenance-template">
                            <div style="font-size: 2rem; margin-bottom: 10px;">⚖️</div>
                            <h4>قالب الحفاظ على الوزن</h4>
                            <p style="font-size: 14px; margin-top: 10px;">1800-2200 سعرة حرارية</p>
                        </div>
                        <div class="card" style="background: #9b59b6;" id="diabetic-template">
                            <div style="font-size: 2rem; margin-bottom: 10px;">🩺</div>
                            <h4>قالب مرضى السكري</h4>
                            <p style="font-size: 14px; margin-top: 10px;">منخفض الكربوهيدرات</p>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn" id="hide-templates" style="background: #6c757d;">
                            ❌ إخفاء القوالب
                        </button>
                    </div>
                </div>

                <!-- نافذة تفاصيل القالب -->
                <div id="template-details" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); z-index: 1000; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 id="template-title" style="color: #2c3e50; margin-bottom: 10px;">قالب غذائي</h2>
                        <p id="template-description" style="color: #666; font-size: 16px;"></p>
                    </div>

                    <div id="template-content">
                        <!-- سيتم ملء المحتوى بـ JavaScript -->
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" id="use-template">
                            ✅ استخدام هذا القالب
                        </button>
                        <button class="btn" id="print-template" style="background: #17a2b8;">
                            🖨️ طباعة القالب
                        </button>
                        <button class="btn" id="close-template" style="background: #6c757d;">
                            ❌ إغلاق
                        </button>
                    </div>
                </div>

                <!-- خلفية النافذة المنبثقة -->
                <div id="template-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999;"></div>
            </div>
            
            <!-- Prescriptions -->
            <div id="prescriptions" class="page">
                <h1>💊 الوصفات الطبية</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>
                
                <div class="cards">
                    <div class="card" data-info="وصفات اليوم: 8 وصفات مكتوبة">
                        <div class="card-number">8</div>
                        <div>وصفات اليوم</div>
                    </div>
                    <div class="card" style="background: #9b59b6;" data-info="إجمالي الوصفات: 156 وصفة في الأرشيف">
                        <div class="card-number">156</div>
                        <div>إجمالي الوصفات</div>
                    </div>
                    <div class="card" style="background: #1abc9c;" data-info="وصفات مطبوعة: 142 وصفة تم طباعتها">
                        <div class="card-number">142</div>
                        <div>وصفات مطبوعة</div>
                    </div>
                </div>
                
                <button class="btn btn-success" data-action="كتابة وصفة طبية جديدة">
                    📝 كتابة وصفة جديدة
                </button>
                <button class="btn" id="print-prescription">
                    🖨️ طباعة وصفة تجريبية
                </button>
                <button class="btn" data-action="عرض أرشيف الوصفات">
                    📚 أرشيف الوصفات
                </button>
            </div>
            
            <!-- Accounting -->
            <div id="accounting" class="page">
                <h1>💰 إدارة الحسابات</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>
                
                <div class="financial-cards">
                    <div class="financial-card">
                        <h3>💚 إجمالي الإيرادات</h3>
                        <div class="money-amount">58,500,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">+15% هذا الشهر</small>
                    </div>
                    <div class="financial-card expense">
                        <h3>💸 إجمالي المصروفات</h3>
                        <div class="money-amount">24,050,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">-5% عن الشهر الماضي</small>
                    </div>
                    <div class="financial-card profit">
                        <h3>📈 صافي الربح</h3>
                        <div class="money-amount">34,450,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">+25% نمو ممتاز</small>
                    </div>
                </div>
                
                <h3>💼 الخدمات المالية:</h3>
                <ul style="line-height: 2;">
                    <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                    <li>إدارة الفواتير والمدفوعات</li>
                    <li>تقارير مالية شهرية ومفصلة</li>
                    <li>حساب الضرائب المستحقة</li>
                    <li>تحليل الأداء المالي</li>
                </ul>
                
                <button class="btn btn-success" data-action="إضافة معاملة مالية جديدة">
                    ➕ إضافة معاملة جديدة
                </button>
                <button class="btn" data-action="عرض التقارير المالية">
                    📊 عرض التقارير المالية
                </button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        var currentPage = 'dashboard';
        
        // دالة إظهار الصفحات
        function showPage(pageId) {
            // إخفاء جميع الصفحات
            var pages = document.querySelectorAll('.page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }
            
            // إزالة active من جميع أزرار القائمة
            var menuItems = document.querySelectorAll('.menu-item');
            for (var i = 0; i < menuItems.length; i++) {
                menuItems[i].classList.remove('active');
            }
            
            // إظهار الصفحة المحددة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // تفعيل زر القائمة المحدد
            var activeMenuItem = document.querySelector('[data-page="' + pageId + '"]');
            if (activeMenuItem) {
                activeMenuItem.classList.add('active');
            }
            
            currentPage = pageId;
            
            // رسالة تأكيد
            var pageNames = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'prescriptions': 'الوصفات الطبية',
                'accounting': 'إدارة الحسابات'
            };
            
            alert('✅ تم الانتقال إلى: ' + pageNames[pageId]);
        }
        
        // دالة طباعة الوصفة
        function printPrescription() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            var content = '<!DOCTYPE html><html dir="rtl" lang="ar"><head><meta charset="UTF-8"><title>وصفة طبية</title><style>body{font-family:Arial;padding:20px;direction:rtl;}.header{text-align:center;border-bottom:2px solid #333;padding-bottom:20px;margin-bottom:20px;}.patient{background:#f5f5f5;padding:15px;margin:20px 0;border-radius:5px;}.medicine{border:1px solid #ddd;padding:10px;margin:10px 0;border-radius:5px;}</style></head><body><div class="header"><h2>🏥 عيادة التغذية العلاجية</h2><p>د. [اسم الطبيب] - أخصائي التغذية</p></div><div class="patient"><strong>المريض:</strong> أحمد محمد - 35 سنة<br><strong>التاريخ:</strong> ' + new Date().toLocaleDateString() + '<br><strong>التشخيص:</strong> نقص فيتامين د</div><h3>الأدوية المطلوبة:</h3><div class="medicine"><strong>1. فيتامين د3 1000 وحدة</strong><br>الجرعة: حبة واحدة يومياً<br>المدة: 3 أشهر</div><div class="medicine"><strong>2. كالسيوم 500 ملغ</strong><br>الجرعة: حبة واحدة مساءً<br>المدة: شهر واحد</div><p><strong>التعليمات:</strong> التعرض لأشعة الشمس، شرب الحليب</p><div style="margin-top:50px;"><p>توقيع الطبيب: _______________</p></div><script>window.print();</script></body></html>';
            
            printWindow.document.write(content);
            printWindow.document.close();
        }
        
        // قوالب الخطط الغذائية
        var nutritionTemplates = {
            'weight-loss': {
                title: '⚖️ قالب إنقاص الوزن',
                description: 'خطة غذائية متوازنة لإنقاص الوزن بشكل صحي (1200-1500 سعرة حرارية)',
                meals: {
                    breakfast: {
                        title: 'الإفطار (300-400 سعرة)',
                        items: [
                            '2 شريحة خبز أسمر + ملعقة كبيرة لبنة قليلة الدسم',
                            'حبة خيار متوسطة + حبة طماطم',
                            'كوب شاي أخضر بدون سكر',
                            'حفنة صغيرة من اللوز (10 حبات)'
                        ]
                    },
                    snack1: {
                        title: 'وجبة خفيفة صباحية (100-150 سعرة)',
                        items: [
                            'حبة تفاح متوسطة',
                            'أو كوب زبادي قليل الدسم'
                        ]
                    },
                    lunch: {
                        title: 'الغداء (400-500 سعرة)',
                        items: [
                            '100 جرام دجاج مشوي منزوع الجلد',
                            'كوب أرز أسمر مسلوق',
                            'سلطة خضراء كبيرة بالليمون',
                            'كوب شوربة خضار'
                        ]
                    },
                    snack2: {
                        title: 'وجبة خفيفة مسائية (100-150 سعرة)',
                        items: [
                            'حبة برتقال متوسطة',
                            'أو كوب عصير طبيعي بدون سكر'
                        ]
                    },
                    dinner: {
                        title: 'العشاء (300-400 سعرة)',
                        items: [
                            '100 جرام سمك مشوي',
                            'كوب خضار مسلوقة',
                            'شريحة خبز أسمر',
                            'سلطة خضراء صغيرة'
                        ]
                    }
                },
                tips: [
                    'شرب 8-10 أكواب ماء يومياً',
                    'ممارسة المشي 30 دقيقة يومياً',
                    'تجنب السكريات والدهون المشبعة',
                    'تناول الطعام ببطء ومضغ جيد'
                ]
            },
            'weight-gain': {
                title: '📈 قالب زيادة الوزن',
                description: 'خطة غذائية غنية بالسعرات لزيادة الوزن بشكل صحي (2500-3000 سعرة حرارية)',
                meals: {
                    breakfast: {
                        title: 'الإفطار (600-700 سعرة)',
                        items: [
                            '3 شرائح خبز أسمر + 2 ملعقة كبيرة زبدة فول سوداني',
                            'كوب حليب كامل الدسم + ملعقة عسل',
                            'حبة موز + حفنة تمر',
                            'عجة بيضتين بالزيت'
                        ]
                    },
                    snack1: {
                        title: 'وجبة خفيفة صباحية (300-400 سعرة)',
                        items: [
                            'كوب عصير طبيعي + ملعقة عسل',
                            'حفنة مكسرات مشكلة',
                            'قطعة كيك منزلي'
                        ]
                    },
                    lunch: {
                        title: 'الغداء (800-900 سعرة)',
                        items: [
                            '150 جرام لحم أحمر مشوي',
                            'كوب ونصف أرز أبيض',
                            'كوب فاصولياء بالزيت',
                            'سلطة بالزيت والليمون',
                            'كوب عصير طبيعي'
                        ]
                    },
                    snack2: {
                        title: 'وجبة خفيفة مسائية (300-400 سعرة)',
                        items: [
                            'ساندويش جبنة وزيتون',
                            'كوب حليب بالشوكولاتة',
                            'حبة أفوكادو'
                        ]
                    },
                    dinner: {
                        title: 'العشاء (500-600 سعرة)',
                        items: [
                            '150 جرام دجاج مشوي',
                            'كوب مكرونة بالصلصة',
                            'سلطة خضراء بالزيت',
                            'كوب زبادي كامل الدسم'
                        ]
                    }
                },
                tips: [
                    'تناول 5-6 وجبات صغيرة يومياً',
                    'شرب السوائل بين الوجبات وليس معها',
                    'إضافة الزيوت الصحية للطعام',
                    'ممارسة تمارين المقاومة'
                ]
            },
            'maintenance': {
                title: '⚖️ قالب الحفاظ على الوزن',
                description: 'خطة غذائية متوازنة للحفاظ على الوزن المثالي (1800-2200 سعرة حرارية)',
                meals: {
                    breakfast: {
                        title: 'الإفطار (400-500 سعرة)',
                        items: [
                            '2 شريحة خبز أسمر + جبنة قليلة الدسم',
                            'كوب حليب قليل الدسم',
                            'حبة فاكهة موسمية',
                            'ملعقة صغيرة عسل'
                        ]
                    },
                    snack1: {
                        title: 'وجبة خفيفة صباحية (150-200 سعرة)',
                        items: [
                            'كوب زبادي + ملعقة شوفان',
                            'أو حفنة مكسرات صغيرة'
                        ]
                    },
                    lunch: {
                        title: 'الغداء (600-700 سعرة)',
                        items: [
                            '120 جرام بروتين (دجاج/سمك/لحم)',
                            'كوب أرز أو مكرونة',
                            'كوب خضار مطبوخة',
                            'سلطة متنوعة',
                            'ملعقة زيت زيتون'
                        ]
                    },
                    snack2: {
                        title: 'وجبة خفيفة مسائية (150-200 سعرة)',
                        items: [
                            'حبة فاكهة + كوب شاي',
                            'أو قطعة شوكولاتة داكنة صغيرة'
                        ]
                    },
                    dinner: {
                        title: 'العشاء (500-600 سعرة)',
                        items: [
                            '100 جرام بروتين خفيف',
                            'كوب خضار مشكلة',
                            'شريحة خبز أسمر',
                            'سلطة خضراء'
                        ]
                    }
                },
                tips: [
                    'الحفاظ على نشاط بدني منتظم',
                    'شرب الماء بكميات كافية',
                    'تناول وجبات منتظمة',
                    'مراقبة الوزن أسبوعياً'
                ]
            },
            'diabetic': {
                title: '🩺 قالب مرضى السكري',
                description: 'خطة غذائية خاصة لمرضى السكري مع التحكم في الكربوهيدرات',
                meals: {
                    breakfast: {
                        title: 'الإفطار (350-400 سعرة)',
                        items: [
                            'شريحة خبز أسمر + بيضة مسلوقة',
                            'كوب حليب خالي الدسم بدون سكر',
                            'حبة خيار + طماطم',
                            'ملعقة صغيرة زيت زيتون'
                        ]
                    },
                    snack1: {
                        title: 'وجبة خفيفة صباحية (100-120 سعرة)',
                        items: [
                            'حفنة لوز نيء (10 حبات)',
                            'أو كوب زبادي خالي الدسم'
                        ]
                    },
                    lunch: {
                        title: 'الغداء (450-500 سعرة)',
                        items: [
                            '100 جرام دجاج مشوي',
                            'نصف كوب أرز أسمر',
                            'كوب خضار ورقية',
                            'سلطة خضراء بالليمون',
                            'كوب شوربة خضار'
                        ]
                    },
                    snack2: {
                        title: 'وجبة خفيفة مسائية (80-100 سعرة)',
                        items: [
                            'حبة تفاح صغيرة',
                            'أو كوب شاي أخضر + 2 حبة تمر'
                        ]
                    },
                    dinner: {
                        title: 'العشاء (350-400 سعرة)',
                        items: [
                            '100 جرام سمك مشوي',
                            'كوب خضار مسلوقة',
                            'سلطة خضراء كبيرة',
                            'ملعقة صغيرة زيت زيتون'
                        ]
                    }
                },
                tips: [
                    'مراقبة مستوى السكر بانتظام',
                    'تجنب السكريات البسيطة',
                    'تناول الألياف بكثرة',
                    'ممارسة الرياضة بانتظام',
                    'شرب الماء بدلاً من العصائر'
                ]
            }
        };

        // دالة عرض تفاصيل القالب
        function showTemplateDetails(templateId) {
            var template = nutritionTemplates[templateId];
            if (!template) return;

            document.getElementById('template-title').textContent = template.title;
            document.getElementById('template-description').textContent = template.description;

            var content = '<div style="text-align: right;">';

            // عرض الوجبات
            for (var mealKey in template.meals) {
                var meal = template.meals[mealKey];
                content += '<div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 10px;">';
                content += '<h4 style="color: #2c3e50; margin-bottom: 10px;">' + meal.title + '</h4>';
                content += '<ul style="margin: 0; padding-right: 20px;">';
                for (var i = 0; i < meal.items.length; i++) {
                    content += '<li style="margin-bottom: 5px;">' + meal.items[i] + '</li>';
                }
                content += '</ul></div>';
            }

            // عرض النصائح
            content += '<div style="background: #e7f3ff; padding: 15px; border-radius: 10px; border-right: 4px solid #007bff;">';
            content += '<h4 style="color: #007bff; margin-bottom: 10px;">💡 نصائح مهمة:</h4>';
            content += '<ul style="margin: 0; padding-right: 20px;">';
            for (var i = 0; i < template.tips.length; i++) {
                content += '<li style="margin-bottom: 5px;">' + template.tips[i] + '</li>';
            }
            content += '</ul></div>';

            content += '</div>';

            document.getElementById('template-content').innerHTML = content;
            document.getElementById('template-details').style.display = 'block';
            document.getElementById('template-overlay').style.display = 'block';

            // حفظ معرف القالب الحالي
            document.getElementById('template-details').setAttribute('data-current-template', templateId);
        }

        // دالة طباعة القالب
        function printTemplate() {
            var templateId = document.getElementById('template-details').getAttribute('data-current-template');
            var template = nutritionTemplates[templateId];
            if (!template) return;

            var printWindow = window.open('', '_blank', 'width=800,height=600');
            var content = '<!DOCTYPE html><html dir="rtl" lang="ar"><head><meta charset="UTF-8"><title>' + template.title + '</title><style>body{font-family:Arial;padding:20px;direction:rtl;line-height:1.6;}.header{text-align:center;border-bottom:3px solid #2c3e50;padding-bottom:20px;margin-bottom:30px;}.meal{background:#f8f9fa;padding:15px;margin:15px 0;border-radius:10px;border-right:4px solid #007bff;}.tips{background:#e7f3ff;padding:15px;border-radius:10px;border-right:4px solid #007bff;margin-top:20px;}ul{padding-right:20px;}li{margin-bottom:8px;}</style></head><body>';

            content += '<div class="header"><h1>' + template.title + '</h1><p>' + template.description + '</p><p><strong>تاريخ الطباعة:</strong> ' + new Date().toLocaleDateString('ar-SA') + '</p></div>';

            for (var mealKey in template.meals) {
                var meal = template.meals[mealKey];
                content += '<div class="meal"><h3>' + meal.title + '</h3><ul>';
                for (var i = 0; i < meal.items.length; i++) {
                    content += '<li>' + meal.items[i] + '</li>';
                }
                content += '</ul></div>';
            }

            content += '<div class="tips"><h3>💡 نصائح مهمة:</h3><ul>';
            for (var i = 0; i < template.tips.length; i++) {
                content += '<li>' + template.tips[i] + '</li>';
            }
            content += '</ul></div>';

            content += '<div style="margin-top:50px;text-align:center;"><p>أخصائي التغذية: _______________</p><p>التوقيع: _______________</p></div>';
            content += '<script>window.onload=function(){window.print();};</script></body></html>';

            printWindow.document.write(content);
            printWindow.document.close();
        }

        // إعداد مستمعي الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // مستمعي أحداث أزرار القائمة
            var menuItems = document.querySelectorAll('.menu-item');
            for (var i = 0; i < menuItems.length; i++) {
                menuItems[i].addEventListener('click', function() {
                    var pageId = this.getAttribute('data-page');
                    showPage(pageId);
                });
            }
            
            // مستمعي أحداث البطاقات
            var cards = document.querySelectorAll('.card[data-info]');
            for (var i = 0; i < cards.length; i++) {
                cards[i].addEventListener('click', function() {
                    var info = this.getAttribute('data-info');
                    alert(info);
                });
            }
            
            // مستمعي أحداث الأزرار
            var buttons = document.querySelectorAll('.btn[data-action]');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].addEventListener('click', function() {
                    var action = this.getAttribute('data-action');
                    alert('تم تنفيذ: ' + action);
                });
            }
            
            // زر طباعة الوصفة
            var printBtn = document.getElementById('print-prescription');
            if (printBtn) {
                printBtn.addEventListener('click', printPrescription);
            }

            // أزرار القوالب الغذائية
            var showTemplatesBtn = document.getElementById('show-templates');
            if (showTemplatesBtn) {
                showTemplatesBtn.addEventListener('click', function() {
                    var templatesSection = document.getElementById('templates-section');
                    if (templatesSection.style.display === 'none') {
                        templatesSection.style.display = 'block';
                        this.textContent = '📚 إخفاء القوالب';
                    } else {
                        templatesSection.style.display = 'none';
                        this.textContent = '📚 عرض القوالب الجاهزة';
                    }
                });
            }

            var hideTemplatesBtn = document.getElementById('hide-templates');
            if (hideTemplatesBtn) {
                hideTemplatesBtn.addEventListener('click', function() {
                    document.getElementById('templates-section').style.display = 'none';
                    document.getElementById('show-templates').textContent = '📚 عرض القوالب الجاهزة';
                });
            }

            // أزرار القوالب الفردية
            var templateCards = ['weight-loss-template', 'weight-gain-template', 'maintenance-template', 'diabetic-template'];
            var templateIds = ['weight-loss', 'weight-gain', 'maintenance', 'diabetic'];

            for (var i = 0; i < templateCards.length; i++) {
                var card = document.getElementById(templateCards[i]);
                if (card) {
                    (function(templateId) {
                        card.addEventListener('click', function() {
                            showTemplateDetails(templateId);
                        });
                    })(templateIds[i]);
                }
            }

            // أزرار نافذة تفاصيل القالب
            var useTemplateBtn = document.getElementById('use-template');
            if (useTemplateBtn) {
                useTemplateBtn.addEventListener('click', function() {
                    var templateId = document.getElementById('template-details').getAttribute('data-current-template');
                    var template = nutritionTemplates[templateId];
                    alert('تم اختيار القالب: ' + template.title + '\n\nسيتم إنشاء خطة غذائية جديدة بناءً على هذا القالب.');
                    document.getElementById('template-details').style.display = 'none';
                    document.getElementById('template-overlay').style.display = 'none';
                });
            }

            var printTemplateBtn = document.getElementById('print-template');
            if (printTemplateBtn) {
                printTemplateBtn.addEventListener('click', printTemplate);
            }

            var closeTemplateBtn = document.getElementById('close-template');
            if (closeTemplateBtn) {
                closeTemplateBtn.addEventListener('click', function() {
                    document.getElementById('template-details').style.display = 'none';
                    document.getElementById('template-overlay').style.display = 'none';
                });
            }

            // إغلاق النافذة عند النقر على الخلفية
            var templateOverlay = document.getElementById('template-overlay');
            if (templateOverlay) {
                templateOverlay.addEventListener('click', function() {
                    document.getElementById('template-details').style.display = 'none';
                    document.getElementById('template-overlay').style.display = 'none';
                });
            }
            
            // رسالة ترحيب
            setTimeout(function() {
                alert('🎉 مرحباً بك في نظام إدارة العيادات!\n\nالنظام متوافق مع Safari ويعمل بشكل مثالي!\n\nجرب النقر على الأزرار في الـ Sidebar.');
            }, 1000);
        });
    </script>
</body>
</html>
