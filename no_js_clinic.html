<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات - بدون JavaScript</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            direction: rtl;
            margin: 0;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            height: 100vh;
            padding: 20px 0;
            position: fixed;
            right: 0;
            top: 0;
        }
        
        .sidebar h2 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 0 20px;
        }
        
        .nav-link {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            border-bottom: 1px solid #34495e;
            transition: background 0.3s;
        }
        
        .nav-link:hover {
            background: #3498db;
            text-decoration: none;
            color: white;
        }
        
        .nav-link:target {
            background: #3498db;
        }
        
        .content {
            margin-right: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page:target {
            display: block;
        }

        #dashboard {
            display: block;
        }

        /* إظهار القوالب عند استهدافها */
        #templates:target {
            display: block !important;
        }

        /* إخفاء الصفحات الأخرى عند استهداف القوالب */
        #templates:target ~ .page {
            display: none;
        }
        
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            border: 1px solid #c3e6cb;
        }
        
        .cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            min-width: 200px;
            flex: 1;
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            margin: 10px 5px;
            font-size: 16px;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            text-decoration: none;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .financial-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .financial-card {
            background: #27ae60;
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            min-width: 250px;
            flex: 1;
        }
        
        .financial-card.expense {
            background: #e74c3c;
        }
        
        .financial-card.profit {
            background: #f39c12;
        }
        
        .money-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .currency {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .templates {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }
        
        .template-card {
            background: #3498db;
            color: white;
            padding: 20px;
            margin: 10px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
        }
        
        .template-details {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #3498db;
        }
        
        .meal-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            border-right: 4px solid #007bff;
        }
        
        .tips-section {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #007bff;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header" style="text-align: center; padding: 20px 10px; border-bottom: 2px solid rgba(255,255,255,0.1); margin-bottom: 20px;">
                <div class="sidebar-logo" style="width: 60px; height: 60px; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                    <span style="font-size: 24px; color: white;">🏥</span>
                </div>
                <h2 class="sidebar-clinic-name" style="margin: 0 0 8px 0; font-size: 18px; color: white;">عيادة الدكتور أحمد محمد علي</h2>
                <p class="sidebar-doctor-name" style="margin: 0; font-size: 12px; color: rgba(255,255,255,0.8);">👨‍⚕️ د. أحمد محمد علي</p>
            </div>
            
            <a href="#dashboard" class="nav-link">
                📊 لوحة التحكم
            </a>
            
            <a href="#patients" class="nav-link">
                👥 إدارة المرضى
            </a>

            <a href="#appointments" class="nav-link">
                📅 إدارة المواعيد
            </a>

            <a href="#nutrition" class="nav-link">
                🍽️ الخطط الغذائية
            </a>

            <a href="#prescriptions" class="nav-link">
                💊 الوصفات الطبية
            </a>

            <a href="#clinic-settings" class="nav-link">
                ⚙️ إعدادات العيادة
            </a>
            
            <a href="#accounting" class="nav-link">
                💰 إدارة الحسابات
            </a>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Header with Doctor Info and Logo -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <!-- شعار العيادة ومعلومات الطبيب -->
                    <div style="display: flex; align-items: center; gap: 20px;">
                        <!-- شعار العيادة -->
                        <div class="header-logo" style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid rgba(255,255,255,0.3); box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                            <span style="font-size: 36px;">🏥</span>
                        </div>
                        <!-- معلومات العيادة والطبيب -->
                        <div class="header-clinic-info">
                            <h1 class="clinic-name" style="margin: 0 0 8px 0; font-size: 28px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">عيادة الدكتور أحمد محمد علي</h1>
                            <p class="clinic-specialty" style="margin: 0 0 5px 0; font-size: 16px; opacity: 0.9;">أخصائي الطب الباطني والتغذية العلاجية</p>
                            <p class="clinic-contact" style="margin: 0; font-size: 14px; opacity: 0.8;">📍 شارع الملك فهد، الرياض | 📞 ************ | 📧 <EMAIL></p>
                        </div>
                    </div>

                    <!-- معلومات التاريخ والوقت -->
                    <div class="header-doctor-info" style="text-align: right; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(255,255,255,0.2);">
                        <div class="doctor-name" style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">👨‍⚕️ د. أحمد محمد علي</div>
                        <div class="license-number" style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">رقم الترخيص: 12345</div>
                        <div style="font-size: 14px; opacity: 0.8;">📅 <span id="current-date"></span></div>
                        <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">🕐 <span id="current-time"></span></div>
                        <button onclick="goHome()" class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 15px; font-size: 12px; margin-top: 10px; width: 100%;">
                            🏠 العودة للوحة التحكم
                        </button>
                    </div>
                </div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" class="page">
                <h1>📊 لوحة التحكم</h1>
                
                <div class="cards">
                    <div class="card">
                        <div class="card-number">25</div>
                        <div>إجمالي المرضى</div>
                    </div>
                    <div class="card" style="background: #e74c3c;">
                        <div class="card-number">12</div>
                        <div>خطط نشطة</div>
                    </div>
                    <div class="card" style="background: #27ae60;">
                        <div class="card-number">8</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div class="card" style="background: #f39c12;">
                        <div class="card-number">45</div>
                        <div>رسائل جديدة</div>
                    </div>
                </div>
                
                <h3>🎯 ميزات النظام:</h3>
                <ul style="line-height: 2;">
                    <li>✅ إدارة شاملة للمرضى والملفات الطبية</li>
                    <li>✅ كتابة وطباعة الوصفات الطبية</li>
                    <li>✅ نظام محاسبي متكامل بالدينار العراقي</li>
                    <li>✅ إدارة الخطط الغذائية المتخصصة</li>
                    <li>✅ قوالب غذائية جاهزة للاستخدام</li>
                </ul>
            </div>
            
            <!-- Patients -->
            <div id="patients" class="page">
                <h1>👥 إدارة المرضى</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>
                
                <h3>🔧 الوظائف المتاحة:</h3>
                <ul style="line-height: 2;">
                    <li>إضافة مرضى جدد مع جميع البيانات</li>
                    <li>تعديل وتحديث بيانات المرضى</li>
                    <li>عرض التاريخ الطبي الكامل</li>
                    <li>متابعة تقدم المرضى</li>
                    <li>إنشاء خطط غذائية مخصصة</li>
                </ul>
                
                <a href="#add-patient" class="btn btn-success">
                    ➕ إضافة مريض جديد
                </a>
                <a href="#patients-list" class="btn">
                    📋 عرض قائمة المرضى
                </a>
                <a href="#search-patient" class="btn">
                    🔍 البحث عن مريض
                </a>
            </div>

            <!-- Appointments -->
            <div id="appointments" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>📅 إدارة المواعيد</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة المواعيد بنجاح!
                </div>

                <!-- أزرار التحكم الرئيسية -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <a href="#new-appointment" class="btn btn-success" style="padding: 20px; text-align: center; font-size: 18px;">
                        ➕ حجز موعد جديد
                    </a>
                    <a href="#appointments-today" class="btn" style="background: #3498db; padding: 20px; text-align: center; font-size: 18px;">
                        📋 مواعيد اليوم
                    </a>
                    <a href="#appointments-calendar" class="btn" style="background: #9b59b6; padding: 20px; text-align: center; font-size: 18px;">
                        📅 التقويم الشهري
                    </a>
                    <a href="#whatsapp-reminders" class="btn" style="background: #25d366; padding: 20px; text-align: center; font-size: 18px;">
                        📱 تذكير واتساب
                    </a>
                </div>

                <!-- إحصائيات سريعة -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">8</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">3</div>
                        <div>مواعيد متأخرة</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">15</div>
                        <div>مواعيد الأسبوع</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">5</div>
                        <div>تذكيرات مرسلة</div>
                    </div>
                </div>

                <!-- مواعيد اليوم -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 25px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: #2c3e50; margin: 0; border-bottom: 2px solid #3498db; padding-bottom: 10px;">📋 مواعيد اليوم - <span id="today-date"></span></h3>
                        <div>
                            <button onclick="addTestAppointment()" class="btn" style="background: #28a745; padding: 8px 15px; font-size: 14px; margin-left: 10px;">➕ موعد واحد</button>
                            <button onclick="addMultipleTestAppointments()" class="btn" style="background: #6f42c1; padding: 8px 15px; font-size: 14px; margin-left: 10px;">🕐 مواعيد 24 ساعة</button>
                            <button onclick="refreshAppointments()" class="btn" style="background: #17a2b8; padding: 8px 15px; font-size: 14px;">🔄 تحديث</button>
                            <button onclick="clearAllAppointments()" class="btn" style="background: #dc3545; padding: 8px 15px; font-size: 14px;">🗑️ مسح الكل</button>
                        </div>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">الوقت</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">اسم المريض</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">العمر/الجنس</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">القياسات</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">الوظيفة</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">رقم الهاتف</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">نوع الزيارة</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #dee2e6;">الحالة</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #dee2e6;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="appointments-table-body">
                                <!-- سيتم ملء المواعيد هنا ديناميكياً -->
                                <tr>
                                    <td colspan="9" style="padding: 30px; text-align: center; color: #666;">
                                        📅 لا توجد مواعيد محجوزة لليوم<br>
                                        <small>احجز موعد جديد لرؤية البيانات هنا</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- New Appointment -->
            <div id="new-appointment" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>➕ حجز موعد جديد</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى صفحة حجز موعد جديد!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <form id="appointment-form">
                        <!-- بيانات المريض -->
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 18px;">👤 بيانات المريض</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">الاسم الكامل:</label>
                                    <input type="text" id="patient-name" placeholder="مثال: أحمد محمد علي" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">رقم الهاتف (مع كود البلد):</label>
                                    <input type="tel" id="patient-phone" placeholder="مثال: +966501234567" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;" required>
                                    <small style="color: #6c757d; font-size: 10px; line-height: 1.2;">
                                        🇸🇦 +966 | 🇦🇪 +971 | 🇰🇼 +965 | 🇶🇦 +974 | 🇧🇭 +973 | 🇴🇲 +968
                                    </small>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">العمر:</label>
                                    <input type="number" id="patient-age" placeholder="35" min="1" max="120" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">الجنس:</label>
                                    <select id="patient-gender" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                                        <option value="">اختر الجنس</option>
                                        <option value="ذكر">ذكر</option>
                                        <option value="أنثى">أنثى</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">الحالة الاجتماعية:</label>
                                    <select id="patient-marital-status" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                                        <option value="">اختر الحالة</option>
                                        <option value="أعزب">أعزب</option>
                                        <option value="متزوج">متزوج</option>
                                        <option value="مطلق">مطلق</option>
                                        <option value="أرمل">أرمل</option>
                                    </select>
                                </div>
                            </div>

                            <!-- القياسات الجسمية -->
                            <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 15px; border: 1px solid #28a745;">
                                <h4 style="color: #2c3e50; margin-bottom: 12px; text-align: center; font-size: 16px;">📏 القياسات الجسمية ومؤشر كتلة الجسم</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; font-size: 12px;">📏 الطول (سم):</label>
                                        <input type="number" id="patient-height" placeholder="170" min="100" max="250" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #28a745; border-radius: 4px; font-size: 14px;" oninput="calculateBMI()">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; font-size: 12px;">⚖️ الوزن (كجم):</label>
                                        <input type="number" id="patient-weight" placeholder="70" min="20" max="300" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #28a745; border-radius: 4px; font-size: 14px;" oninput="calculateBMI()">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; font-size: 12px;">📊 مؤشر كتلة الجسم:</label>
                                        <input type="text" id="patient-bmi" placeholder="تلقائي" readonly style="width: 100%; padding: 8px; border: 1px solid #6c757d; border-radius: 4px; font-size: 14px; background: #f8f9fa; font-weight: bold; text-align: center;">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; font-size: 12px;">📈 التصنيف:</label>
                                        <input type="text" id="bmi-category" placeholder="تلقائي" readonly style="width: 100%; padding: 8px; border: 1px solid #6c757d; border-radius: 4px; font-size: 12px; background: #f8f9fa; font-weight: bold; text-align: center;">
                                    </div>
                                </div>

                                <!-- مرجع مؤشر كتلة الجسم -->
                                <div style="background: white; padding: 8px; border-radius: 4px; margin-top: 10px; border: 1px solid #dee2e6;">
                                    <h5 style="color: #2c3e50; margin-bottom: 8px; text-align: center; font-size: 14px;">📋 مرجع التصنيف</h5>
                                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 6px; font-size: 10px; text-align: center;">
                                        <div style="background: #cce5ff; padding: 4px; border-radius: 3px; border: 1px solid #007bff;">
                                            <strong>نقص الوزن</strong><br>
                                            &lt; 18.5
                                        </div>
                                        <div style="background: #d4edda; padding: 4px; border-radius: 3px; border: 1px solid #28a745;">
                                            <strong>طبيعي</strong><br>
                                            18.5 - 24.9
                                        </div>
                                        <div style="background: #fff3cd; padding: 4px; border-radius: 3px; border: 1px solid #ffc107;">
                                            <strong>زيادة وزن</strong><br>
                                            25.0 - 29.9
                                        </div>
                                        <div style="background: #f8d7da; padding: 4px; border-radius: 3px; border: 1px solid #dc3545;">
                                            <strong>سمنة</strong><br>
                                            ≥ 30.0
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">🎓 المؤهل الدراسي:</label>
                                    <select id="patient-education" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                                        <option value="">اختر المؤهل</option>
                                        <option value="ابتدائي">ابتدائي</option>
                                        <option value="متوسط">متوسط</option>
                                        <option value="ثانوي">ثانوي</option>
                                        <option value="دبلوم">دبلوم</option>
                                        <option value="بكالوريوس">بكالوريوس</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">💼 الوظيفة الحالية:</label>
                                    <select id="patient-occupation" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                                        <option value="">اختر الوظيفة</option>
                                        <optgroup label="القطاع الحكومي">
                                            <option value="موظف حكومي">موظف حكومي</option>
                                            <option value="معلم">معلم</option>
                                            <option value="طبيب">طبيب</option>
                                            <option value="ممرض">ممرض</option>
                                            <option value="مهندس">مهندس</option>
                                            <option value="محاسب">محاسب</option>
                                            <option value="عسكري">عسكري</option>
                                            <option value="شرطي">شرطي</option>
                                        </optgroup>
                                        <optgroup label="القطاع الخاص">
                                            <option value="موظف قطاع خاص">موظف قطاع خاص</option>
                                            <option value="مدير">مدير</option>
                                            <option value="مبرمج">مبرمج</option>
                                            <option value="مصمم">مصمم</option>
                                            <option value="مسوق">مسوق</option>
                                            <option value="مبيعات">مبيعات</option>
                                            <option value="خدمة عملاء">خدمة عملاء</option>
                                        </optgroup>
                                        <optgroup label="أعمال حرة">
                                            <option value="رجل أعمال">رجل أعمال</option>
                                            <option value="تاجر">تاجر</option>
                                            <option value="مقاول">مقاول</option>
                                            <option value="حرفي">حرفي</option>
                                            <option value="سائق">سائق</option>
                                            <option value="عامل">عامل</option>
                                        </optgroup>
                                        <optgroup label="أخرى">
                                            <option value="طالب">طالب</option>
                                            <option value="ربة منزل">ربة منزل</option>
                                            <option value="متقاعد">متقاعد</option>
                                            <option value="عاطل عن العمل">عاطل عن العمل</option>
                                            <option value="أخرى">أخرى</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>

                            <!-- حقل مخصص للوظيفة -->
                            <div id="custom-occupation-field" style="margin-top: 10px; display: none;">
                                <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">تحديد الوظيفة:</label>
                                <input type="text" id="custom-occupation" placeholder="اكتب الوظيفة..." style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;">
                            </div>
                        </div>

                        <!-- الحالات المرضية والتاريخ الطبي -->
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ffc107;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 18px;">🏥 الحالات المرضية والتاريخ الطبي</h3>
                            <p style="margin-bottom: 15px; color: #856404; font-weight: bold; font-size: 14px;">يرجى الإجابة على الأسئلة التالية بصدق:</p>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <!-- العمود الأول -->
                                <div>
                                    <!-- الحساسية -->
                                    <div style="background: white; padding: 10px; border-radius: 6px; margin-bottom: 10px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; font-size: 13px;">🤧 هل تعاني من أي حساسية؟</label>
                                        <div style="display: flex; gap: 10px; margin-bottom: 8px;">
                                            <label style="display: flex; align-items: center; cursor: pointer; font-size: 12px;">
                                                <input type="radio" name="allergies" value="نعم" style="margin-left: 6px;" onchange="toggleDetails('allergies-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer; font-size: 12px;">
                                                <input type="radio" name="allergies" value="لا" style="margin-left: 6px;" onchange="toggleDetails('allergies-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="allergies-details" style="display: none;">
                                            <textarea placeholder="اذكر نوع الحساسية..." style="width: 100%; padding: 6px; border: 1px solid #e9ecef; border-radius: 4px; min-height: 40px; font-size: 12px;"></textarea>
                                        </div>
                                    </div>

                                    <!-- الأدوية الحالية -->
                                    <div style="background: white; padding: 10px; border-radius: 6px; margin-bottom: 10px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; font-size: 13px;">💊 هل تتناول أي أدوية حالياً؟</label>
                                        <div style="display: flex; gap: 10px; margin-bottom: 8px;">
                                            <label style="display: flex; align-items: center; cursor: pointer; font-size: 12px;">
                                                <input type="radio" name="medications" value="نعم" style="margin-left: 6px;" onchange="toggleDetails('medications-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer; font-size: 12px;">
                                                <input type="radio" name="medications" value="لا" style="margin-left: 6px;" onchange="toggleDetails('medications-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="medications-details" style="display: none;">
                                            <textarea placeholder="اذكر أسماء الأدوية..." style="width: 100%; padding: 6px; border: 1px solid #e9ecef; border-radius: 4px; min-height: 40px; font-size: 12px;"></textarea>
                                        </div>
                                    </div>

                                    <!-- العمليات الجراحية -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🏥 هل أجريت أي عمليات جراحية؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="surgeries" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('surgeries-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="surgeries" value="لا" style="margin-left: 8px;" onchange="toggleDetails('surgeries-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="surgeries-details" style="display: none;">
                                            <textarea placeholder="اذكر نوع العملية وتاريخها..." style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px;"></textarea>
                                        </div>
                                    </div>

                                    <!-- الأمراض المزمنة -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🩺 هل تعاني من أي أمراض مزمنة؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="chronic-diseases" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('chronic-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="chronic-diseases" value="لا" style="margin-left: 8px;" onchange="toggleDetails('chronic-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="chronic-details" style="display: none;">
                                            <textarea placeholder="مثل: السكري، ضغط الدم، أمراض القلب، الغدة الدرقية..." style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px;"></textarea>
                                        </div>
                                    </div>

                                    <!-- النشاط البدني -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🏃‍♂️ هل تمارس الرياضة بانتظام؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="exercise" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('exercise-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="exercise" value="لا" style="margin-left: 8px;" onchange="toggleDetails('exercise-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="exercise-details" style="display: none;">
                                            <textarea placeholder="اذكر نوع الرياضة وعدد مرات الممارسة أسبوعياً..." style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px;"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- العمود الثاني -->
                                <div>
                                    <!-- الحمل (للنساء) -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🤱 هل أنت حامل؟ (للنساء)</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="pregnancy" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('pregnancy-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="pregnancy" value="لا" style="margin-left: 8px;" onchange="toggleDetails('pregnancy-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="pregnancy" value="غير مطبق" style="margin-left: 8px;" onchange="toggleDetails('pregnancy-details', this.value === 'نعم')">
                                                <span>غير مطبق</span>
                                            </label>
                                        </div>
                                        <div id="pregnancy-details" style="display: none;">
                                            <input type="text" placeholder="في أي شهر من الحمل؟" style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px;">
                                        </div>
                                    </div>

                                    <!-- الرضاعة (للنساء) -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🍼 هل ترضعين طبيعياً؟ (للنساء)</label>
                                        <div style="display: flex; gap: 15px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="breastfeeding" value="نعم" style="margin-left: 8px;">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="breastfeeding" value="لا" style="margin-left: 8px;">
                                                <span>لا</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="breastfeeding" value="غير مطبق" style="margin-left: 8px;">
                                                <span>غير مطبق</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- التدخين -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🚬 هل تدخن؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="smoking" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('smoking-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="smoking" value="لا" style="margin-left: 8px;" onchange="toggleDetails('smoking-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="smoking" value="سابقاً" style="margin-left: 8px;" onchange="toggleDetails('smoking-details', this.value === 'نعم' || this.value === 'سابقاً')">
                                                <span>سابقاً</span>
                                            </label>
                                        </div>
                                        <div id="smoking-details" style="display: none;">
                                            <input type="text" placeholder="كم سيجارة يومياً؟ أو متى توقفت؟" style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px;">
                                        </div>
                                    </div>

                                    <!-- المشروبات الكحولية -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🍷 هل تتناول المشروبات الكحولية؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="alcohol" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('alcohol-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="alcohol" value="لا" style="margin-left: 8px;" onchange="toggleDetails('alcohol-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="alcohol" value="أحياناً" style="margin-left: 8px;" onchange="toggleDetails('alcohol-details', this.value === 'نعم' || this.value === 'أحياناً')">
                                                <span>أحياناً</span>
                                            </label>
                                        </div>
                                        <div id="alcohol-details" style="display: none;">
                                            <input type="text" placeholder="كم مرة في الأسبوع؟" style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px;">
                                        </div>
                                    </div>

                                    <!-- الخطط الغذائية السابقة -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">📋 هل اتبعت خطط غذائية سابقاً؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="previous-diets" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('diets-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="previous-diets" value="لا" style="margin-left: 8px;" onchange="toggleDetails('diets-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="diets-details" style="display: none;">
                                            <textarea placeholder="اذكر نوع الخطة الغذائية ونتائجها..." style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px;"></textarea>
                                        </div>
                                    </div>

                                    <!-- اضطرابات الأكل -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #e9ecef;">
                                        <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">🍽️ هل تعاني من اضطرابات في الأكل؟</label>
                                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="eating-disorders" value="نعم" style="margin-left: 8px;" onchange="toggleDetails('eating-details', this.value === 'نعم')">
                                                <span>نعم</span>
                                            </label>
                                            <label style="display: flex; align-items: center; cursor: pointer;">
                                                <input type="radio" name="eating-disorders" value="لا" style="margin-left: 8px;" onchange="toggleDetails('eating-details', this.value === 'نعم')">
                                                <span>لا</span>
                                            </label>
                                        </div>
                                        <div id="eating-details" style="display: none;">
                                            <textarea placeholder="مثل: فقدان الشهية، الشراهة، اضطرابات هضمية..." style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px;"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظات إضافية -->
                            <div style="background: white; padding: 15px; border-radius: 8px; margin-top: 20px; border: 1px solid #e9ecef;">
                                <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #2c3e50;">📝 ملاحظات طبية إضافية:</label>
                                <textarea id="medical-notes" placeholder="أي معلومات طبية أخرى تود إضافتها..." style="width: 100%; padding: 12px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 80px; resize: vertical;"></textarea>
                            </div>
                        </div>

                        <!-- تفاصيل الموعد -->
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 18px;">📅 تفاصيل الموعد</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">التاريخ:</label>
                                    <input type="date" id="appointment-date" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">الوقت (24 ساعة):</label>
                                    <select id="appointment-time" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;" required>
                                        <option value="">اختر الوقت</option>
                                        <!-- الفترة الصباحية -->
                                        <optgroup label="🌅 الفترة الصباحية (6:00 - 11:59)">
                                            <option value="06:00">06:00 - 6:00 ص</option>
                                            <option value="06:30">06:30 - 6:30 ص</option>
                                            <option value="07:00">07:00 - 7:00 ص</option>
                                            <option value="07:30">07:30 - 7:30 ص</option>
                                            <option value="08:00">08:00 - 8:00 ص</option>
                                            <option value="08:30">08:30 - 8:30 ص</option>
                                            <option value="09:00">09:00 - 9:00 ص</option>
                                            <option value="09:30">09:30 - 9:30 ص</option>
                                            <option value="10:00">10:00 - 10:00 ص</option>
                                            <option value="10:30">10:30 - 10:30 ص</option>
                                            <option value="11:00">11:00 - 11:00 ص</option>
                                            <option value="11:30">11:30 - 11:30 ص</option>
                                        </optgroup>

                                        <!-- فترة الظهر -->
                                        <optgroup label="☀️ فترة الظهر (12:00 - 17:59)">
                                            <option value="12:00">12:00 - 12:00 م</option>
                                            <option value="12:30">12:30 - 12:30 م</option>
                                            <option value="13:00">13:00 - 1:00 م</option>
                                            <option value="13:30">13:30 - 1:30 م</option>
                                            <option value="14:00">14:00 - 2:00 م</option>
                                            <option value="14:30">14:30 - 2:30 م</option>
                                            <option value="15:00">15:00 - 3:00 م</option>
                                            <option value="15:30">15:30 - 3:30 م</option>
                                            <option value="16:00">16:00 - 4:00 م</option>
                                            <option value="16:30">16:30 - 4:30 م</option>
                                            <option value="17:00">17:00 - 5:00 م</option>
                                            <option value="17:30">17:30 - 5:30 م</option>
                                        </optgroup>

                                        <!-- الفترة المسائية -->
                                        <optgroup label="🌆 الفترة المسائية (18:00 - 23:59)">
                                            <option value="18:00">18:00 - 6:00 م</option>
                                            <option value="18:30">18:30 - 6:30 م</option>
                                            <option value="19:00">19:00 - 7:00 م</option>
                                            <option value="19:30">19:30 - 7:30 م</option>
                                            <option value="20:00">20:00 - 8:00 م</option>
                                            <option value="20:30">20:30 - 8:30 م</option>
                                            <option value="21:00">21:00 - 9:00 م</option>
                                            <option value="21:30">21:30 - 9:30 م</option>
                                            <option value="22:00">22:00 - 10:00 م</option>
                                            <option value="22:30">22:30 - 10:30 م</option>
                                            <option value="23:00">23:00 - 11:00 م</option>
                                            <option value="23:30">23:30 - 11:30 م</option>
                                        </optgroup>

                                        <!-- الفترة الليلية -->
                                        <optgroup label="🌙 الفترة الليلية (00:00 - 05:59)">
                                            <option value="00:00">00:00 - 12:00 ص</option>
                                            <option value="00:30">00:30 - 12:30 ص</option>
                                            <option value="01:00">01:00 - 1:00 ص</option>
                                            <option value="01:30">01:30 - 1:30 ص</option>
                                            <option value="02:00">02:00 - 2:00 ص</option>
                                            <option value="02:30">02:30 - 2:30 ص</option>
                                            <option value="03:00">03:00 - 3:00 ص</option>
                                            <option value="03:30">03:30 - 3:30 ص</option>
                                            <option value="04:00">04:00 - 4:00 ص</option>
                                            <option value="04:30">04:30 - 4:30 ص</option>
                                            <option value="05:00">05:00 - 5:00 ص</option>
                                            <option value="05:30">05:30 - 5:30 ص</option>
                                        </optgroup>
                                    </select>
                                    <small style="color: #6c757d; font-size: 12px;">
                                        متاح الحجز على مدار 24 ساعة | الوقت بالنظام 24 ساعة مع عرض 12 ساعة للوضوح
                                    </small>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">نوع الزيارة:</label>
                                    <select id="visit-type" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px;" required>
                                        <option value="">اختر نوع الزيارة</option>
                                        <option value="كشف أولي">كشف أولي</option>
                                        <option value="متابعة">متابعة</option>
                                        <option value="استشارة تغذية">استشارة تغذية</option>
                                        <option value="فحص دوري">فحص دوري</option>
                                        <option value="طوارئ">طوارئ</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #2c3e50; font-size: 14px;">ملاحظات إضافية:</label>
                                <textarea id="appointment-notes" placeholder="أي ملاحظات خاصة بالموعد..." style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; font-size: 14px; min-height: 60px; resize: vertical;"></textarea>
                            </div>
                        </div>

                        <!-- خيارات التذكير -->
                        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 20px; border: 1px solid #25d366;">
                            <h3 style="color: #2c3e50; margin-bottom: 12px; font-size: 16px;">📱 تذكير واتساب</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: flex; align-items: center; cursor: pointer; margin-bottom: 8px;">
                                        <input type="checkbox" id="send-confirmation" checked style="margin-left: 8px; transform: scale(1.1);">
                                        <span style="font-weight: bold; font-size: 14px;">إرسال تأكيد فوري</span>
                                    </label>
                                    <p style="margin: 0; font-size: 12px; color: #666;">سيتم إرسال رسالة تأكيد فور حجز الموعد</p>
                                </div>
                                <div>
                                    <label style="display: flex; align-items: center; cursor: pointer; margin-bottom: 8px;">
                                        <input type="checkbox" id="send-reminder" checked style="margin-left: 8px; transform: scale(1.1);">
                                        <span style="font-weight: bold; font-size: 14px;">تذكير قبل الموعد</span>
                                    </label>
                                    <select id="reminder-time" style="width: 100%; padding: 6px; border: 1px solid #25d366; border-radius: 4px; font-size: 12px;">
                                        <option value="60">ساعة واحدة قبل الموعد</option>
                                        <option value="120">ساعتين قبل الموعد</option>
                                        <option value="1440">يوم واحد قبل الموعد</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div style="text-align: center; margin-top: 20px;">
                            <button type="submit" class="btn btn-success" style="font-size: 16px; padding: 12px 24px; margin: 0 8px;">
                                ✅ حجز الموعد
                            </button>
                            <button type="button" onclick="sendTestWhatsApp()" class="btn" style="background: #25d366; font-size: 16px; padding: 12px 24px; margin: 0 8px;">
                                📱 اختبار واتساب
                            </button>
                            <button type="button" onclick="goBack()" class="btn" style="background: #6c757d; font-size: 16px; padding: 12px 24px; margin: 0 8px;">
                                ← العودة للصفحة السابقة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- WhatsApp Reminders -->
            <div id="whatsapp-reminders" class="page">
                <h1>📱 تذكيرات الواتساب</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم تذكيرات الواتساب!
                </div>

                <!-- أزرار التحكم -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <button onclick="sendBulkReminders()" class="btn btn-success" style="padding: 20px; text-align: center; font-size: 18px;">
                        📤 إرسال تذكيرات جماعية
                    </button>
                    <button onclick="loadReminderHistory()" class="btn" style="background: #3498db; padding: 20px; text-align: center; font-size: 18px;">
                        📋 سجل التذكيرات
                    </button>
                    <button onclick="setupAutoReminders()" class="btn" style="background: #9b59b6; padding: 20px; text-align: center; font-size: 18px;">
                        ⚙️ إعدادات التذكير
                    </button>
                    <a href="#appointments" class="btn" style="background: #6c757d; padding: 20px; text-align: center; font-size: 18px;">
                        ← العودة للمواعيد
                    </a>
                </div>

                <!-- إحصائيات التذكيرات -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">12</div>
                        <div>تذكيرات اليوم</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">45</div>
                        <div>تذكيرات الأسبوع</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">3</div>
                        <div>تذكيرات فاشلة</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 10px;">98%</div>
                        <div>معدل النجاح</div>
                    </div>
                </div>

                <!-- قوالب الرسائل -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 25px;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #25d366; padding-bottom: 10px;">📝 قوالب رسائل الواتساب</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #25d366;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">🔔 تذكير بالموعد</h4>
                            <div style="background: white; padding: 10px; border-radius: 5px; font-size: 14px; line-height: 1.5; border: 1px solid #e9ecef;">
                                🏥 <strong>عيادة الدكتور أحمد محمد علي</strong><br><br>
                                مرحباً [اسم المريض]،<br><br>
                                🔔 تذكير بموعدك:<br>
                                📅 التاريخ: [التاريخ]<br>
                                🕐 الوقت: [الوقت]<br>
                                👨‍⚕️ الطبيب: د. أحمد محمد علي<br><br>
                                📍 العنوان: شارع الملك فهد، الرياض<br>
                                📞 للاستفسار: ************
                            </div>
                            <button onclick="useTemplate('reminder')" class="btn" style="background: #25d366; margin-top: 10px; width: 100%;">استخدام هذا القالب</button>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #3498db;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">✅ تأكيد الحجز</h4>
                            <div style="background: white; padding: 10px; border-radius: 5px; font-size: 14px; line-height: 1.5; border: 1px solid #e9ecef;">
                                🏥 <strong>عيادة الدكتور أحمد محمد علي</strong><br><br>
                                مرحباً [اسم المريض]،<br><br>
                                ✅ تم تأكيد حجز موعدك بنجاح:<br><br>
                                📅 التاريخ: [التاريخ]<br>
                                🕐 الوقت: [الوقت]<br>
                                🏥 نوع الزيارة: [نوع الزيارة]<br><br>
                                💡 احضر قبل 15 دقيقة من الموعد
                            </div>
                            <button onclick="useTemplate('confirmation')" class="btn" style="background: #3498db; margin-top: 10px; width: 100%;">استخدام هذا القالب</button>
                        </div>
                    </div>
                </div>

                <!-- سجل التذكيرات -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px;">📋 سجل التذكيرات المرسلة</h3>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">التاريخ</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">اسم المريض</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">رقم الهاتف</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">وقت الموعد</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #dee2e6;">نوع الرسالة</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #dee2e6;">الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">20 يناير 2024 - 08:30 ص</td>
                                    <td style="padding: 15px; text-align: right;">أحمد محمد علي</td>
                                    <td style="padding: 15px; text-align: right;">0501234567</td>
                                    <td style="padding: 15px; text-align: right;">09:00 ص</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #25d366; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">تذكير</span></td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">تم الإرسال</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">20 يناير 2024 - 09:30 ص</td>
                                    <td style="padding: 15px; text-align: right;">فاطمة أحمد</td>
                                    <td style="padding: 15px; text-align: right;">0507654321</td>
                                    <td style="padding: 15px; text-align: right;">10:30 ص</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #3498db; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">تأكيد</span></td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">تم الإرسال</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">20 يناير 2024 - 01:00 م</td>
                                    <td style="padding: 15px; text-align: right;">محمد حسن</td>
                                    <td style="padding: 15px; text-align: right;">0551234567</td>
                                    <td style="padding: 15px; text-align: right;">02:00 م</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">عاجل</span></td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">تم الإرسال</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Clinic Settings -->
            <div id="clinic-settings" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>⚙️ إعدادات العيادة</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى إعدادات العيادة!
                </div>

                <!-- إعدادات معلومات العيادة -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 25px;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px;">🏥 معلومات العيادة الأساسية</h3>

                    <form id="clinic-info-form">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">👨‍⚕️ اسم الطبيب:</label>
                                <input type="text" id="doctor-name" placeholder="د. أحمد محمد علي" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🏥 اسم العيادة:</label>
                                <input type="text" id="clinic-name" placeholder="عيادة الدكتور أحمد محمد علي" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🩺 التخصص:</label>
                                <input type="text" id="doctor-specialty" placeholder="أخصائي الطب الباطني والتغذية العلاجية" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📄 رقم الترخيص:</label>
                                <input type="text" id="license-number" placeholder="12345" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📍 عنوان العيادة:</label>
                                <input type="text" id="clinic-address" placeholder="شارع الملك فهد، الرياض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📞 رقم الهاتف:</label>
                                <input type="tel" id="clinic-phone" placeholder="************" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📧 البريد الإلكتروني:</label>
                            <input type="email" id="clinic-email" placeholder="<EMAIL>" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>

                        <button type="submit" class="btn btn-success" style="padding: 12px 24px; font-size: 16px;">
                            ✅ حفظ معلومات العيادة
                        </button>
                    </form>
                </div>

                <!-- إعدادات الشعار -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 25px;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">🎨 إعدادات الشعار</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                        <!-- رفع شعار -->
                        <div>
                            <h4 style="color: #2c3e50; margin-bottom: 15px;">📁 رفع شعار العيادة</h4>
                            <div style="border: 2px dashed #e9ecef; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 15px;">
                                <input type="file" id="logo-upload" accept="image/*" style="display: none;" onchange="handleLogoUpload(event)">
                                <div id="logo-preview" style="margin-bottom: 15px;">
                                    <div id="current-logo" style="width: 80px; height: 80px; background: #f8f9fa; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 32px;">🏥</div>
                                </div>
                                <button type="button" onclick="document.getElementById('logo-upload').click()" class="btn" style="background: #e74c3c;">
                                    📁 اختيار شعار
                                </button>
                                <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                    الأحجام المدعومة: JPG, PNG, GIF<br>
                                    الحد الأقصى: 2 ميجابايت
                                </p>
                            </div>
                        </div>

                        <!-- شعار نصي -->
                        <div>
                            <h4 style="color: #2c3e50; margin-bottom: 15px;">✏️ شعار نصي (إيموجي)</h4>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">اختر إيموجي للشعار:</label>
                                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <button type="button" onclick="selectEmoji('🏥')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">🏥</button>
                                    <button type="button" onclick="selectEmoji('⚕️')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">⚕️</button>
                                    <button type="button" onclick="selectEmoji('🩺')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">🩺</button>
                                    <button type="button" onclick="selectEmoji('💊')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">💊</button>
                                    <button type="button" onclick="selectEmoji('🏨')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">🏨</button>
                                    <button type="button" onclick="selectEmoji('🔬')" class="emoji-btn" style="padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; background: white; font-size: 24px; cursor: pointer;">🔬</button>
                                </div>
                                <input type="text" id="custom-emoji" placeholder="أو اكتب إيموجي مخصص" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 24px; text-align: center;" maxlength="2">
                                <div style="margin-top: 15px; text-align: center;">
                                    <div style="font-size: 12px; color: #666; margin-bottom: 10px;">معاينة الشعار:</div>
                                    <div id="emoji-preview" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; margin: 0 auto; border: 2px solid #e9ecef;">🏥</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="button" onclick="saveLogoSettings()" class="btn btn-success" style="padding: 12px 24px; font-size: 16px;">
                            ✅ حفظ إعدادات الشعار
                        </button>
                        <button type="button" onclick="resetLogo()" class="btn" style="background: #6c757d; padding: 12px 24px; font-size: 16px; margin-right: 10px;">
                            🔄 إعادة تعيين
                        </button>
                    </div>
                </div>

                <!-- معاينة التغييرات -->
                <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #27ae60; padding-bottom: 10px;">👁️ معاينة التغييرات</h3>

                    <!-- معاينة الهيدر الرئيسي -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 الهيدر الرئيسي:</h4>
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <div id="preview-logo" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-left: 15px;">🏥</div>
                                    <div>
                                        <h1 id="preview-clinic-name" style="margin: 0 0 5px 0; font-size: 24px;">عيادة الدكتور أحمد محمد علي</h1>
                                        <p id="preview-specialty" style="margin: 0; opacity: 0.9; font-size: 14px;">أخصائي الطب الباطني والتغذية العلاجية</p>
                                        <p id="preview-address" style="margin: 0; opacity: 0.8; font-size: 12px;">📍 شارع الملك فهد، الرياض | 📞 ************</p>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: bold; margin-bottom: 5px;" id="preview-doctor-name">👨‍⚕️ د. أحمد محمد علي</div>
                                    <div style="font-size: 12px; opacity: 0.8;" id="preview-license">رقم الترخيص: 12345</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معاينة الـ Sidebar -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">📱 الشريط الجانبي (Sidebar):</h4>
                        <div style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px; border-radius: 15px; max-width: 300px;">
                            <div style="text-align: center; padding: 15px; border-bottom: 2px solid rgba(255,255,255,0.1); margin-bottom: 15px;">
                                <div id="preview-sidebar-logo" style="width: 50px; height: 50px; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px auto; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                                    <span style="font-size: 20px; color: white;">🏥</span>
                                </div>
                                <h3 id="preview-sidebar-clinic-name" style="margin: 0 0 5px 0; font-size: 16px; color: white;">عيادة الدكتور أحمد محمد علي</h3>
                                <p id="preview-sidebar-doctor-name" style="margin: 0; font-size: 11px; color: rgba(255,255,255,0.8);">👨‍⚕️ د. أحمد محمد علي</p>
                            </div>
                            <div style="font-size: 12px; color: rgba(255,255,255,0.6); text-align: center;">
                                📊 لوحة التحكم<br>
                                📅 إدارة المواعيد<br>
                                🍽️ الخطط الغذائية<br>
                                📝 الوصفات الطبية<br>
                                ⚙️ إعدادات العيادة
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="button" onclick="applyChanges()" class="btn btn-success" style="padding: 15px 30px; font-size: 18px;">
                            🚀 تطبيق التغييرات على النظام
                        </button>
                    </div>
                </div>
            </div>

            <!-- Nutrition -->
            <div id="nutrition" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>🍽️ الخطط الغذائية</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>
                
                <div class="cards">
                    <div class="card" style="background: #e74c3c;">
                        <div class="card-number">12</div>
                        <div>خطط إنقاص الوزن</div>
                    </div>
                    <div class="card" style="background: #27ae60;">
                        <div class="card-number">8</div>
                        <div>خطط زيادة الوزن</div>
                    </div>
                    <div class="card" style="background: #f39c12;">
                        <div class="card-number">5</div>
                        <div>خطط الحفاظ على الوزن</div>
                    </div>
                </div>
                
                <a href="#create-nutrition-plan" class="btn btn-success" style="font-size: 20px; padding: 20px 40px; background: #27ae60; border: 3px solid #229954;">
                    ➕ إنشاء خطة جديدة (اختبار)
                </a>
                <a href="#saved-plans" class="btn" style="background: #28a745; font-size: 18px; padding: 15px 30px;">
                    📋 عرض الخطط المحفوظة
                </a>
                <a href="#templates" class="btn">
                    📚 عرض القوالب الجاهزة
                </a>
                

                
                <!-- رابط للقوالب الجاهزة -->
                <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; border: 2px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📚 القوالب الغذائية الجاهزة</h4>
                    <p style="color: #666; margin-bottom: 15px;">اختر من بين 4 قوالب غذائية جاهزة ومعتمدة طبياً</p>
                    <a href="#templates" class="btn" style="background: #3498db; font-size: 18px; padding: 15px 30px;">
                        📚 عرض جميع القوالب الجاهزة
                    </a>
                </div>
            </div>

            <!-- Add Patient Page -->
            <div id="add-patient" class="page">
                <h1>➕ إضافة مريض جديد</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى صفحة إضافة مريض جديد!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">📝 بيانات المريض الجديد</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">👤 الاسم الكامل:</label>
                            <input type="text" placeholder="أدخل الاسم الكامل" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🎂 العمر:</label>
                            <input type="number" placeholder="أدخل العمر" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">⚧ الجنس:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option>اختر الجنس</option>
                                <option>ذكر</option>
                                <option>أنثى</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📞 رقم الهاتف:</label>
                            <input type="tel" placeholder="أدخل رقم الهاتف" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🏠 العنوان:</label>
                        <input type="text" placeholder="أدخل العنوان الكامل" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                    </div>

                    <h4 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #3498db; padding-bottom: 10px;">📊 البيانات الصحية</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">⚖️ الوزن (كغ):</label>
                            <input type="number" step="0.1" placeholder="الوزن" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📏 الطول (سم):</label>
                            <input type="number" placeholder="الطول" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📈 مؤشر كتلة الجسم:</label>
                            <input type="text" placeholder="يحسب تلقائياً" readonly style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; background: #f8f9fa;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🎯 الهدف من العلاج:</label>
                        <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            <option>اختر الهدف</option>
                            <option>إنقاص الوزن</option>
                            <option>زيادة الوزن</option>
                            <option>الحفاظ على الوزن</option>
                            <option>علاج السكري</option>
                            <option>علاج ضغط الدم</option>
                            <option>تحسين التغذية العامة</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🏥 الحالات المرضية:</label>
                        <textarea placeholder="أدخل أي حالات مرضية أو أدوية يتناولها المريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; min-height: 100px; resize: vertical;"></textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📝 ملاحظات إضافية:</label>
                        <textarea placeholder="أي ملاحظات أو تفاصيل إضافية" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; min-height: 80px; resize: vertical;"></textarea>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#patients" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            ✅ حفظ بيانات المريض
                        </a>
                        <a href="#patients" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            ❌ إلغاء
                        </a>
                    </div>
                </div>
            </div>

            <!-- Patients List Page -->
            <div id="patients-list" class="page">
                <h1>📋 قائمة المرضى</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قائمة المرضى!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; margin: 0;">👥 جميع المرضى المسجلين</h3>
                        <a href="#add-patient" class="btn btn-success">
                            ➕ إضافة مريض جديد
                        </a>
                    </div>

                    <!-- جدول المرضى -->
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <thead>
                                <tr style="background: #3498db; color: white;">
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">الرقم</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">الاسم</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">العمر</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">الجنس</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">الهدف</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">تاريخ التسجيل</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #2980b9;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">001</td>
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">أحمد محمد علي</td>
                                    <td style="padding: 15px; text-align: right;">35</td>
                                    <td style="padding: 15px; text-align: right;">ذكر</td>
                                    <td style="padding: 15px; text-align: right;"><span style="background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">إنقاص الوزن</span></td>
                                    <td style="padding: 15px; text-align: right;">15/01/2024</td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#patients" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#patients" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">002</td>
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">فاطمة أحمد</td>
                                    <td style="padding: 15px; text-align: right;">28</td>
                                    <td style="padding: 15px; text-align: right;">أنثى</td>
                                    <td style="padding: 15px; text-align: right;"><span style="background: #27ae60; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">زيادة الوزن</span></td>
                                    <td style="padding: 15px; text-align: right;">18/01/2024</td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#patients" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#patients" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right;">003</td>
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">محمد حسن</td>
                                    <td style="padding: 15px; text-align: right;">45</td>
                                    <td style="padding: 15px; text-align: right;">ذكر</td>
                                    <td style="padding: 15px; text-align: right;"><span style="background: #9b59b6; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">علاج السكري</span></td>
                                    <td style="padding: 15px; text-align: right;">20/01/2024</td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#patients" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#patients" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#patients" class="btn">
                            ← العودة لإدارة المرضى
                        </a>
                    </div>
                </div>
            </div>

            <!-- Search Patient Page -->
            <div id="search-patient" class="page">
                <h1>🔍 البحث عن مريض</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى صفحة البحث عن المرضى!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🔍 البحث في قاعدة بيانات المرضى</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">👤 البحث بالاسم:</label>
                            <input type="text" placeholder="أدخل اسم المريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📞 البحث برقم الهاتف:</label>
                            <input type="tel" placeholder="أدخل رقم الهاتف" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🎯 البحث بالهدف:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option>جميع الأهداف</option>
                                <option>إنقاص الوزن</option>
                                <option>زيادة الوزن</option>
                                <option>الحفاظ على الوزن</option>
                                <option>علاج السكري</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📅 البحث بالتاريخ:</label>
                            <input type="date" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div style="text-align: center; margin: 25px 0;">
                        <a href="#search-patient" class="btn btn-success" style="font-size: 18px; padding: 15px 30px;">
                            🔍 بحث
                        </a>
                        <a href="#search-patient" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin-right: 10px;">
                            🔄 مسح الحقول
                        </a>
                    </div>

                    <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; border-right: 4px solid #3498db;">
                        <h4 style="color: #2c3e50; margin-top: 0;">📊 نتائج البحث:</h4>
                        <p style="color: #666; margin-bottom: 0;">سيتم عرض نتائج البحث هنا بعد إدخال معايير البحث والنقر على زر البحث.</p>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#patients" class="btn">
                            ← العودة لإدارة المرضى
                        </a>
                    </div>
                </div>
            </div>

            <!-- Create Nutrition Plan Page -->
            <div id="create-nutrition-plan" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>➕ إنشاء خطة غذائية جديدة</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى صفحة إنشاء خطة غذائية جديدة!
                </div>

                <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; font-weight: bold; border: 2px solid #c3e6cb; text-align: center; font-size: 18px;">
                    🎉 مبروك! وصلت لصفحة إنشاء الخطة الغذائية! 🎉<br>
                    الآن يمكنك اختيار أكثر من صنف من كل فئة طعام!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🍽️ تصميم خطة غذائية مخصصة</h3>

                    <!-- بيانات المريض -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #3498db;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">👤 بيانات المريض</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">اسم المريض:</label>
                                <input type="text" placeholder="أدخل اسم المريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">العمر:</label>
                                <input type="number" placeholder="العمر" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">الوزن الحالي (كغ):</label>
                                <input type="number" step="0.1" placeholder="الوزن" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">الطول (سم):</label>
                                <input type="number" placeholder="الطول" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">الوزن المستهدف (كغ):</label>
                                <input type="number" step="0.1" placeholder="الهدف" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>
                    </div>

                    <!-- هدف الخطة -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🎯 هدف الخطة الغذائية:</label>
                        <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            <option>اختر الهدف</option>
                            <option>إنقاص الوزن</option>
                            <option>زيادة الوزن</option>
                            <option>الحفاظ على الوزن</option>
                            <option>بناء العضلات</option>
                            <option>علاج السكري</option>
                            <option>علاج ضغط الدم</option>
                            <option>تحسين الهضم</option>
                        </select>
                    </div>

                    <!-- السعرات المطلوبة -->
                    <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #007bff;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">📊 حساب السعرات الحرارية</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">مستوى النشاط:</label>
                                <select style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <option>قليل الحركة</option>
                                    <option>نشاط خفيف</option>
                                    <option>نشاط متوسط</option>
                                    <option>نشاط عالي</option>
                                    <option>نشاط مكثف</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">السعرات المطلوبة يومياً:</label>
                                <input type="number" placeholder="مثال: 1800" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">مدة الخطة:</label>
                                <select style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <option>أسبوع واحد</option>
                                    <option>أسبوعين</option>
                                    <option>شهر واحد</option>
                                    <option>شهرين</option>
                                    <option>3 أشهر</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- الوجبات اليومية -->
                    <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #ffc107;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 20px;">🍽️ تخطيط الوجبات اليومية</h4>

                        <!-- الإفطار -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🌅 الإفطار (25% من السعرات)</h5>

                            <!-- اختيار الأطعمة من القوائم -->
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🍞 الكربوهيدرات:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">الخبز والحبوب:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="خبز أسمر (شريحة واحدة - 80 سعرة)" style="margin-left: 8px;">
                                            <span>خبز أسمر (شريحة واحدة - 80 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="خبز أبيض (شريحة واحدة - 90 سعرة)" style="margin-left: 8px;">
                                            <span>خبز أبيض (شريحة واحدة - 90 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="خبز الشعير (شريحة واحدة - 75 سعرة)" style="margin-left: 8px;">
                                            <span>خبز الشعير (شريحة واحدة - 75 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="توست أسمر (شريحة واحدة - 70 سعرة)" style="margin-left: 8px;">
                                            <span>توست أسمر (شريحة واحدة - 70 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="شوفان (نصف كوب - 150 سعرة)" style="margin-left: 8px;">
                                            <span>شوفان (نصف كوب - 150 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="كورن فليكس (كوب واحد - 100 سعرة)" style="margin-left: 8px;">
                                            <span>كورن فليكس (كوب واحد - 100 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">الأرز والمعكرونة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="أرز أبيض مسلوق (نصف كوب - 110 سعرة)" style="margin-left: 8px;">
                                            <span>أرز أبيض مسلوق (نصف كوب - 110 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="أرز أسمر مسلوق (نصف كوب - 105 سعرة)" style="margin-left: 8px;">
                                            <span>أرز أسمر مسلوق (نصف كوب - 105 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="برغل مسلوق (نصف كوب - 95 سعرة)" style="margin-left: 8px;">
                                            <span>برغل مسلوق (نصف كوب - 95 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-carbs" value="معكرونة مسلوقة (نصف كوب - 100 سعرة)" style="margin-left: 8px;">
                                            <span>معكرونة مسلوقة (نصف كوب - 100 سعرة)</span>
                                        </label>
                                    </div>
                                </div>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomCarb()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة كربوهيدرات مخصصة
                                    </button>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🥛 البروتين:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">منتجات الألبان:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="لبنة قليلة الدسم (ملعقة كبيرة - 35 سعرة)" style="margin-left: 8px;">
                                            <span>لبنة قليلة الدسم (ملعقة كبيرة - 35 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="جبنة بيضاء قليلة الدسم (30 جرام - 60 سعرة)" style="margin-left: 8px;">
                                            <span>جبنة بيضاء قليلة الدسم (30 جرام - 60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="حليب قليل الدسم (كوب واحد - 120 سعرة)" style="margin-left: 8px;">
                                            <span>حليب قليل الدسم (كوب واحد - 120 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="زبادي قليل الدسم (كوب واحد - 100 سعرة)" style="margin-left: 8px;">
                                            <span>زبادي قليل الدسم (كوب واحد - 100 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="جبنة شيدر (شريحة واحدة - 110 سعرة)" style="margin-left: 8px;">
                                            <span>جبنة شيدر (شريحة واحدة - 110 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">البيض:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="بيضة مسلوقة (حبة واحدة - 70 سعرة)" style="margin-left: 8px;">
                                            <span>بيضة مسلوقة (حبة واحدة - 70 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="بياض البيض (2 حبة - 35 سعرة)" style="margin-left: 8px;">
                                            <span>بياض البيض (2 حبة - 35 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="عجة بيضة واحدة (90 سعرة)" style="margin-left: 8px;">
                                            <span>عجة بيضة واحدة (90 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">اللحوم:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="لحم مقدد قليل الدهن (شريحتان - 80 سعرة)" style="margin-left: 8px;">
                                            <span>لحم مقدد قليل الدهن (شريحتان - 80 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="breakfast-protein" value="سجق دجاج (حبة واحدة - 60 سعرة)" style="margin-left: 8px;">
                                            <span>سجق دجاج (حبة واحدة - 60 سعرة)</span>
                                        </label>
                                    </div>
                                </div>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomProtein()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة بروتين مخصص
                                    </button>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🥗 الخضروات:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px;">
                                    <option>اختر الخضروات</option>
                                    <optgroup label="خضروات طازجة">
                                        <option>خيار (حبة متوسطة - 15 سعرة)</option>
                                        <option>طماطم (حبة متوسطة - 20 سعرة)</option>
                                        <option>خس (كوب واحد - 10 سعرة)</option>
                                        <option>جزر (حبة متوسطة - 25 سعرة)</option>
                                        <option>فلفل أخضر (حبة متوسطة - 20 سعرة)</option>
                                        <option>بصل أخضر (عود واحد - 5 سعرات)</option>
                                    </optgroup>
                                    <optgroup label="خضروات مطبوخة">
                                        <option>سبانخ مطبوخة (نصف كوب - 20 سعرة)</option>
                                        <option>بروكلي مسلوق (نصف كوب - 25 سعرة)</option>
                                        <option>كوسا مطبوخة (نصف كوب - 15 سعرة)</option>
                                    </optgroup>
                                </select>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomVegetable()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة خضروات مخصصة
                                    </button>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🍎 الفواكه:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px;">
                                    <option>اختر الفواكه</option>
                                    <optgroup label="فواكه طازجة">
                                        <option>تفاح (حبة متوسطة - 80 سعرة)</option>
                                        <option>موز (حبة متوسطة - 105 سعرة)</option>
                                        <option>برتقال (حبة متوسطة - 60 سعرة)</option>
                                        <option>عنب (كوب واحد - 60 سعرة)</option>
                                        <option>فراولة (كوب واحد - 50 سعرة)</option>
                                        <option>كيوي (حبة متوسطة - 40 سعرة)</option>
                                    </optgroup>
                                    <optgroup label="فواكه مجففة">
                                        <option>تمر (3 حبات - 60 سعرة)</option>
                                        <option>زبيب (ملعقة كبيرة - 25 سعرة)</option>
                                        <option>مشمش مجفف (5 حبات - 40 سعرة)</option>
                                    </optgroup>
                                </select>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomFruit()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة فواكه مخصصة
                                    </button>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">☕ المشروبات:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 10px;">
                                    <option>اختر المشروب</option>
                                    <optgroup label="مشروبات ساخنة">
                                        <option>شاي أخضر (كوب واحد - 2 سعرة)</option>
                                        <option>شاي أحمر (كوب واحد - 2 سعرة)</option>
                                        <option>قهوة عربية (كوب واحد - 5 سعرات)</option>
                                        <option>قهوة تركية (كوب صغير - 10 سعرات)</option>
                                        <option>نسكافيه (كوب واحد - 15 سعرة)</option>
                                    </optgroup>
                                    <optgroup label="عصائر طبيعية">
                                        <option>عصير برتقال طبيعي (كوب واحد - 110 سعرة)</option>
                                        <option>عصير تفاح طبيعي (كوب واحد - 115 سعرة)</option>
                                        <option>عصير جزر طبيعي (كوب واحد - 95 سعرة)</option>
                                    </optgroup>
                                </select>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomDrink()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة مشروب مخصص
                                    </button>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">📝 ملخص الوجبة:</label>
                                    <textarea placeholder="سيتم ملء هذا الحقل تلقائياً بناءً على اختياراتك أعلاه، أو يمكنك كتابة وجبة مخصصة" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px; resize: vertical;"></textarea>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔢 السعرات المقدرة:</label>
                                    <input type="number" placeholder="مثال: 450" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <button type="button" onclick="testBreakfast()" style="width: 100%; margin-top: 5px; padding: 8px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 12px; cursor: pointer;">
                                        🧮 حساب السعرات تلقائياً
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- وجبة خفيفة صباحية -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">☕ وجبة خفيفة صباحية (10% من السعرات)</h5>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🍎 اختر الوجبات الخفيفة:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">فواكه طازجة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="تفاح متوسط + شاي أخضر (85 سعرة)" style="margin-left: 8px;">
                                            <span>تفاح متوسط + شاي أخضر (85 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="موز صغير + قهوة (90 سعرة)" style="margin-left: 8px;">
                                            <span>موز صغير + قهوة (90 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="برتقال متوسط + شاي (65 سعرة)" style="margin-left: 8px;">
                                            <span>برتقال متوسط + شاي (65 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="حفنة عنب + ماء (60 سعرة)" style="margin-left: 8px;">
                                            <span>حفنة عنب + ماء (60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="كيوي + شاي بالنعناع (45 سعرة)" style="margin-left: 8px;">
                                            <span>كيوي + شاي بالنعناع (45 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">مكسرات وبذور:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="10 حبات لوز (70 سعرة)" style="margin-left: 8px;">
                                            <span>10 حبات لوز (70 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="15 حبة فستق (80 سعرة)" style="margin-left: 8px;">
                                            <span>15 حبة فستق (80 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="ملعقة كبيرة بذور عباد الشمس (50 سعرة)" style="margin-left: 8px;">
                                            <span>ملعقة كبيرة بذور عباد الشمس (50 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="5 حبات جوز (90 سعرة)" style="margin-left: 8px;">
                                            <span>5 حبات جوز (90 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">منتجات ألبان:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="كوب زبادي قليل الدسم (100 سعرة)" style="margin-left: 8px;">
                                            <span>كوب زبادي قليل الدسم (100 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="كوب حليب قليل الدسم (120 سعرة)" style="margin-left: 8px;">
                                            <span>كوب حليب قليل الدسم (120 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="قطعة جبن قليل الدسم (60 سعرة)" style="margin-left: 8px;">
                                            <span>قطعة جبن قليل الدسم (60 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">أخرى:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="3 حبات تمر (60 سعرة)" style="margin-left: 8px;">
                                            <span>3 حبات تمر (60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="ملعقة عسل + شاي (70 سعرة)" style="margin-left: 8px;">
                                            <span>ملعقة عسل + شاي (70 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="morning-snack" value="قطعة شوكولاتة داكنة صغيرة (50 سعرة)" style="margin-left: 8px;">
                                            <span>قطعة شوكولاتة داكنة صغيرة (50 سعرة)</span>
                                        </label>
                                    </div>
                                </div>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomMorningSnack()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة وجبة خفيفة مخصصة
                                    </button>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">📝 ملخص الوجبة الخفيفة:</label>
                                    <textarea placeholder="سيتم ملء هذا الحقل تلقائياً بناءً على اختيارك أعلاه" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px; resize: vertical;"></textarea>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔢 السعرات المقدرة:</label>
                                    <input type="number" placeholder="مثال: 180" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <button type="button" onclick="calculateMorningSnack()" style="width: 100%; margin-top: 5px; padding: 8px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 12px; cursor: pointer;">
                                        🧮 حساب السعرات تلقائياً
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- الغداء -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🍽️ الغداء (35% من السعرات)</h5>

                            <!-- اختيار أطعمة الغداء -->
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🍖 البروتين الرئيسي:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">لحوم حمراء:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="لحم بقر مشوي (100 جرام - 250 سعرة)" style="margin-left: 8px;">
                                            <span>لحم بقر مشوي (100 جرام - 250 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="لحم غنم مشوي (100 جرام - 280 سعرة)" style="margin-left: 8px;">
                                            <span>لحم غنم مشوي (100 جرام - 280 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="كباب مشوي (100 جرام - 260 سعرة)" style="margin-left: 8px;">
                                            <span>كباب مشوي (100 جرام - 260 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="ستيك لحم (100 جرام - 270 سعرة)" style="margin-left: 8px;">
                                            <span>ستيك لحم (100 جرام - 270 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">دجاج:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="دجاج مشوي منزوع الجلد (100 جرام - 165 سعرة)" style="margin-left: 8px;">
                                            <span>دجاج مشوي منزوع الجلد (100 جرام - 165 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="دجاج مسلوق (100 جرام - 140 سعرة)" style="margin-left: 8px;">
                                            <span>دجاج مسلوق (100 جرام - 140 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="صدر دجاج مشوي (100 جرام - 150 سعرة)" style="margin-left: 8px;">
                                            <span>صدر دجاج مشوي (100 جرام - 150 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="فخذ دجاج مشوي (100 جرام - 180 سعرة)" style="margin-left: 8px;">
                                            <span>فخذ دجاج مشوي (100 جرام - 180 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">أسماك:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="سمك سلمون مشوي (100 جرام - 200 سعرة)" style="margin-left: 8px;">
                                            <span>سمك سلمون مشوي (100 جرام - 200 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="سمك تونة مشوي (100 جرام - 130 سعرة)" style="margin-left: 8px;">
                                            <span>سمك تونة مشوي (100 جرام - 130 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="سمك هامور مشوي (100 جرام - 120 سعرة)" style="margin-left: 8px;">
                                            <span>سمك هامور مشوي (100 جرام - 120 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="سمك مقلي (100 جرام - 200 سعرة)" style="margin-left: 8px;">
                                            <span>سمك مقلي (100 جرام - 200 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">بقوليات:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="فاصولياء حمراء مطبوخة (كوب واحد - 220 سعرة)" style="margin-left: 8px;">
                                            <span>فاصولياء حمراء مطبوخة (كوب واحد - 220 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="عدس مطبوخ (كوب واحد - 230 سعرة)" style="margin-left: 8px;">
                                            <span>عدس مطبوخ (كوب واحد - 230 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="حمص مطبوخ (كوب واحد - 210 سعرة)" style="margin-left: 8px;">
                                            <span>حمص مطبوخ (كوب واحد - 210 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-protein" value="فول مطبوخ (كوب واحد - 180 سعرة)" style="margin-left: 8px;">
                                            <span>فول مطبوخ (كوب واحد - 180 سعرة)</span>
                                        </label>
                                    </div>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🍚 الكربوهيدرات:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">أرز:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="أرز أبيض مسلوق (كوب واحد - 220 سعرة)" style="margin-left: 8px;">
                                            <span>أرز أبيض مسلوق (كوب واحد - 220 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="أرز أسمر مسلوق (كوب واحد - 210 سعرة)" style="margin-left: 8px;">
                                            <span>أرز أسمر مسلوق (كوب واحد - 210 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="أرز بسمتي (كوب واحد - 200 سعرة)" style="margin-left: 8px;">
                                            <span>أرز بسمتي (كوب واحد - 200 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="أرز بالزعفران (كوب واحد - 240 سعرة)" style="margin-left: 8px;">
                                            <span>أرز بالزعفران (كوب واحد - 240 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">معكرونة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="معكرونة بالصلصة (كوب واحد - 180 سعرة)" style="margin-left: 8px;">
                                            <span>معكرونة بالصلصة (كوب واحد - 180 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="معكرونة بالزبدة (كوب واحد - 220 سعرة)" style="margin-left: 8px;">
                                            <span>معكرونة بالزبدة (كوب واحد - 220 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="معكرونة بالجبن (كوب واحد - 250 سعرة)" style="margin-left: 8px;">
                                            <span>معكرونة بالجبن (كوب واحد - 250 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">خبز:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="خبز عربي (رغيف واحد - 160 سعرة)" style="margin-left: 8px;">
                                            <span>خبز عربي (رغيف واحد - 160 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="خبز أسمر (3 شرائح - 240 سعرة)" style="margin-left: 8px;">
                                            <span>خبز أسمر (3 شرائح - 240 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="خبز التنور (قطعة واحدة - 120 سعرة)" style="margin-left: 8px;">
                                            <span>خبز التنور (قطعة واحدة - 120 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">أخرى:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="برغل مطبوخ (كوب واحد - 190 سعرة)" style="margin-left: 8px;">
                                            <span>برغل مطبوخ (كوب واحد - 190 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="بطاطس مسلوقة (حبة كبيرة - 160 سعرة)" style="margin-left: 8px;">
                                            <span>بطاطس مسلوقة (حبة كبيرة - 160 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="lunch-carbs" value="بطاطس مشوية (حبة كبيرة - 140 سعرة)" style="margin-left: 8px;">
                                            <span>بطاطس مشوية (حبة كبيرة - 140 سعرة)</span>
                                        </label>
                                    </div>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🥗 السلطة والخضروات:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px;">
                                    <option>اختر السلطة أو الخضروات</option>
                                    <optgroup label="سلطات">
                                        <option>سلطة خضراء مشكلة (كوب كبير - 20 سعرة)</option>
                                        <option>سلطة يونانية (كوب واحد - 80 سعرة)</option>
                                        <option>تبولة (نصف كوب - 60 سعرة)</option>
                                        <option>فتوش (كوب واحد - 70 سعرة)</option>
                                        <option>سلطة جزر (نصف كوب - 25 سعرة)</option>
                                    </optgroup>
                                    <optgroup label="خضروات مطبوخة">
                                        <option>خضار مشكلة مطبوخة (كوب واحد - 50 سعرة)</option>
                                        <option>بامية مطبوخة (كوب واحد - 60 سعرة)</option>
                                        <option>ملوخية (كوب واحد - 40 سعرة)</option>
                                        <option>فاصولياء خضراء (كوب واحد - 35 سعرة)</option>
                                        <option>كوسا محشية (حبة واحدة - 80 سعرة)</option>
                                    </optgroup>
                                </select>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🍲 الشوربة:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 10px;">
                                    <option>اختر نوع الشوربة (اختياري)</option>
                                    <optgroup label="شوربات خضار">
                                        <option>شوربة خضار (كوب واحد - 30 سعرة)</option>
                                        <option>شوربة طماطم (كوب واحد - 40 سعرة)</option>
                                        <option>شوربة عدس (كوب واحد - 120 سعرة)</option>
                                        <option>شوربة فطر (كوب واحد - 35 سعرة)</option>
                                    </optgroup>
                                    <optgroup label="شوربات لحوم">
                                        <option>شوربة دجاج (كوب واحد - 60 سعرة)</option>
                                        <option>شوربة لحم (كوب واحد - 80 سعرة)</option>
                                        <option>شوربة سمك (كوب واحد - 50 سعرة)</option>
                                    </optgroup>
                                </select>
                            </div>

                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">📝 ملخص وجبة الغداء:</label>
                                    <textarea placeholder="سيتم ملء هذا الحقل تلقائياً بناءً على اختياراتك أعلاه" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px; resize: vertical;"></textarea>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔢 السعرات المقدرة:</label>
                                    <input type="number" placeholder="مثال: 630" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <button type="button" onclick="calculateLunch()" style="width: 100%; margin-top: 5px; padding: 8px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 12px; cursor: pointer;">
                                        🧮 حساب السعرات تلقائياً
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- وجبة خفيفة مسائية -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🍎 وجبة خفيفة مسائية (10% من السعرات)</h5>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🥤 اختر الوجبات الخفيفة المسائية:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">منتجات ألبان:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="كوب زبادي قليل الدسم + ملعقة عسل (130 سعرة)" style="margin-left: 8px;">
                                            <span>كوب زبادي قليل الدسم + ملعقة عسل (130 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="كوب حليب دافئ + قرفة (125 سعرة)" style="margin-left: 8px;">
                                            <span>كوب حليب دافئ + قرفة (125 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="قطعة جبن قليل الدسم + خيار (80 سعرة)" style="margin-left: 8px;">
                                            <span>قطعة جبن قليل الدسم + خيار (80 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="كوب لبن رائب (90 سعرة)" style="margin-left: 8px;">
                                            <span>كوب لبن رائب (90 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">مكسرات خفيفة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="حفنة صغيرة لوز (8 حبات - 60 سعرة)" style="margin-left: 8px;">
                                            <span>حفنة صغيرة لوز (8 حبات - 60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="ملعقة كبيرة بذور اليقطين (50 سعرة)" style="margin-left: 8px;">
                                            <span>ملعقة كبيرة بذور اليقطين (50 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="5 حبات جوز + شاي (95 سعرة)" style="margin-left: 8px;">
                                            <span>5 حبات جوز + شاي (95 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">فواكه خفيفة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="تفاح صغير مقطع + قرفة (70 سعرة)" style="margin-left: 8px;">
                                            <span>تفاح صغير مقطع + قرفة (70 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="كوب فراولة طازجة (50 سعرة)" style="margin-left: 8px;">
                                            <span>كوب فراولة طازجة (50 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="حبة كيوي + شاي بالأعشاب (45 سعرة)" style="margin-left: 8px;">
                                            <span>حبة كيوي + شاي بالأعشاب (45 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">خيارات صحية أخرى:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="شاي أخضر + 2 حبة تمر (45 سعرة)" style="margin-left: 8px;">
                                            <span>شاي أخضر + 2 حبة تمر (45 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="كوب شاي بالأعشاب + بسكويت صحي (60 سعرة)" style="margin-left: 8px;">
                                            <span>كوب شاي بالأعشاب + بسكويت صحي (60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="evening-snack" value="عصير خضار طبيعي (40 سعرة)" style="margin-left: 8px;">
                                            <span>عصير خضار طبيعي (40 سعرة)</span>
                                        </label>
                                    </div>
                                </div>
                                <div style="margin-top: 5px;">
                                    <button type="button" onclick="addCustomEveningSnack()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;">
                                        ➕ إضافة وجبة خفيفة مخصصة
                                    </button>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">📝 ملخص الوجبة الخفيفة:</label>
                                    <textarea placeholder="سيتم ملء هذا الحقل تلقائياً بناءً على اختيارك أعلاه" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px; resize: vertical;"></textarea>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔢 السعرات المقدرة:</label>
                                    <input type="number" placeholder="مثال: 180" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <button type="button" onclick="calculateEveningSnack()" style="width: 100%; margin-top: 5px; padding: 8px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 12px; cursor: pointer;">
                                        🧮 حساب السعرات تلقائياً
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- العشاء -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🌙 العشاء (20% من السعرات)</h5>

                            <!-- اختيار أطعمة العشاء -->
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🐟 البروتين الخفيف:</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; max-height: 200px; overflow-y: auto;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">أسماك:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="سمك مشوي (100 جرام - 120 سعرة)" style="margin-left: 8px;">
                                            <span>سمك مشوي (100 جرام - 120 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="سمك سلمون مشوي (80 جرام - 160 سعرة)" style="margin-left: 8px;">
                                            <span>سمك سلمون مشوي (80 جرام - 160 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="تونة مشوية (100 جرام - 130 سعرة)" style="margin-left: 8px;">
                                            <span>تونة مشوية (100 جرام - 130 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="سمك فيليه مسلوق (100 جرام - 110 سعرة)" style="margin-left: 8px;">
                                            <span>سمك فيليه مسلوق (100 جرام - 110 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">دجاج خفيف:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="صدر دجاج مشوي (80 جرام - 120 سعرة)" style="margin-left: 8px;">
                                            <span>صدر دجاج مشوي (80 جرام - 120 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="دجاج مسلوق (80 جرام - 110 سعرة)" style="margin-left: 8px;">
                                            <span>دجاج مسلوق (80 جرام - 110 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="شرائح دجاج مشوية (80 جرام - 115 سعرة)" style="margin-left: 8px;">
                                            <span>شرائح دجاج مشوية (80 جرام - 115 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">بيض:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="عجة بيضتين بالخضار (140 سعرة)" style="margin-left: 8px;">
                                            <span>عجة بيضتين بالخضار (140 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="بيضتان مسلوقتان (140 سعرة)" style="margin-left: 8px;">
                                            <span>بيضتان مسلوقتان (140 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="بياض 3 بيضات (105 سعرة)" style="margin-left: 8px;">
                                            <span>بياض 3 بيضات (105 سعرة)</span>
                                        </label>
                                    </div>

                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">منتجات ألبان:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="جبن قريش (100 جرام - 90 سعرة)" style="margin-left: 8px;">
                                            <span>جبن قريش (100 جرام - 90 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-protein" value="جبن قليل الدسم (50 جرام - 80 سعرة)" style="margin-left: 8px;">
                                            <span>جبن قليل الدسم (50 جرام - 80 سعرة)</span>
                                        </label>
                                    </div>
                                </div>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🥬 الخضروات:</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 5px;">
                                    <option>اختر الخضروات للعشاء</option>
                                    <optgroup label="خضروات مطبوخة">
                                        <option>خضار مشكلة مسلوقة (كوب واحد - 50 سعرة)</option>
                                        <option>بروكلي مسلوق (كوب واحد - 55 سعرة)</option>
                                        <option>كوسا مطبوخة (كوب واحد - 30 سعرة)</option>
                                        <option>سبانخ مطبوخة (كوب واحد - 40 سعرة)</option>
                                        <option>قرنبيط مسلوق (كوب واحد - 25 سعرة)</option>
                                    </optgroup>
                                    <optgroup label="سلطات خفيفة">
                                        <option>سلطة خضراء كبيرة (كوب كبير - 25 سعرة)</option>
                                        <option>سلطة خيار ولبن (كوب واحد - 60 سعرة)</option>
                                        <option>سلطة جزر مبشور (نصف كوب - 25 سعرة)</option>
                                        <option>سلطة ملفوف (كوب واحد - 20 سعرة)</option>
                                    </optgroup>
                                </select>

                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50; margin-top: 10px;">🍞 كربوهيدرات خفيفة (اختياري):</label>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                                    <div style="margin-bottom: 10px;">
                                        <strong style="color: #495057;">خيارات خفيفة:</strong>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-carbs" value="شريحة خبز أسمر (80 سعرة)" style="margin-left: 8px;">
                                            <span>شريحة خبز أسمر (80 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-carbs" value="نصف كوب أرز أسمر (105 سعرة)" style="margin-left: 8px;">
                                            <span>نصف كوب أرز أسمر (105 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-carbs" value="قطعة خبز التنور الصغيرة (60 سعرة)" style="margin-left: 8px;">
                                            <span>قطعة خبز التنور الصغيرة (60 سعرة)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; cursor: pointer;">
                                            <input type="checkbox" name="dinner-carbs" value="2 ملعقة كبيرة شوفان (60 سعرة)" style="margin-left: 8px;">
                                            <span>2 ملعقة كبيرة شوفان (60 سعرة)</span>
                                        </label>
                                    </div>
                                    <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; border: 1px solid #ffeaa7;">
                                        <small style="color: #856404;">💡 <strong>نصيحة:</strong> يُفضل تجنب الكربوهيدرات في العشاء لتحسين عملية الهضم والنوم</small>
                                    </div>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">📝 ملخص وجبة العشاء:</label>
                                    <textarea placeholder="سيتم ملء هذا الحقل تلقائياً بناءً على اختياراتك أعلاه" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 60px; resize: vertical;"></textarea>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔢 السعرات المقدرة:</label>
                                    <input type="number" placeholder="مثال: 360" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <button type="button" onclick="calculateDinner()" style="width: 100%; margin-top: 5px; padding: 8px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 12px; cursor: pointer;">
                                        🧮 حساب السعرات تلقائياً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي السعرات الحرارية -->
                    <div id="total-calories-summary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 25px 0; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                        <h3 style="margin: 0 0 15px 0; font-size: 24px;">🔥 إجمالي السعرات الحرارية اليومية</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <div style="font-size: 14px; opacity: 0.9;">🌅 الإفطار</div>
                                <div id="breakfast-calories" style="font-size: 20px; font-weight: bold;">0 سعرة</div>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <div style="font-size: 14px; opacity: 0.9;">☕ وجبة خفيفة صباحية</div>
                                <div id="morning-snack-calories" style="font-size: 20px; font-weight: bold;">0 سعرة</div>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <div style="font-size: 14px; opacity: 0.9;">🍽️ الغداء</div>
                                <div id="lunch-calories" style="font-size: 20px; font-weight: bold;">0 سعرة</div>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <div style="font-size: 14px; opacity: 0.9;">🍎 وجبة خفيفة مسائية</div>
                                <div id="evening-snack-calories" style="font-size: 20px; font-weight: bold;">0 سعرة</div>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <div style="font-size: 14px; opacity: 0.9;">🌙 العشاء</div>
                                <div id="dinner-calories" style="font-size: 20px; font-weight: bold;">0 سعرة</div>
                            </div>
                        </div>
                        <div style="background: rgba(255,255,255,0.3); padding: 20px; border-radius: 12px; border: 2px solid rgba(255,255,255,0.4);">
                            <div style="font-size: 18px; margin-bottom: 10px;">📊 الإجمالي النهائي</div>
                            <div id="total-daily-calories" style="font-size: 36px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">0 سعرة حرارية</div>
                            <button type="button" onclick="calculateAllMealsCalories()" style="margin-top: 15px; padding: 12px 25px; background: #fff; color: #667eea; border: none; border-radius: 25px; font-weight: bold; cursor: pointer; font-size: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                                🧮 حساب إجمالي السعرات
                            </button>
                        </div>
                    </div>

                    <!-- النصائح والتعليمات -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">💡 نصائح وتعليمات خاصة:</label>
                        <textarea placeholder="أدخل النصائح والتعليمات الخاصة بهذه الخطة الغذائية" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; min-height: 100px; resize: vertical;"></textarea>
                    </div>

                    <!-- أزرار التحكم -->
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="button" onclick="previewCurrentPlan()" class="btn" style="background: #3498db; font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            👁️ معاينة الخطة
                        </button>
                        <button type="button" onclick="saveNutritionPlan()" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            ✅ حفظ الخطة الغذائية
                        </button>
                        <button type="button" onclick="saveAndPrintNutritionPlan()" class="btn" style="background: #17a2b8; font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            🖨️ حفظ وطباعة
                        </button>
                        <a href="#saved-plans" class="btn" style="background: #28a745; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            📋 عرض الخطط المحفوظة
                        </a>
                        <a href="#templates" class="btn" style="background: #ffc107; color: #212529; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            📚 استخدام قالب جاهز
                        </a>
                        <button type="button" onclick="goBack()" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            ← العودة للصفحة السابقة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Saved Plans Page -->
            <div id="saved-plans" class="page">
                <h1>📋 الخطط الغذائية المحفوظة</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية المحفوظة!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; margin: 0;">📚 جميع الخطط المحفوظة</h3>
                        <button type="button" onclick="clearAllSavedPlans()" class="btn" style="background: #dc3545; font-size: 14px; padding: 10px 20px;">
                            🗑️ مسح جميع الخطط
                        </button>
                    </div>

                    <div id="saved-plans-container" style="min-height: 200px;">
                        <!-- سيتم ملء هذا القسم بالخطط المحفوظة عبر JavaScript -->
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#create-nutrition-plan" class="btn btn-success">
                            ➕ إنشاء خطة جديدة
                        </a>
                        <a href="#nutrition" class="btn">
                            ← العودة للخطط الغذائية
                        </a>
                    </div>
                </div>
            </div>

            <!-- Plan Preview Page -->
            <div id="plan-preview" class="page">
                <h1>👁️ معاينة الخطة الغذائية</h1>
                <div class="success-msg">
                    ✅ معاينة الخطة قبل الطباعة
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 20px;">
                    <div id="preview-content" style="min-height: 400px;">
                        <!-- سيتم ملء هذا القسم بمحتوى الخطة -->
                    </div>

                    <!-- أزرار التحكم -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" onclick="printCurrentPreview()" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            🖨️ طباعة الخطة
                        </button>
                        <button type="button" onclick="editCurrentPlan()" class="btn" style="background: #f39c12; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            ✏️ تعديل الخطة
                        </button>
                        <button type="button" onclick="goBackFromPreview()" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            ← العودة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Templates Page -->
            <div id="templates" class="page">
                <h1>📚 القوالب الغذائية الجاهزة</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم القوالب الغذائية الجاهزة!
                </div>

                <div class="cards">
                    <a href="#weight-loss-template" class="template-card" style="background: #e74c3c; text-decoration: none; color: white;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">⚖️</div>
                        <h4>قالب إنقاص الوزن</h4>
                        <p style="font-size: 14px; margin-top: 10px;">1200-1500 سعرة حرارية</p>
                    </a>
                    <a href="#weight-gain-template" class="template-card" style="background: #27ae60; text-decoration: none; color: white;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">📈</div>
                        <h4>قالب زيادة الوزن</h4>
                        <p style="font-size: 14px; margin-top: 10px;">2500-3000 سعرة حرارية</p>
                    </a>
                    <a href="#maintenance-template" class="template-card" style="background: #f39c12; text-decoration: none; color: white;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">⚖️</div>
                        <h4>قالب الحفاظ على الوزن</h4>
                        <p style="font-size: 14px; margin-top: 10px;">1800-2200 سعرة حرارية</p>
                    </a>
                    <a href="#diabetic-template" class="template-card" style="background: #9b59b6; text-decoration: none; color: white;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🩺</div>
                        <h4>قالب مرضى السكري</h4>
                        <p style="font-size: 14px; margin-top: 10px;">منخفض الكربوهيدرات</p>
                    </a>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="#nutrition" class="btn">
                        ← العودة للخطط الغذائية
                    </a>
                </div>
            </div>

            <!-- Weight Loss Template -->
            <div id="weight-loss-template" class="page">
                <h2 style="color: #e74c3c; text-align: center;">⚖️ قالب إنقاص الوزن</h2>
                <p style="text-align: center; color: #666; font-size: 16px;">خطة غذائية متوازنة لإنقاص الوزن بشكل صحي (1200-1500 سعرة حرارية)</p>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الإفطار (300-400 سعرة)</h4>
                    <ul>
                        <li>2 شريحة خبز أسمر + ملعقة كبيرة لبنة قليلة الدسم</li>
                        <li>حبة خيار متوسطة + حبة طماطم</li>
                        <li>كوب شاي أخضر بدون سكر</li>
                        <li>حفنة صغيرة من اللوز (10 حبات)</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">وجبة خفيفة صباحية (100-150 سعرة)</h4>
                    <ul>
                        <li>حبة تفاح متوسطة</li>
                        <li>أو كوب زبادي قليل الدسم</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الغداء (400-500 سعرة)</h4>
                    <ul>
                        <li>100 جرام دجاج مشوي منزوع الجلد</li>
                        <li>كوب أرز أسمر مسلوق</li>
                        <li>سلطة خضراء كبيرة بالليمون</li>
                        <li>كوب شوربة خضار</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">العشاء (300-400 سعرة)</h4>
                    <ul>
                        <li>100 جرام سمك مشوي</li>
                        <li>كوب خضار مسلوقة</li>
                        <li>شريحة خبز أسمر</li>
                        <li>سلطة خضراء صغيرة</li>
                    </ul>
                </div>

                <div class="tips-section">
                    <h4 style="color: #007bff; margin-top: 0;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>شرب 8-10 أكواب ماء يومياً</li>
                        <li>ممارسة المشي 30 دقيقة يومياً</li>
                        <li>تجنب السكريات والدهون المشبعة</li>
                        <li>تناول الطعام ببطء ومضغ جيد</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <a href="#templates" class="btn">
                        ← العودة للقوالب
                    </a>
                </div>
            </div>

            <!-- Weight Gain Template -->
            <div id="weight-gain-template" class="page">
                <h2 style="color: #27ae60; text-align: center;">📈 قالب زيادة الوزن</h2>
                <p style="text-align: center; color: #666; font-size: 16px;">خطة غذائية غنية بالسعرات لزيادة الوزن بشكل صحي (2500-3000 سعرة حرارية)</p>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الإفطار (600-700 سعرة)</h4>
                    <ul>
                        <li>3 شرائح خبز أسمر + 2 ملعقة كبيرة زبدة فول سوداني</li>
                        <li>كوب حليب كامل الدسم + ملعقة عسل</li>
                        <li>حبة موز + حفنة تمر</li>
                        <li>عجة بيضتين بالزيت</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">وجبة خفيفة صباحية (300-400 سعرة)</h4>
                    <ul>
                        <li>كوب عصير طبيعي + ملعقة عسل</li>
                        <li>حفنة مكسرات مشكلة</li>
                        <li>قطعة كيك منزلي</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الغداء (800-900 سعرة)</h4>
                    <ul>
                        <li>150 جرام لحم أحمر مشوي</li>
                        <li>كوب ونصف أرز أبيض</li>
                        <li>كوب فاصولياء بالزيت</li>
                        <li>سلطة بالزيت والليمون</li>
                        <li>كوب عصير طبيعي</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">العشاء (500-600 سعرة)</h4>
                    <ul>
                        <li>150 جرام دجاج مشوي</li>
                        <li>كوب مكرونة بالصلصة</li>
                        <li>سلطة خضراء بالزيت</li>
                        <li>كوب زبادي كامل الدسم</li>
                    </ul>
                </div>

                <div class="tips-section">
                    <h4 style="color: #007bff; margin-top: 0;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>تناول 5-6 وجبات صغيرة يومياً</li>
                        <li>شرب السوائل بين الوجبات وليس معها</li>
                        <li>إضافة الزيوت الصحية للطعام</li>
                        <li>ممارسة تمارين المقاومة</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <a href="#templates" class="btn">
                        ← العودة للقوالب
                    </a>
                </div>
            </div>

            <!-- Maintenance Template -->
            <div id="maintenance-template" class="page">
                <h2 style="color: #f39c12; text-align: center;">⚖️ قالب الحفاظ على الوزن</h2>
                <p style="text-align: center; color: #666; font-size: 16px;">خطة غذائية متوازنة للحفاظ على الوزن المثالي (1800-2200 سعرة حرارية)</p>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الإفطار (400-500 سعرة)</h4>
                    <ul>
                        <li>2 شريحة خبز أسمر + جبنة قليلة الدسم</li>
                        <li>كوب حليب قليل الدسم</li>
                        <li>حبة فاكهة موسمية</li>
                        <li>ملعقة صغيرة عسل</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الغداء (600-700 سعرة)</h4>
                    <ul>
                        <li>120 جرام بروتين (دجاج/سمك/لحم)</li>
                        <li>كوب أرز أو مكرونة</li>
                        <li>كوب خضار مطبوخة</li>
                        <li>سلطة متنوعة</li>
                        <li>ملعقة زيت زيتون</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">العشاء (500-600 سعرة)</h4>
                    <ul>
                        <li>100 جرام بروتين خفيف</li>
                        <li>كوب خضار مشكلة</li>
                        <li>شريحة خبز أسمر</li>
                        <li>سلطة خضراء</li>
                    </ul>
                </div>

                <div class="tips-section">
                    <h4 style="color: #007bff; margin-top: 0;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>الحفاظ على نشاط بدني منتظم</li>
                        <li>شرب الماء بكميات كافية</li>
                        <li>تناول وجبات منتظمة</li>
                        <li>مراقبة الوزن أسبوعياً</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <a href="#templates" class="btn">
                        ← العودة للقوالب
                    </a>
                </div>
            </div>

            <!-- Diabetic Template -->
            <div id="diabetic-template" class="page">
                <h2 style="color: #9b59b6; text-align: center;">🩺 قالب مرضى السكري</h2>
                <p style="text-align: center; color: #666; font-size: 16px;">خطة غذائية خاصة لمرضى السكري مع التحكم في الكربوهيدرات</p>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الإفطار (350-400 سعرة)</h4>
                    <ul>
                        <li>شريحة خبز أسمر + بيضة مسلوقة</li>
                        <li>كوب حليب خالي الدسم بدون سكر</li>
                        <li>حبة خيار + طماطم</li>
                        <li>ملعقة صغيرة زيت زيتون</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">الغداء (450-500 سعرة)</h4>
                    <ul>
                        <li>100 جرام دجاج مشوي</li>
                        <li>نصف كوب أرز أسمر</li>
                        <li>كوب خضار ورقية</li>
                        <li>سلطة خضراء بالليمون</li>
                        <li>كوب شوربة خضار</li>
                    </ul>
                </div>

                <div class="meal-section">
                    <h4 style="color: #2c3e50;">العشاء (350-400 سعرة)</h4>
                    <ul>
                        <li>100 جرام سمك مشوي</li>
                        <li>كوب خضار مسلوقة</li>
                        <li>سلطة خضراء كبيرة</li>
                        <li>ملعقة صغيرة زيت زيتون</li>
                    </ul>
                </div>

                <div class="tips-section">
                    <h4 style="color: #007bff; margin-top: 0;">💡 نصائح مهمة:</h4>
                    <ul>
                        <li>مراقبة مستوى السكر بانتظام</li>
                        <li>تجنب السكريات البسيطة</li>
                        <li>تناول الألياف بكثرة</li>
                        <li>ممارسة الرياضة بانتظام</li>
                        <li>شرب الماء بدلاً من العصائر</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <a href="#templates" class="btn">
                        ← العودة للقوالب
                    </a>
                </div>
            </div>

            <!-- Prescriptions -->
            <div id="prescriptions" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>💊 الوصفات الطبية</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>

                <div class="cards">
                    <div class="card">
                        <div class="card-number">8</div>
                        <div>وصفات اليوم</div>
                    </div>
                    <div class="card" style="background: #9b59b6;">
                        <div class="card-number">156</div>
                        <div>إجمالي الوصفات</div>
                    </div>
                    <div class="card" style="background: #1abc9c;">
                        <div class="card-number">142</div>
                        <div>وصفات مطبوعة</div>
                    </div>
                </div>

                <h3>📝 خدمات الوصفات الطبية:</h3>
                <ul style="line-height: 2;">
                    <li>كتابة وصفات طبية جديدة مع تفاصيل شاملة</li>
                    <li>إضافة أدوية متعددة مع الجرعات والتعليمات</li>
                    <li>طباعة وصفات بتصميم طبي احترافي ومعتمد</li>
                    <li>حفظ وأرشفة جميع الوصفات لسهولة الوصول</li>
                    <li>إعادة طباعة الوصفات السابقة عند الحاجة</li>
                </ul>

                <a href="#new-prescription" class="btn btn-success">
                    📝 كتابة وصفة جديدة
                </a>
                <a href="#sample-prescription" class="btn">
                    👁️ عرض وصفة تجريبية
                </a>
                <a href="#print-sample" class="btn" style="background: #17a2b8;">
                    🖨️ طباعة وصفة تجريبية
                </a>
                <a href="#prescriptions-archive" class="btn">
                    📚 أرشيف الوصفات
                </a>

                <!-- وصفة تجريبية -->
                <div id="sample-prescription" class="template-details">
                    <h2 style="color: #2c3e50; text-align: center;">🏥 وصفة طبية تجريبية</h2>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-right: 5px solid #3498db;">
                        <strong>👤 بيانات المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>📅 تاريخ الوصفة:</strong> 20/01/2024<br>
                        <strong>🆔 رقم الوصفة:</strong> RX-12345<br>
                        <strong>🔍 التشخيص:</strong> نقص فيتامين د وضعف عام
                    </div>

                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">💊 الأدوية المطلوبة:</h3>

                    <div class="meal-section">
                        <h4 style="color: #2c3e50;">1. فيتامين د3 (Vitamin D3)</h4>
                        <ul>
                            <li><strong>التركيز:</strong> 1000 وحدة دولية</li>
                            <li><strong>الجرعة:</strong> حبة واحدة يومياً مع الطعام</li>
                            <li><strong>المدة:</strong> 3 أشهر</li>
                        </ul>
                    </div>

                    <div class="meal-section">
                        <h4 style="color: #2c3e50;">2. كالسيوم (Calcium Carbonate)</h4>
                        <ul>
                            <li><strong>التركيز:</strong> 500 ملغ</li>
                            <li><strong>الجرعة:</strong> حبة واحدة مع الطعام مساءً</li>
                            <li><strong>المدة:</strong> شهر واحد</li>
                        </ul>
                    </div>

                    <div class="tips-section">
                        <h4 style="color: #856404; margin-top: 0;">⚠️ التعليمات العامة:</h4>
                        <ul>
                            <li>التعرض لأشعة الشمس صباحاً لمدة 15-20 دقيقة يومياً</li>
                            <li>شرب كمية كافية من الماء (8-10 أكواب يومياً)</li>
                            <li>تناول الأدوية مع الطعام لتجنب اضطراب المعدة</li>
                            <li>المتابعة بعد شهر واحد لتقييم التحسن</li>
                            <li>تجنب تناول الكالسيوم مع الحديد في نفس الوقت</li>
                        </ul>
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding: 20px; border: 2px dashed #ccc;">
                        <p><strong>توقيع الطبيب:</strong> _______________</p>
                        <p style="font-size: 14px; color: #666;">د. [اسم الطبيب]</p>
                        <p style="font-size: 12px; color: #999;">تاريخ الطباعة: 20/01/2024</p>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <a href="#prescriptions" class="btn">
                            ← العودة للوصفات الطبية
                        </a>
                    </div>
                </div>
            </div>

            <!-- New Prescription Page -->
            <div id="new-prescription" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>📝 كتابة وصفة طبية جديدة</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى صفحة كتابة وصفة طبية جديدة!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🏥 بيانات الوصفة الطبية</h3>

                    <!-- بيانات المريض -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #3498db;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">👤 بيانات المريض</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">اسم المريض:</label>
                                <input type="text" placeholder="أدخل اسم المريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">العمر:</label>
                                <input type="number" placeholder="العمر" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">رقم الهاتف:</label>
                                <input type="tel" placeholder="رقم الهاتف" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">تاريخ الوصفة:</label>
                                <input type="date" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                            </div>
                        </div>
                    </div>

                    <!-- التشخيص -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🔍 التشخيص:</label>
                        <textarea placeholder="أدخل التشخيص الطبي للمريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; min-height: 80px; resize: vertical;"></textarea>
                    </div>

                    <!-- الأدوية -->
                    <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #007bff;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 20px;">💊 الأدوية المطلوبة</h4>

                        <!-- دواء 1 -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">الدواء الأول:</h5>
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">اسم الدواء:</label>
                                    <input type="text" placeholder="مثال: فيتامين د3" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">التركيز:</label>
                                    <input type="text" placeholder="مثال: 1000 وحدة" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">الجرعة:</label>
                                    <input type="text" placeholder="مثال: حبة واحدة يومياً" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">المدة:</label>
                                    <input type="text" placeholder="مثال: 3 أشهر" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                            </div>
                        </div>

                        <!-- دواء 2 -->
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <h5 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">الدواء الثاني:</h5>
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">اسم الدواء:</label>
                                    <input type="text" placeholder="مثال: كالسيوم" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">التركيز:</label>
                                    <input type="text" placeholder="مثال: 500 ملغ" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">الجرعة:</label>
                                    <input type="text" placeholder="مثال: حبة واحدة مساءً" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">المدة:</label>
                                    <input type="text" placeholder="مثال: شهر واحد" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 15px;">
                            <a href="#new-prescription" class="btn" style="background: #28a745; color: white; padding: 10px 20px; font-size: 14px;">
                                ➕ إضافة دواء آخر
                            </a>
                        </div>
                    </div>

                    <!-- التعليمات -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">⚠️ التعليمات العامة:</label>
                        <textarea placeholder="أدخل التعليمات والنصائح للمريض" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; min-height: 100px; resize: vertical;"></textarea>
                    </div>

                    <!-- أزرار التحكم -->
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#prescriptions" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                            ✅ حفظ الوصفة
                        </a>
                        <button type="button" onclick="saveAndPrintPrescription()" class="btn" style="background: #17a2b8; font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            🖨️ حفظ وطباعة
                        </button>
                        <button type="button" onclick="testClinicSettings()" class="btn" style="background: #ffc107; color: #000; font-size: 16px; padding: 12px 20px; margin: 0 10px; border: none; cursor: pointer;">
                            🔧 اختبار الإعدادات
                        </button>
                        <button type="button" onclick="goBack()" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px; border: none; cursor: pointer;">
                            ← العودة للصفحة السابقة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Prescriptions Archive Page -->
            <div id="prescriptions-archive" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>📚 أرشيف الوصفات الطبية</h1>
                    <button onclick="goBack()" class="btn" style="background: #6c757d; padding: 10px 20px; font-size: 14px;">
                        ← العودة للصفحة السابقة
                    </button>
                </div>
                <div class="success-msg">
                    ✅ تم الانتقال إلى أرشيف الوصفات الطبية!
                </div>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; margin: 0;">📋 جميع الوصفات المحفوظة</h3>
                        <a href="#new-prescription" class="btn btn-success">
                            📝 كتابة وصفة جديدة
                        </a>
                    </div>

                    <!-- فلاتر البحث -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🔍 البحث والفلترة</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                            <div>
                                <input type="text" placeholder="البحث باسم المريض" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                            </div>
                            <div>
                                <input type="date" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                            </div>
                            <div>
                                <select style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                    <option>جميع الوصفات</option>
                                    <option>وصفات اليوم</option>
                                    <option>وصفات الأسبوع</option>
                                    <option>وصفات الشهر</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الوصفات -->
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <thead>
                                <tr style="background: #3498db; color: white;">
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">رقم الوصفة</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">اسم المريض</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">التاريخ</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">التشخيص</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #2980b9;">عدد الأدوية</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #2980b9;">الحالة</th>
                                    <th style="padding: 15px; text-align: center; border-bottom: 2px solid #2980b9;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">RX-001</td>
                                    <td style="padding: 15px; text-align: right;">أحمد محمد علي</td>
                                    <td style="padding: 15px; text-align: right;">20/01/2024</td>
                                    <td style="padding: 15px; text-align: right;">نقص فيتامين د</td>
                                    <td style="padding: 15px; text-align: right;">2</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">مطبوعة</span></td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#sample-prescription" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#new-prescription" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                        <a href="#prescriptions-archive" class="btn" style="background: #17a2b8; padding: 8px 12px; font-size: 12px; margin: 2px;">🖨️ طباعة</a>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">RX-002</td>
                                    <td style="padding: 15px; text-align: right;">فاطمة أحمد</td>
                                    <td style="padding: 15px; text-align: right;">18/01/2024</td>
                                    <td style="padding: 15px; text-align: right;">فقر الدم</td>
                                    <td style="padding: 15px; text-align: right;">3</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 15px; font-size: 12px;">محفوظة</span></td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#prescriptions-archive" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#new-prescription" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                        <a href="#prescriptions-archive" class="btn" style="background: #17a2b8; padding: 8px 12px; font-size: 12px; margin: 2px;">🖨️ طباعة</a>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e9ecef;">
                                    <td style="padding: 15px; text-align: right; font-weight: bold;">RX-003</td>
                                    <td style="padding: 15px; text-align: right;">محمد حسن</td>
                                    <td style="padding: 15px; text-align: right;">15/01/2024</td>
                                    <td style="padding: 15px; text-align: right;">ارتفاع ضغط الدم</td>
                                    <td style="padding: 15px; text-align: right;">2</td>
                                    <td style="padding: 15px; text-align: center;"><span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">مطبوعة</span></td>
                                    <td style="padding: 15px; text-align: center;">
                                        <a href="#prescriptions-archive" class="btn" style="background: #3498db; padding: 8px 12px; font-size: 12px; margin: 2px;">👁️ عرض</a>
                                        <a href="#new-prescription" class="btn" style="background: #f39c12; padding: 8px 12px; font-size: 12px; margin: 2px;">✏️ تعديل</a>
                                        <a href="#prescriptions-archive" class="btn" style="background: #17a2b8; padding: 8px 12px; font-size: 12px; margin: 2px;">🖨️ طباعة</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#prescriptions" class="btn">
                            ← العودة للوصفات الطبية
                        </a>
                    </div>
                </div>
            </div>

            <!-- Print Sample Prescription -->
            <div id="print-sample" class="page">
                <div style="background: white; padding: 40px; max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6; color: #333;">

                    <!-- رأس الوصفة -->
                    <div style="text-align: center; border-bottom: 3px solid #2c3e50; padding-bottom: 20px; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50; margin: 0; font-size: 28px;">🏥 عيادة التغذية العلاجية</h1>
                        <h2 style="color: #3498db; margin: 10px 0; font-size: 20px;">د. [اسم الطبيب] - أخصائي التغذية العلاجية</h2>
                        <p style="margin: 5px 0; color: #666;">📞 الهاتف: 07XX XXX XXXX | 📧 البريد: <EMAIL></p>
                        <p style="margin: 5px 0; color: #666;">🏠 العنوان: شارع الطب، بغداد، العراق</p>
                    </div>

                    <!-- بيانات المريض -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #3498db;">
                        <h3 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px; font-size: 18px;">👤 بيانات المريض</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div><strong>الاسم:</strong> أحمد محمد علي</div>
                            <div><strong>العمر:</strong> 35 سنة</div>
                            <div><strong>الجنس:</strong> ذكر</div>
                            <div><strong>رقم الهاتف:</strong> 07XX XXX XXXX</div>
                        </div>
                        <div style="margin-top: 15px;">
                            <div><strong>📅 تاريخ الوصفة:</strong> <span id="current-date"></span></div>
                            <div><strong>🆔 رقم الوصفة:</strong> RX-2024-001</div>
                        </div>
                    </div>

                    <!-- التشخيص -->
                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 18px; border-bottom: 2px solid #3498db; padding-bottom: 8px;">🔍 التشخيص الطبي</h3>
                        <p style="background: #fff3cd; padding: 15px; border-radius: 8px; border-right: 4px solid #ffc107; margin: 0;">
                            نقص فيتامين د مع ضعف عام في العظام وآلام في المفاصل. يحتاج المريض إلى تعويض نقص الفيتامين مع تناول الكالسيوم لتقوية العظام.
                        </p>
                    </div>

                    <!-- الأدوية المطلوبة -->
                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; margin-bottom: 20px; font-size: 18px; border-bottom: 2px solid #3498db; padding-bottom: 8px;">💊 الأدوية المطلوبة</h3>

                        <!-- دواء 1 -->
                        <div style="border: 2px solid #e9ecef; padding: 20px; margin-bottom: 15px; border-radius: 10px; background: #fdfdfe;">
                            <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px; font-size: 16px;">1️⃣ فيتامين د3 (Vitamin D3)</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 10px;">
                                <div><strong>التركيز:</strong> 1000 وحدة دولية</div>
                                <div><strong>الشكل الصيدلاني:</strong> كبسولات جيلاتينية</div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div><strong>الجرعة:</strong> كبسولة واحدة يومياً مع الطعام</div>
                                <div><strong>مدة العلاج:</strong> 3 أشهر</div>
                            </div>
                            <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin-top: 10px;">
                                <strong>ملاحظات:</strong> يُفضل تناولها مع وجبة تحتوي على دهون لتحسين الامتصاص
                            </div>
                        </div>

                        <!-- دواء 2 -->
                        <div style="border: 2px solid #e9ecef; padding: 20px; margin-bottom: 15px; border-radius: 10px; background: #fdfdfe;">
                            <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px; font-size: 16px;">2️⃣ كالسيوم كربونات (Calcium Carbonate)</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 10px;">
                                <div><strong>التركيز:</strong> 500 ملليغرام</div>
                                <div><strong>الشكل الصيدلاني:</strong> أقراص قابلة للمضغ</div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div><strong>الجرعة:</strong> قرص واحد مساءً مع الطعام</div>
                                <div><strong>مدة العلاج:</strong> شهر واحد</div>
                            </div>
                            <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin-top: 10px;">
                                <strong>ملاحظات:</strong> لا يُتناول مع الحديد في نفس الوقت، فاصل زمني 2 ساعة على الأقل
                            </div>
                        </div>
                    </div>

                    <!-- التعليمات العامة -->
                    <div style="background: #fff3cd; padding: 20px; border-radius: 10px; border-right: 4px solid #ffc107; margin-bottom: 25px;">
                        <h3 style="color: #856404; margin-top: 0; margin-bottom: 15px; font-size: 18px;">⚠️ التعليمات العامة والنصائح المهمة</h3>
                        <ul style="margin: 0; padding-right: 20px; line-height: 1.8;">
                            <li><strong>التعرض لأشعة الشمس:</strong> 15-20 دقيقة يومياً في الصباح الباكر أو بعد العصر</li>
                            <li><strong>شرب الماء:</strong> 8-10 أكواب من الماء يومياً لتحسين امتصاص الأدوية</li>
                            <li><strong>النظام الغذائي:</strong> تناول الأطعمة الغنية بالكالسيوم مثل الحليب والجبن</li>
                            <li><strong>النشاط البدني:</strong> ممارسة المشي لمدة 30 دقيقة يومياً لتقوية العظام</li>
                            <li><strong>المتابعة الطبية:</strong> مراجعة العيادة بعد شهر واحد لتقييم التحسن</li>
                            <li><strong>تجنب:</strong> التدخين والمشروبات الغازية التي تؤثر على امتصاص الكالسيوم</li>
                        </ul>
                    </div>

                    <!-- معلومات إضافية -->
                    <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; margin-bottom: 25px; border-right: 4px solid #17a2b8;">
                        <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 10px;">📋 معلومات مهمة للمريض</h4>
                        <p style="margin: 0; line-height: 1.6;">
                            • في حالة ظهور أي أعراض جانبية مثل الغثيان أو الإمساك، يرجى التواصل مع العيادة فوراً<br>
                            • لا تتوقف عن تناول الأدوية دون استشارة طبية<br>
                            • احتفظ بالأدوية في مكان بارد وجاف بعيداً عن متناول الأطفال
                        </p>
                    </div>

                    <!-- التوقيع والختم -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 40px; padding-top: 20px; border-top: 2px dashed #ccc;">
                        <div style="text-align: center;">
                            <div style="border: 2px dashed #666; padding: 20px; margin-bottom: 10px; min-height: 60px; display: flex; align-items: center; justify-content: center; color: #666;">
                                ختم العيادة
                            </div>
                            <p style="margin: 0; font-weight: bold;">ختم وتوقيع العيادة</p>
                        </div>
                        <div style="text-align: center;">
                            <div style="border-bottom: 2px solid #333; margin-bottom: 10px; padding-bottom: 40px;"></div>
                            <p style="margin: 0; font-weight: bold;">توقيع الطبيب</p>
                            <p style="margin: 5px 0; color: #666; font-size: 14px;">د. [اسم الطبيب]</p>
                            <p style="margin: 0; color: #666; font-size: 12px;">أخصائي التغذية العلاجية</p>
                        </div>
                    </div>

                    <!-- معلومات الطباعة -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px;">
                        <p style="margin: 0;">تاريخ الطباعة: <span id="print-date"></span> | وقت الطباعة: <span id="print-time"></span></p>
                        <p style="margin: 5px 0;">هذه الوصفة صالحة لمدة 30 يوماً من تاريخ الإصدار</p>
                    </div>

                    <!-- أزرار التحكم -->
                    <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <button onclick="window.print()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 18px; cursor: pointer; margin: 0 10px;">
                            🖨️ طباعة الوصفة
                        </button>
                        <a href="#prescriptions" style="background: #6c757d; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 18px; text-decoration: none; margin: 0 10px; display: inline-block;">
                            ← العودة للوصفات
                        </a>
                    </div>
                </div>

                <script>
                    // تحديث التاريخ والوقت (ميلادي)
                    document.addEventListener('DOMContentLoaded', function() {
                        console.log('🚀 تم تحميل الصفحة - بدء التهيئة');

                        updateDateTime();
                        // تحديث الوقت كل دقيقة
                        setInterval(updateDateTime, 60000);

                        // تأخير قصير للتأكد من تحميل العناصر
                        setTimeout(function() {
                            console.log('⏰ بدء عرض المواعيد بعد التأخير');
                            displayAppointments();
                            updateAppointmentStats();
                        }, 500);

                        // تحديث المواعيد عند تغيير الصفحة
                        window.addEventListener('hashchange', function() {
                            console.log('🔄 تغيير الصفحة إلى:', window.location.hash);
                            if (window.location.hash === '#appointments') {
                                setTimeout(function() {
                                    displayAppointments();
                                    updateAppointmentStats();
                                }, 200);
                            }
                        });

                        // معالج حقل الوظيفة المخصص
                        var occupationSelect = document.getElementById('patient-occupation');
                        var customOccupationField = document.getElementById('custom-occupation-field');

                        if (occupationSelect && customOccupationField) {
                            occupationSelect.addEventListener('change', function() {
                                if (this.value === 'أخرى') {
                                    customOccupationField.style.display = 'block';
                                    document.getElementById('custom-occupation').required = true;
                                } else {
                                    customOccupationField.style.display = 'none';
                                    document.getElementById('custom-occupation').required = false;
                                    document.getElementById('custom-occupation').value = '';
                                }
                            });
                        }

                        // معالج نموذج حجز الموعد
                        var appointmentForm = document.getElementById('appointment-form');
                        if (appointmentForm) {
                            appointmentForm.addEventListener('submit', function(e) {
                                e.preventDefault();
                                console.log('📝 محاولة حفظ موعد جديد');

                                var occupationValue = document.getElementById('patient-occupation').value;
                                var finalOccupation = occupationValue === 'أخرى' ?
                                    document.getElementById('custom-occupation').value : occupationValue;

                                var appointmentData = {
                                    patientName: document.getElementById('patient-name').value,
                                    phoneNumber: document.getElementById('patient-phone').value,
                                    age: document.getElementById('patient-age').value,
                                    gender: document.getElementById('patient-gender').value,
                                    maritalStatus: document.getElementById('patient-marital-status').value,
                                    education: document.getElementById('patient-education').value,
                                    occupation: finalOccupation,
                                    height: document.getElementById('patient-height').value,
                                    weight: document.getElementById('patient-weight').value,
                                    bmi: document.getElementById('patient-bmi').value,
                                    bmiCategory: document.getElementById('bmi-category').value,
                                    medicalHistory: collectMedicalHistory(),
                                    date: document.getElementById('appointment-date').value,
                                    time: document.getElementById('appointment-time').value,
                                    visitType: document.getElementById('visit-type').value,
                                    notes: document.getElementById('appointment-notes').value,
                                    sendConfirmation: document.getElementById('send-confirmation').checked,
                                    sendReminder: document.getElementById('send-reminder').checked,
                                    reminderTime: document.getElementById('reminder-time').value
                                };

                                console.log('📋 بيانات الموعد:', appointmentData);

                                // التحقق من صحة البيانات
                                if (!appointmentData.patientName || !appointmentData.phoneNumber ||
                                    !appointmentData.date || !appointmentData.time || !appointmentData.visitType) {
                                    alert('يرجى ملء جميع الحقول المطلوبة');
                                    return;
                                }

                                // التحقق من صحة رقم الهاتف
                                if (!validatePhoneNumber(appointmentData.phoneNumber)) {
                                    alert('يرجى إدخال رقم هاتف صحيح مع كود البلد\nمثال: +966501234567 أو 966501234567');
                                    return;
                                }

                                // حفظ الموعد
                                var savedAppointment = saveAppointment(appointmentData);
                                console.log('✅ تم حفظ الموعد:', savedAppointment);

                                // إرسال تأكيد فوري إذا كان مطلوباً
                                if (appointmentData.sendConfirmation) {
                                    sendBookingConfirmation(appointmentData);
                                }

                                var patientDetails = '';
                                if (appointmentData.age) patientDetails += 'العمر: ' + appointmentData.age + ' سنة\n';
                                if (appointmentData.gender) patientDetails += 'الجنس: ' + appointmentData.gender + '\n';
                                if (appointmentData.occupation) patientDetails += 'الوظيفة: ' + appointmentData.occupation + '\n';
                                if (appointmentData.education) patientDetails += 'المؤهل: ' + appointmentData.education + '\n';

                                var successMessage = '✅ تم حجز الموعد بنجاح!\n\n' +
                                    'المريض: ' + appointmentData.patientName + '\n' +
                                    patientDetails + 'التاريخ: ' + appointmentData.date + '\n' +
                                    'الوقت: ' + appointmentData.time + '\n' +
                                    'نوع الزيارة: ' + appointmentData.visitType + '\n\n' +
                                    (appointmentData.sendConfirmation ? '📱 تم إرسال رسالة تأكيد عبر واتساب' : '');

                                alert(successMessage);

                                // إعادة تعيين النموذج
                                appointmentForm.reset();

                                // تحديث عرض المواعيد
                                displayAppointments();
                                updateAppointmentStats();

                                // الانتقال لصفحة المواعيد
                                window.location.hash = '#appointments';
                            });
                        }
                    });

                    function updateDateTime() {
                        var now = new Date();
                        var currentDate = formatDateInArabic(now); // تاريخ ميلادي بالعربية
                        var currentTime = now.toLocaleTimeString('ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        });
                        var printDate = now.toLocaleDateString('en-GB'); // تاريخ مختصر
                        var printTime = now.toLocaleTimeString('ar-SA');

                        // تحديث عناصر التاريخ والوقت
                        var dateElements = document.querySelectorAll('#current-date');
                        var timeElements = document.querySelectorAll('#current-time');
                        var todayDateElements = document.querySelectorAll('#today-date');

                        for (var i = 0; i < dateElements.length; i++) {
                            dateElements[i].textContent = currentDate;
                        }

                        for (var i = 0; i < timeElements.length; i++) {
                            timeElements[i].textContent = currentTime;
                        }

                        for (var i = 0; i < todayDateElements.length; i++) {
                            todayDateElements[i].textContent = currentDate;
                        }

                        var currentDateElement = document.getElementById('current-date');
                        var printDateElement = document.getElementById('print-date');
                        var printTimeElement = document.getElementById('print-time');

                        if (currentDateElement) currentDateElement.textContent = currentDate;
                        if (printDateElement) printDateElement.textContent = printDate;
                        if (printTimeElement) printTimeElement.textContent = printTime;
                    });
                </script>

                <style>
                    @media print {
                        body { margin: 0; }
                        .page { box-shadow: none !important; }
                        button, .btn { display: none !important; }
                        div[style*="background: #f8f9fa"][style*="padding: 20px"][style*="text-align: center"] { display: none !important; }
                    }
                </style>
            </div>

            <!-- Accounting -->
            <div id="accounting" class="page">
                <h1>💰 إدارة الحسابات</h1>
                <div class="success-msg">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>

                <div class="financial-cards">
                    <div class="financial-card">
                        <h3>💚 إجمالي الإيرادات</h3>
                        <div class="money-amount">58,500,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">+15% هذا الشهر</small>
                    </div>
                    <div class="financial-card expense">
                        <h3>💸 إجمالي المصروفات</h3>
                        <div class="money-amount">24,050,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">-5% عن الشهر الماضي</small>
                    </div>
                    <div class="financial-card profit">
                        <h3>📈 صافي الربح</h3>
                        <div class="money-amount">34,450,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">+25% نمو ممتاز</small>
                    </div>
                </div>

                <h3>💼 الخدمات المالية المتاحة:</h3>
                <ul style="line-height: 2;">
                    <li>تتبع الإيرادات والمصروفات بالدينار العراقي بدقة عالية</li>
                    <li>إدارة الفواتير والمدفوعات مع نظام تذكير تلقائي</li>
                    <li>تقارير مالية شهرية وسنوية مفصلة وشاملة</li>
                    <li>حساب الضرائب المستحقة وفقاً للقوانين المحلية</li>
                    <li>تحليل الأداء المالي مع مؤشرات الربحية</li>
                    <li>إدارة رواتب الموظفين ومكافآت الأداء</li>
                </ul>

                <a href="#new-transaction" class="btn btn-success">
                    ➕ إضافة معاملة جديدة
                </a>
                <a href="#financial-reports" class="btn">
                    📊 عرض التقارير المالية
                </a>
                <a href="#financial-settings" class="btn">
                    ⚙️ إعدادات النظام المالي
                </a>
            </div>
        </div>

        <!-- New Transaction Page -->
        <div id="new-transaction" class="page">
            <h1>➕ إضافة معاملة مالية جديدة</h1>
            <div class="success-msg">
                ✅ تم الانتقال إلى صفحة إضافة معاملة مالية جديدة!
            </div>

            <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">💰 تسجيل معاملة مالية جديدة</h3>

                <!-- نوع المعاملة -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">📋 نوع المعاملة</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">نوع المعاملة:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option>اختر نوع المعاملة</option>
                                <option>💚 إيراد - دخل</option>
                                <option>💸 مصروف - خرج</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">التصنيف:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option>اختر التصنيف</option>
                                <optgroup label="الإيرادات">
                                    <option>💊 استشارات طبية</option>
                                    <option>🍽️ خطط غذائية</option>
                                    <option>📝 وصفات طبية</option>
                                    <option>🔬 فحوصات طبية</option>
                                    <option>💰 إيرادات أخرى</option>
                                </optgroup>
                                <optgroup label="المصروفات">
                                    <option>🏠 إيجار العيادة</option>
                                    <option>⚡ فواتير الكهرباء</option>
                                    <option>💧 فواتير الماء</option>
                                    <option>📞 فواتير الاتصالات</option>
                                    <option>🧹 مواد التنظيف</option>
                                    <option>📋 مستلزمات مكتبية</option>
                                    <option>👨‍⚕️ رواتب الموظفين</option>
                                    <option>🚗 مواصلات</option>
                                    <option>💸 مصروفات أخرى</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل المعاملة -->
                <div style="margin-bottom: 25px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">💰 المبلغ (بالدينار العراقي):</label>
                            <input type="number" step="1000" placeholder="أدخل المبلغ" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📅 تاريخ المعاملة:</label>
                            <input type="date" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📝 وصف المعاملة:</label>
                        <input type="text" placeholder="مثال: استشارة طبية للمريض أحمد محمد" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #007bff;">
                    <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">📋 معلومات إضافية</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">👤 اسم المريض (للإيرادات):</label>
                            <input type="text" placeholder="اسم المريض (اختياري)" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">💳 طريقة الدفع:</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                <option>نقداً</option>
                                <option>بطاقة ائتمان</option>
                                <option>تحويل بنكي</option>
                                <option>شيك</option>
                                <option>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">🏢 اسم المورد/العميل:</label>
                        <input type="text" placeholder="اسم المورد أو العميل (اختياري)" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">📄 ملاحظات إضافية:</label>
                        <textarea placeholder="أي ملاحظات أو تفاصيل إضافية" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; min-height: 80px; resize: vertical;"></textarea>
                    </div>
                </div>

                <!-- ملخص المعاملة -->
                <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #28a745;">
                    <h4 style="color: #155724; margin-top: 0; margin-bottom: 15px;">📊 ملخص المعاملة</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; text-align: center;">
                        <div style="background: white; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #2c3e50; margin: 0 0 10px 0;">المبلغ</h5>
                            <div style="font-size: 24px; font-weight: bold; color: #28a745;">0</div>
                            <div style="font-size: 14px; color: #666;">د.ع</div>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #2c3e50; margin: 0 0 10px 0;">النوع</h5>
                            <div style="font-size: 18px; font-weight: bold; color: #007bff;">غير محدد</div>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #2c3e50; margin: 0 0 10px 0;">التاريخ</h5>
                            <div style="font-size: 16px; font-weight: bold; color: #6c757d;">اليوم</div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div style="text-align: center; margin-top: 30px;">
                    <a href="#accounting" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                        ✅ حفظ المعاملة
                    </a>
                    <a href="#new-transaction" class="btn" style="background: #17a2b8; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                        🖨️ حفظ وطباعة إيصال
                    </a>
                    <a href="#accounting" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                        ❌ إلغاء
                    </a>
                </div>
            </div>
        </div>

        <!-- Financial Reports Page -->
        <div id="financial-reports" class="page">
            <h1>📊 التقارير المالية</h1>
            <div class="success-msg">
                ✅ تم الانتقال إلى صفحة التقارير المالية!
            </div>

            <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">📈 تقارير الأداء المالي</h3>

                <!-- فلاتر التقارير -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">🔍 فلاتر التقارير</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">من تاريخ:</label>
                            <input type="date" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">إلى تاريخ:</label>
                            <input type="date" style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">نوع التقرير:</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px;">
                                <option>تقرير شامل</option>
                                <option>الإيرادات فقط</option>
                                <option>المصروفات فقط</option>
                                <option>تقرير الأرباح</option>
                            </select>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="#financial-reports" class="btn btn-success">
                            📊 إنشاء التقرير
                        </a>
                    </div>
                </div>

                <!-- ملخص مالي سريع -->
                <div class="financial-cards">
                    <div class="financial-card">
                        <h3>💚 إجمالي الإيرادات</h3>
                        <div class="money-amount">58,500,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">هذا الشهر</small>
                    </div>
                    <div class="financial-card expense">
                        <h3>💸 إجمالي المصروفات</h3>
                        <div class="money-amount">24,050,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">هذا الشهر</small>
                    </div>
                    <div class="financial-card profit">
                        <h3>📈 صافي الربح</h3>
                        <div class="money-amount">34,450,000</div>
                        <div class="currency">د.ع</div>
                        <small style="opacity: 0.8;">هذا الشهر</small>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="#accounting" class="btn">
                        ← العودة لإدارة الحسابات
                    </a>
                </div>
            </div>
        </div>

        <!-- Financial Settings Page -->
        <div id="financial-settings" class="page">
            <h1>⚙️ إعدادات النظام المالي</h1>
            <div class="success-msg">
                ✅ تم الانتقال إلى صفحة إعدادات النظام المالي!
            </div>

            <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <h3 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🔧 إعدادات النظام المالي</h3>

                <!-- إعدادات العملة -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">💰 إعدادات العملة</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">العملة الأساسية:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option selected>الدينار العراقي (د.ع)</option>
                                <option>الدولار الأمريكي ($)</option>
                                <option>اليورو (€)</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">تنسيق عرض المبالغ:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option selected>1,000,000 د.ع</option>
                                <option>د.ع 1,000,000</option>
                                <option>1000000 د.ع</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الفواتير -->
                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #ffc107;">
                    <h4 style="color: #856404; margin-top: 0; margin-bottom: 15px;">📄 إعدادات الفواتير</h4>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">اسم العيادة على الفواتير:</label>
                        <input type="text" value="عيادة التغذية العلاجية" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">عنوان العيادة:</label>
                        <input type="text" value="شارع الطب، بغداد، العراق" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">رقم الهاتف:</label>
                            <input type="tel" value="07XX XXX XXXX" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">البريد الإلكتروني:</label>
                            <input type="email" value="<EMAIL>" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>
                </div>

                <!-- إعدادات النسخ الاحتياطي -->
                <div style="background: #d1ecf1; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 5px solid #17a2b8;">
                    <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">💾 النسخ الاحتياطي</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #2c3e50;">تكرار النسخ الاحتياطي:</label>
                            <select style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px;">
                                <option>يومياً</option>
                                <option selected>أسبوعياً</option>
                                <option>شهرياً</option>
                            </select>
                        </div>
                        <div style="text-align: center; padding-top: 20px;">
                            <a href="#financial-settings" class="btn" style="background: #17a2b8;">
                                💾 إنشاء نسخة احتياطية الآن
                            </a>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div style="text-align: center; margin-top: 30px;">
                    <a href="#financial-settings" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                        ✅ حفظ الإعدادات
                    </a>
                    <a href="#accounting" class="btn" style="background: #6c757d; font-size: 18px; padding: 15px 30px; margin: 0 10px;">
                        ← العودة لإدارة الحسابات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // دالة استخراج السعرات من النص
        function extractCalories(text) {
            var match = text.match(/(\d+)\s*سعرة/);
            return match ? parseInt(match[1]) : 0;
        }

        // دالة اختبار بسيطة للإفطار مع دعم checkboxes
        function testBreakfast() {
            alert('تم النقر على زر حساب السعرات!');

            var totalCalories = 0;
            var selectedFoods = [];

            // البحث عن checkboxes الكربوهيدرات
            var carbCheckboxes = document.querySelectorAll('input[name="breakfast-carbs"]:checked');
            for (var i = 0; i < carbCheckboxes.length; i++) {
                var calories = extractCalories(carbCheckboxes[i].value);
                var foodName = carbCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن checkboxes البروتين
            var proteinCheckboxes = document.querySelectorAll('input[name="breakfast-protein"]:checked');
            for (var i = 0; i < proteinCheckboxes.length; i++) {
                var calories = extractCalories(proteinCheckboxes[i].value);
                var foodName = proteinCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن باقي القوائم المنسدلة (الخضروات، الفواكه، المشروبات)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');

            // فحص القوائم المتبقية (بدءاً من الثالثة)
            for (var i = 0; i < Math.min(3, allSelects.length); i++) {
                var select = allSelects[i];
                if (select && select.value && select.value.indexOf('اختر') === -1) {
                    var calories = extractCalories(select.value);
                    var foodName = select.value.split(' (')[0];

                    totalCalories += calories;
                    selectedFoods.push(foodName);
                }
            }

            // البحث عن حقل السعرات وحقل الملخص
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');
            var textareas = document.querySelectorAll('#create-nutrition-plan textarea');

            // تحديث أول حقل سعرات (الإفطار)
            if (caloriesInputs.length > 0) {
                caloriesInputs[0].value = totalCalories;
            }

            // تحديث أول textarea (ملخص الإفطار)
            if (textareas.length > 0) {
                if (selectedFoods.length > 0) {
                    textareas[0].value = selectedFoods.join(' + ');
                } else {
                    textareas[0].value = 'لم يتم اختيار أي أطعمة';
                }
            }

            alert('تم حساب السعرات: ' + totalCalories + ' سعرة حرارية\nالأطعمة المختارة: ' + selectedFoods.join(', '));

            // تحديث العرض الإجمالي
            autoUpdateCalories();
        }

        // دالة حساب الوجبة الخفيفة الصباحية مع دعم checkboxes
        function calculateMorningSnack() {
            var totalCalories = 0;
            var selectedFoods = [];

            // البحث عن checkboxes الوجبة الخفيفة الصباحية
            var morningSnackCheckboxes = document.querySelectorAll('input[name="morning-snack"]:checked');
            for (var i = 0; i < morningSnackCheckboxes.length; i++) {
                var calories = extractCalories(morningSnackCheckboxes[i].value);
                var foodName = morningSnackCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن حقل السعرات وحقل الملخص
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');
            var textareas = document.querySelectorAll('#create-nutrition-plan textarea');

            // تحديث حقل السعرات الثاني
            if (caloriesInputs.length > 1) {
                caloriesInputs[1].value = totalCalories;
            }

            // تحديث textarea الثانية
            if (textareas.length > 1) {
                if (selectedFoods.length > 0) {
                    textareas[1].value = selectedFoods.join(' + ');
                } else {
                    textareas[1].value = 'لم يتم اختيار أي وجبات خفيفة';
                }
            }

            if (selectedFoods.length > 0) {
                alert('تم حساب السعرات: ' + totalCalories + ' سعرة حرارية\nالوجبات المختارة: ' + selectedFoods.join(', '));
            } else {
                alert('يرجى اختيار وجبة خفيفة أولاً');
            }

            // تحديث العرض الإجمالي
            autoUpdateCalories();
        }

        // دالة حساب الغداء مع دعم checkboxes للبروتين والكربوهيدرات
        function calculateLunch() {
            var totalCalories = 0;
            var selectedFoods = [];

            // البحث عن checkboxes البروتين الرئيسي
            var lunchProteinCheckboxes = document.querySelectorAll('input[name="lunch-protein"]:checked');
            for (var i = 0; i < lunchProteinCheckboxes.length; i++) {
                var calories = extractCalories(lunchProteinCheckboxes[i].value);
                var foodName = lunchProteinCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن checkboxes الكربوهيدرات
            var lunchCarbsCheckboxes = document.querySelectorAll('input[name="lunch-carbs"]:checked');
            for (var i = 0; i < lunchCarbsCheckboxes.length; i++) {
                var calories = extractCalories(lunchCarbsCheckboxes[i].value);
                var foodName = lunchCarbsCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن باقي القوائم المنسدلة للغداء (السلطة، الشوربة)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');

            // فحص القوائم المتبقية للغداء (السلطة والشوربة)
            var startIndex = 3; // تعديل الفهرس حسب الترتيب الجديد
            for (var i = startIndex; i < Math.min(startIndex + 2, allSelects.length); i++) {
                var select = allSelects[i];
                if (select && select.value && select.value.indexOf('اختر') === -1) {
                    var calories = extractCalories(select.value);
                    var foodName = select.value.split(' (')[0];

                    totalCalories += calories;
                    selectedFoods.push(foodName);
                }
            }

            // البحث عن حقل السعرات وحقل الملخص
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');
            var textareas = document.querySelectorAll('#create-nutrition-plan textarea');

            // تحديث حقل السعرات الثالث
            if (caloriesInputs.length > 2) {
                caloriesInputs[2].value = totalCalories;
            }

            // تحديث textarea الثالثة
            if (textareas.length > 2) {
                if (selectedFoods.length > 0) {
                    textareas[2].value = selectedFoods.join(' + ');
                } else {
                    textareas[2].value = 'لم يتم اختيار أي أطعمة';
                }
            }

            alert('تم حساب السعرات: ' + totalCalories + ' سعرة حرارية\nالأطعمة المختارة: ' + selectedFoods.join(', '));
        }

        // دالة حساب الوجبة الخفيفة المسائية مع دعم checkboxes
        function calculateEveningSnack() {
            var totalCalories = 0;
            var selectedFoods = [];

            // البحث عن checkboxes الوجبة الخفيفة المسائية
            var eveningSnackCheckboxes = document.querySelectorAll('input[name="evening-snack"]:checked');
            for (var i = 0; i < eveningSnackCheckboxes.length; i++) {
                var calories = extractCalories(eveningSnackCheckboxes[i].value);
                var foodName = eveningSnackCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن حقل السعرات وحقل الملخص
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');
            var textareas = document.querySelectorAll('#create-nutrition-plan textarea');

            // تحديث حقل السعرات الرابع
            if (caloriesInputs.length > 3) {
                caloriesInputs[3].value = totalCalories;
            }

            // تحديث textarea الرابعة
            if (textareas.length > 3) {
                if (selectedFoods.length > 0) {
                    textareas[3].value = selectedFoods.join(' + ');
                } else {
                    textareas[3].value = 'لم يتم اختيار أي وجبات خفيفة';
                }
            }

            if (selectedFoods.length > 0) {
                alert('تم حساب السعرات: ' + totalCalories + ' سعرة حرارية\nالوجبات المختارة: ' + selectedFoods.join(', '));
            } else {
                alert('يرجى اختيار وجبة خفيفة أولاً');
            }

            // تحديث العرض الإجمالي
            autoUpdateCalories();
        }

        // دالة حساب العشاء مع دعم checkboxes للبروتين والكربوهيدرات
        function calculateDinner() {
            var totalCalories = 0;
            var selectedFoods = [];

            // البحث عن checkboxes البروتين الخفيف
            var dinnerProteinCheckboxes = document.querySelectorAll('input[name="dinner-protein"]:checked');
            for (var i = 0; i < dinnerProteinCheckboxes.length; i++) {
                var calories = extractCalories(dinnerProteinCheckboxes[i].value);
                var foodName = dinnerProteinCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن checkboxes الكربوهيدرات الخفيفة
            var dinnerCarbsCheckboxes = document.querySelectorAll('input[name="dinner-carbs"]:checked');
            for (var i = 0; i < dinnerCarbsCheckboxes.length; i++) {
                var calories = extractCalories(dinnerCarbsCheckboxes[i].value);
                var foodName = dinnerCarbsCheckboxes[i].value.split(' (')[0];
                totalCalories += calories;
                selectedFoods.push(foodName);
            }

            // البحث عن باقي القوائم المنسدلة للعشاء (الخضروات)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');

            // فحص قائمة الخضروات (تحديد الفهرس المناسب)
            var vegetableSelectIndex = 5; // تعديل الفهرس حسب الترتيب الجديد
            if (allSelects.length > vegetableSelectIndex) {
                var select = allSelects[vegetableSelectIndex];
                if (select && select.value && select.value.indexOf('اختر') === -1) {
                    var calories = extractCalories(select.value);
                    var foodName = select.value.split(' (')[0];

                    totalCalories += calories;
                    selectedFoods.push(foodName);
                }
            }

            // البحث عن حقل السعرات وحقل الملخص
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');
            var textareas = document.querySelectorAll('#create-nutrition-plan textarea');

            // تحديث حقل السعرات الخامس
            if (caloriesInputs.length > 4) {
                caloriesInputs[4].value = totalCalories;
            }

            // تحديث textarea الخامسة
            if (textareas.length > 4) {
                if (selectedFoods.length > 0) {
                    textareas[4].value = selectedFoods.join(' + ');
                } else {
                    textareas[4].value = 'لم يتم اختيار أي أطعمة';
                }
            }

            alert('تم حساب السعرات: ' + totalCalories + ' سعرة حرارية\nالأطعمة المختارة: ' + selectedFoods.join(', '));
        }

        // دالة تنسيق التاريخ الميلادي بالعربية
        function formatDateInArabic(date) {
            var months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            var day = date.getDate();
            var month = months[date.getMonth()];
            var year = date.getFullYear();

            return day + ' ' + month + ' ' + year;
        }

        // دوال حفظ وإدارة الخطط الغذائية

        // دالة معاينة الخطة الحالية
        function previewCurrentPlan() {
            // جمع بيانات المريض
            var patientInputs = document.querySelectorAll('#create-nutrition-plan input[type="text"], #create-nutrition-plan input[type="number"], #create-nutrition-plan select');
            var patientName = patientInputs[0] ? patientInputs[0].value : '';
            var patientAge = patientInputs[1] ? patientInputs[1].value : '';

            if (!patientName.trim()) {
                alert('يرجى إدخال اسم المريض أولاً لمعاينة الخطة');
                return;
            }

            // إنشاء خطة مؤقتة للمعاينة
            var now = new Date();
            var tempPlan = {
                id: 'temp_preview',
                date: formatDateInArabic(now),
                dateShort: now.toLocaleDateString('en-GB'),
                time: now.toLocaleTimeString('ar-SA'),
                patientName: patientName,
                patientAge: patientAge,
                meals: {
                    breakfast: collectMealData('breakfast'),
                    morningSnack: collectMealData('morning-snack'),
                    lunch: collectMealData('lunch'),
                    eveningSnack: collectMealData('evening-snack'),
                    dinner: collectMealData('dinner')
                },
                totalCalories: calculateTotalCalories(),
                notes: document.querySelector('#create-nutrition-plan textarea:last-of-type') ? document.querySelector('#create-nutrition-plan textarea:last-of-type').value : ''
            };

            // عرض المعاينة
            currentPreviewPlan = tempPlan;
            displayPlanPreview(tempPlan);
            window.location.hash = '#plan-preview';
        }

        // دالة حفظ الخطة الغذائية
        function saveNutritionPlan() {
            // جمع بيانات المريض
            var patientInputs = document.querySelectorAll('#create-nutrition-plan input[type="text"], #create-nutrition-plan input[type="number"], #create-nutrition-plan select');
            var patientName = patientInputs[0] ? patientInputs[0].value : '';
            var patientAge = patientInputs[1] ? patientInputs[1].value : '';

            if (!patientName.trim()) {
                alert('يرجى إدخال اسم المريض أولاً');
                return;
            }

            // جمع بيانات الوجبات
            var now = new Date();
            var nutritionPlan = {
                id: 'plan_' + Date.now(),
                date: formatDateInArabic(now), // تاريخ ميلادي بالعربية
                dateShort: now.toLocaleDateString('en-GB'), // تاريخ مختصر للطباعة
                time: now.toLocaleTimeString('ar-SA'),
                patientName: patientName,
                patientAge: patientAge,
                meals: {
                    breakfast: collectMealData('breakfast'),
                    morningSnack: collectMealData('morning-snack'),
                    lunch: collectMealData('lunch'),
                    eveningSnack: collectMealData('evening-snack'),
                    dinner: collectMealData('dinner')
                },
                totalCalories: calculateTotalCalories(),
                notes: document.querySelector('#create-nutrition-plan textarea:last-of-type') ? document.querySelector('#create-nutrition-plan textarea:last-of-type').value : ''
            };

            // حفظ في localStorage
            var savedPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            savedPlans.push(nutritionPlan);
            localStorage.setItem('nutritionPlans', JSON.stringify(savedPlans));

            alert('✅ تم حفظ الخطة الغذائية بنجاح!\nاسم المريض: ' + patientName + '\nتاريخ الحفظ: ' + nutritionPlan.date);

            // الانتقال لصفحة الخطط المحفوظة
            window.location.hash = '#saved-plans';
            loadSavedPlans();
        }

        // دالة حفظ وطباعة الخطة
        function saveAndPrintNutritionPlan() {
            // حفظ الخطة أولاً
            var plan = saveNutritionPlan();
            if (plan) {
                // عرض معاينة الطباعة
                setTimeout(function() {
                    showNutritionPrintPreview(plan);
                }, 500);
            }
        }

        // دالة عرض معاينة طباعة الخطة الغذائية
        function showNutritionPrintPreview(plan) {
            // تحميل إعدادات العيادة بشكل آمن
            var settings = getSafeClinicSettings();
            console.log('✅ تم تحميل إعدادات العيادة للخطة الغذائية:', settings);

            // إنشاء محتوى الشعار
            var logoHtml = '';
            if (settings.logoType === 'image' && settings.logoImage) {
                logoHtml = '<img src="' + settings.logoImage + '" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 3px solid #3498db;">';
            } else {
                logoHtml = '<div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid #3498db;">' +
                    '<span style="font-size: 32px; color: white;">' + settings.logo + '</span>' +
                '</div>';
            }

            var previewContent = `
                <div style="max-width: 900px; margin: 0 auto; padding: 40px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: white; border: 2px solid #3498db; border-radius: 20px; box-shadow: 0 15px 40px rgba(0,0,0,0.1);">
                    <!-- هيدر العيادة -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px; padding-bottom: 30px; border-bottom: 4px solid #3498db;">
                        <div style="display: flex; align-items: center; gap: 25px;">
                            ${logoHtml}
                            <div>
                                <h1 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 32px; font-weight: bold;">${settings.clinicName}</h1>
                                <p style="color: #7f8c8d; font-size: 18px; margin: 0 0 8px 0;">${settings.specialty}</p>
                                <p style="color: #95a5a6; font-size: 16px; margin: 0;">📍 ${settings.address} | 📞 ${settings.phone}</p>
                            </div>
                        </div>
                        <div style="text-align: right; background: #f8f9fa; padding: 25px; border-radius: 15px; border: 2px solid #e9ecef;">
                            <div style="font-size: 20px; color: #2c3e50; margin-bottom: 10px; font-weight: bold;">👨‍⚕️ ${settings.doctorName}</div>
                            <div style="font-size: 16px; color: #7f8c8d; margin-bottom: 8px;">رقم الترخيص: ${settings.licenseNumber}</div>
                            <div style="font-size: 16px; color: #3498db;">📅 ${formatDateInArabic(new Date())}</div>
                        </div>
                    </div>

                    <!-- عنوان الخطة -->
                    <h2 style="text-align: center; color: #27ae60; margin: 40px 0; font-size: 36px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">🍽️ خطة غذائية مخصصة</h2>

                    <!-- معلومات المريض -->
                    <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 20px; margin-bottom: 40px; border: 2px solid #dee2e6;">
                        <h3 style="color: #2c3e50; margin-bottom: 25px; font-size: 24px; border-bottom: 3px solid #27ae60; padding-bottom: 15px;">👤 معلومات المريض</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">
                            <div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">
                                <strong style="color: #2c3e50; font-size: 18px;">الاسم:</strong><br>
                                <span style="color: #495057; font-size: 20px; font-weight: bold;">${plan.patientName}</span>
                            </div>
                            <div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">
                                <strong style="color: #2c3e50; font-size: 18px;">العمر:</strong><br>
                                <span style="color: #495057; font-size: 20px; font-weight: bold;">${plan.patientAge} سنة</span>
                            </div>
                            <div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">
                                <strong style="color: #2c3e50; font-size: 18px;">تاريخ الخطة:</strong><br>
                                <span style="color: #495057; font-size: 20px; font-weight: bold;">${plan.date}</span>
                            </div>
                            <div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">
                                <strong style="color: #2c3e50; font-size: 18px;">وقت الإنشاء:</strong><br>
                                <span style="color: #495057; font-size: 20px; font-weight: bold;">${plan.time}</span>
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي السعرات -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 20px; text-align: center; margin-bottom: 40px; box-shadow: 0 8px 25px rgba(0,0,0,0.2);">
                        <h3 style="margin: 0 0 15px 0; font-size: 28px;">🔥 إجمالي السعرات اليومية</h3>
                        <div style="font-size: 48px; font-weight: bold; text-shadow: 3px 3px 6px rgba(0,0,0,0.3);">${plan.totalCalories}</div>
                        <div style="font-size: 24px; margin-top: 10px; opacity: 0.9;">سعرة حرارية</div>
                    </div>

                    <!-- الوجبات -->
                    <div style="display: grid; gap: 30px;">
                        ${generatePrintMealPreview('🌅 الإفطار', plan.meals.breakfast)}
                        ${generatePrintMealPreview('☕ وجبة خفيفة صباحية', plan.meals.morningSnack)}
                        ${generatePrintMealPreview('🍽️ الغداء', plan.meals.lunch)}
                        ${generatePrintMealPreview('🍎 وجبة خفيفة مسائية', plan.meals.eveningSnack)}
                        ${generatePrintMealPreview('🌙 العشاء', plan.meals.dinner)}
                    </div>

                    ${plan.notes ? `
                    <div style="background: #fff3cd; padding: 30px; border-radius: 20px; margin-top: 40px; border-right: 8px solid #ffc107;">
                        <h4 style="color: #856404; margin: 0 0 20px 0; font-size: 24px;">💡 نصائح وتعليمات خاصة:</h4>
                        <p style="color: #856404; margin: 0; line-height: 1.8; font-size: 18px;">${plan.notes}</p>
                    </div>
                    ` : ''}

                    <!-- توقيع الطبيب -->
                    <div style="text-align: left; margin-top: 50px; padding-top: 30px; border-top: 3px solid #e9ecef;">
                        <div style="display: inline-block; text-align: center; padding: 30px; border: 3px solid #27ae60; border-radius: 15px; background: #f8f9fa;">
                            <div style="font-size: 20px; color: #2c3e50; margin-bottom: 15px; font-weight: bold;">توقيع أخصائي التغذية</div>
                            <div style="font-size: 28px; color: #27ae60; margin-bottom: 15px; font-weight: bold;">${settings.doctorName}</div>
                            <div style="width: 200px; height: 3px; background: #27ae60; margin: 15px auto;"></div>
                        </div>
                    </div>

                    <!-- تذييل -->
                    <div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 2px solid #e9ecef; color: #6c757d; font-size: 14px;">
                        <p style="margin: 0 0 10px 0;">هذه الخطة الغذائية صادرة من ${settings.clinicName}</p>
                        <p style="margin: 0 0 10px 0;">📞 ${settings.phone} | 📧 ${settings.email} | 📍 ${settings.address}</p>
                        <p style="margin: 0;">تاريخ الإصدار: ${formatDateInArabic(new Date())} | رقم الترخيص: ${settings.licenseNumber}</p>
                    </div>
                </div>
            `;

            // فتح نافذة معاينة
            var previewWindow = window.open('', 'nutritionPreview', 'width=1200,height=900,scrollbars=yes,resizable=yes');

            if (!previewWindow) {
                alert('⚠️ تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
                return;
            }

            // التأكد من أن النافذة جاهزة
            if (previewWindow.document) {
                previewWindow.document.open();
                console.log('✅ تم فتح نافذة معاينة الخطة الغذائية بنجاح');
            } else {
                alert('⚠️ خطأ في فتح نافذة المعاينة. يرجى المحاولة مرة أخرى.');
                return;
            }

            // إنشاء محتوى HTML بطريقة آمنة
            var htmlContent = '<!DOCTYPE html>' +
                '<html dir="rtl" lang="ar">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>معاينة الخطة الغذائية - ' + settings.clinicName + '</title>' +
                '<style>' +
                'body { margin: 0; padding: 20px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }' +
                '.preview-container { background: white; padding: 0; border-radius: 20px; box-shadow: 0 20px 50px rgba(0,0,0,0.15); overflow: hidden; }' +
                '.control-bar { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 25px; text-align: center; margin-bottom: 0; }' +
                '.print-btn { background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 10px; cursor: pointer; margin: 0 15px; font-size: 18px; font-weight: bold; transition: all 0.3s ease; }' +
                '.print-btn:hover { background: #218838; transform: translateY(-3px); }' +
                '.close-btn { background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 10px; cursor: pointer; margin: 0 15px; font-size: 18px; font-weight: bold; transition: all 0.3s ease; }' +
                '.close-btn:hover { background: #c82333; transform: translateY(-3px); }' +
                '@media print { .no-print { display: none; } body { background: white; padding: 0; } .preview-container { box-shadow: none; border-radius: 0; } }' +
                '</style>' +
                '</head>' +
                '<body>' +
                '<div class="preview-container">' +
                '<div class="control-bar no-print">' +
                '<h2 style="margin: 0 0 20px 0;">🍽️ معاينة الخطة الغذائية</h2>' +
                '<p style="margin: 0 0 25px 0; opacity: 0.9; font-size: 16px;">تأكد من صحة البيانات قبل الطباعة</p>' +
                '<button class="print-btn" onclick="window.print()">🖨️ طباعة الخطة</button>' +
                '<button class="close-btn" onclick="window.close()">❌ إغلاق المعاينة</button>' +
                '</div>' +
                previewContent +
                '</div>' +
                '</body>' +
                '</html>';

            // كتابة المحتوى إلى النافذة
            try {
                previewWindow.document.write(htmlContent);
                previewWindow.document.close();
                console.log('✅ تم كتابة وإغلاق document للخطة الغذائية بنجاح');

                // التركيز على النافذة الجديدة
                previewWindow.focus();

            } catch (error) {
                console.log('⚠️ خطأ في كتابة محتوى الخطة الغذائية:', error);
                alert('⚠️ حدث خطأ في عرض معاينة الخطة الغذائية. يرجى المحاولة مرة أخرى.');
                if (previewWindow) {
                    previewWindow.close();
                }
            }
        }

        // دالة إنشاء معاينة الوجبة للطباعة
        function generatePrintMealPreview(mealName, mealData) {
            if (!mealData || mealData.length === 0) {
                return `
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border: 2px solid #e9ecef;">
                        <h4 style="color: #6c757d; margin: 0; font-size: 20px;">${mealName}</h4>
                        <p style="color: #6c757d; margin: 10px 0 0 0; font-style: italic;">لم يتم تحديد أطعمة لهذه الوجبة</p>
                    </div>
                `;
            }

            var totalCalories = mealData.reduce(function(sum, food) {
                return sum + (food.calories || 0);
            }, 0);

            var foodsHtml = mealData.map(function(food) {
                return `
                    <div style="background: white; padding: 20px; border-radius: 12px; border: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 5px;">${food.name}</div>
                            <div style="font-size: 14px; color: #6c757d;">الكمية: ${food.quantity || 'غير محدد'}</div>
                        </div>
                        <div style="text-align: left;">
                            <div style="font-size: 20px; font-weight: bold; color: #e74c3c;">${food.calories || 0}</div>
                            <div style="font-size: 12px; color: #6c757d;">سعرة</div>
                        </div>
                    </div>
                `;
            }).join('');

            return `
                <div style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); padding: 30px; border-radius: 20px; border: 2px solid #27ae60;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding-bottom: 20px; border-bottom: 2px solid #27ae60;">
                        <h4 style="color: #27ae60; margin: 0; font-size: 24px; font-weight: bold;">${mealName}</h4>
                        <div style="background: #27ae60; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; font-size: 16px;">
                            ${totalCalories} سعرة حرارية
                        </div>
                    </div>
                    <div style="display: grid; gap: 15px;">
                        ${foodsHtml}
                    </div>
                </div>
            `;
        }

        // دالة حفظ وطباعة الوصفة الطبية
        function saveAndPrintPrescription() {
            console.log('🖨️ بدء عملية حفظ وطباعة الوصفة');

            var patientName = document.getElementById('patient-name-prescription').value;
            var patientAge = document.getElementById('patient-age-prescription').value;
            var diagnosis = document.getElementById('diagnosis').value;
            var medications = document.getElementById('medications').value;
            var instructions = document.getElementById('instructions').value;

            if (!patientName || !diagnosis || !medications) {
                alert('يرجى ملء جميع الحقول المطلوبة قبل الحفظ والطباعة');
                return;
            }

            // التأكد من تحميل إعدادات العيادة
            var testSettings = getSafeClinicSettings();
            console.log('✅ إعدادات العيادة متاحة للطباعة:', testSettings);

            if (!testSettings.doctorName || testSettings.doctorName === 'د. أحمد محمد علي') {
                console.log('⚠️ يتم استخدام الإعدادات الافتراضية');
            }

            // إنشاء بيانات الوصفة
            var prescription = {
                id: 'prescription_' + Date.now(),
                patientName: patientName,
                patientAge: patientAge,
                diagnosis: diagnosis,
                medications: medications,
                instructions: instructions,
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('ar-SA', { hour12: false }),
                createdAt: new Date().toISOString()
            };

            // حفظ الوصفة
            var prescriptions = JSON.parse(localStorage.getItem('prescriptions') || '[]');
            prescriptions.push(prescription);
            localStorage.setItem('prescriptions', JSON.stringify(prescriptions));

            console.log('✅ تم حفظ الوصفة:', prescription);

            // عرض معاينة الطباعة
            showPrescriptionPrintPreview(prescription);
        }

        // دالة عرض معاينة طباعة الوصفة الطبية
        function showPrescriptionPrintPreview(prescription) {
            console.log('🖨️ بدء عرض معاينة طباعة الوصفة');
            console.log('📋 بيانات الوصفة المستلمة:', prescription);

            // تحميل إعدادات العيادة بشكل آمن
            var settings = getSafeClinicSettings();
            console.log('✅ تم تحميل إعدادات العيادة للوصفة:', settings);
            console.log('👨‍⚕️ اسم الطبيب:', settings.doctorName);
            console.log('🏥 اسم العيادة:', settings.clinicName);

            // إنشاء محتوى الشعار
            var logoHtml = '';
            if (settings.logoType === 'image' && settings.logoImage) {
                logoHtml = '<img src="' + settings.logoImage + '" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 3px solid #3498db;">';
            } else {
                logoHtml = '<div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid #3498db;">' +
                    '<span style="font-size: 32px; color: white;">' + settings.logo + '</span>' +
                '</div>';
            }

            // إنشاء محتوى الوصفة
            var previewContent =
                '<div style="max-width: 900px; margin: 0 auto; padding: 40px; font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif; background: white; border: 2px solid #3498db; border-radius: 20px; box-shadow: 0 15px 40px rgba(0,0,0,0.1);">' +
                    '<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px; padding-bottom: 30px; border-bottom: 4px solid #3498db;">' +
                        '<div style="display: flex; align-items: center; gap: 25px;">' +
                            logoHtml +
                            '<div>' +
                                '<h1 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 32px; font-weight: bold;">' + settings.clinicName + '</h1>' +
                                '<p style="color: #7f8c8d; font-size: 18px; margin: 0 0 8px 0;">' + settings.specialty + '</p>' +
                                '<p style="color: #95a5a6; font-size: 16px; margin: 0;">📍 ' + settings.address + ' | 📞 ' + settings.phone + '</p>' +
                            '</div>' +
                        '</div>' +
                        '<div style="text-align: right; background: #f8f9fa; padding: 25px; border-radius: 15px; border: 2px solid #e9ecef;">' +
                            '<div style="font-size: 20px; color: #2c3e50; margin-bottom: 10px; font-weight: bold;">👨‍⚕️ ' + settings.doctorName + '</div>' +
                            '<div style="font-size: 16px; color: #7f8c8d; margin-bottom: 8px;">رقم الترخيص: ' + settings.licenseNumber + '</div>' +
                            '<div style="font-size: 16px; color: #3498db;">📅 ' + formatDateInArabic(new Date()) + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<h2 style="text-align: center; color: #e74c3c; margin: 40px 0; font-size: 36px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">📋 وصفة طبية</h2>' +
                    '<div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 20px; margin-bottom: 40px; border: 2px solid #dee2e6;">' +
                        '<h3 style="color: #2c3e50; margin-bottom: 25px; font-size: 24px; border-bottom: 3px solid #3498db; padding-bottom: 15px;">👤 معلومات المريض</h3>' +
                        '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">' +
                            '<div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">' +
                                '<strong style="color: #2c3e50; font-size: 18px;">الاسم:</strong><br>' +
                                '<span style="color: #495057; font-size: 22px; font-weight: bold;">' + prescription.patientName + '</span>' +
                            '</div>' +
                            '<div style="background: white; padding: 20px; border-radius: 15px; border: 1px solid #dee2e6;">' +
                                '<strong style="color: #2c3e50; font-size: 18px;">العمر:</strong><br>' +
                                '<span style="color: #495057; font-size: 22px; font-weight: bold;">' + (prescription.patientAge || 'غير محدد') + ' سنة</span>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div style="background: #fff3cd; padding: 30px; border-radius: 20px; margin-bottom: 40px; border: 2px solid #ffc107;">' +
                        '<h3 style="color: #856404; margin-bottom: 20px; font-size: 24px; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">🩺 التشخيص</h3>' +
                        '<p style="color: #856404; font-size: 18px; line-height: 1.8; margin: 0; font-weight: 500;">' + prescription.diagnosis + '</p>' +
                    '</div>' +
                    '<div style="background: #d4edda; padding: 30px; border-radius: 20px; margin-bottom: 40px; border: 2px solid #28a745;">' +
                        '<h3 style="color: #155724; margin-bottom: 25px; font-size: 24px; border-bottom: 2px solid #28a745; padding-bottom: 10px;">💊 الأدوية الموصوفة</h3>' +
                        '<div style="color: #155724; font-size: 18px; line-height: 2; white-space: pre-line; font-weight: 500;">' + prescription.medications + '</div>' +
                    '</div>' +
                    '<div style="background: #cce5ff; padding: 30px; border-radius: 20px; margin-bottom: 40px; border: 2px solid #007bff;">' +
                        '<h3 style="color: #004085; margin-bottom: 25px; font-size: 24px; border-bottom: 2px solid #007bff; padding-bottom: 10px;">📝 تعليمات خاصة</h3>' +
                        '<div style="color: #004085; font-size: 18px; line-height: 2; white-space: pre-line; font-weight: 500;">' + (prescription.instructions || 'لا توجد تعليمات خاصة') + '</div>' +
                    '</div>' +
                    '<div style="text-align: left; margin-top: 50px; padding-top: 30px; border-top: 3px solid #e9ecef;">' +
                        '<div style="display: inline-block; text-align: center; padding: 30px; border: 3px solid #3498db; border-radius: 15px; background: #f8f9fa;">' +
                            '<div style="font-size: 20px; color: #2c3e50; margin-bottom: 15px; font-weight: bold;">توقيع الطبيب</div>' +
                            '<div style="font-size: 28px; color: #3498db; margin-bottom: 15px; font-weight: bold;">' + settings.doctorName + '</div>' +
                            '<div style="font-size: 16px; color: #7f8c8d;">رقم الترخيص: ' + settings.licenseNumber + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 2px solid #e9ecef; color: #6c757d; font-size: 14px;">' +
                        '<p style="margin: 0 0 10px 0;">هذه الوصفة الطبية صادرة من ' + settings.clinicName + '</p>' +
                        '<p style="margin: 0 0 10px 0;">📞 ' + settings.phone + ' | 📧 ' + settings.email + ' | 📍 ' + settings.address + '</p>' +
                        '<p style="margin: 0;">تاريخ الإصدار: ' + formatDateInArabic(new Date()) + ' | رقم الترخيص: ' + settings.licenseNumber + '</p>' +
                    '</div>' +
                '</div>';

            console.log('🪟 فتح نافذة معاينة الطباعة');
            console.log('📄 عنوان النافذة سيكون:', 'معاينة الوصفة الطبية - ' + settings.clinicName);

            // فتح نافذة معاينة
            var previewWindow = window.open('', 'prescriptionPreview', 'width=1200,height=900,scrollbars=yes,resizable=yes');

            if (!previewWindow) {
                alert('⚠️ تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
                return;
            }

            // التأكد من أن النافذة جاهزة
            if (previewWindow.document) {
                previewWindow.document.open();
                console.log('✅ تم فتح نافذة المعاينة بنجاح');
            } else {
                alert('⚠️ خطأ في فتح نافذة المعاينة. يرجى المحاولة مرة أخرى.');
                return;
            }

            // إنشاء محتوى HTML بطريقة آمنة
            var htmlContent = '<!DOCTYPE html>' +
                '<html dir="rtl" lang="ar">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>معاينة الوصفة الطبية - ' + settings.clinicName + '</title>';
            htmlContent += '<style>' +
                'body { margin: 0; padding: 20px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }' +
                '.preview-container { background: white; padding: 0; border-radius: 20px; box-shadow: 0 20px 50px rgba(0,0,0,0.15); overflow: hidden; }' +
                '.control-bar { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 25px; text-align: center; margin-bottom: 0; }' +
                '.print-btn { background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 10px; cursor: pointer; margin: 0 15px; font-size: 18px; font-weight: bold; transition: all 0.3s ease; }' +
                '.print-btn:hover { background: #218838; transform: translateY(-3px); }' +
                '.close-btn { background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 10px; cursor: pointer; margin: 0 15px; font-size: 18px; font-weight: bold; transition: all 0.3s ease; }' +
                '.close-btn:hover { background: #c82333; transform: translateY(-3px); }' +
                '@media print { .no-print { display: none; } body { background: white; padding: 0; } .preview-container { box-shadow: none; border-radius: 0; } }' +
                '</style>';
            htmlContent += '<script>' +
                'window.onload = function() {' +
                'console.log("🔍 فحص محتوى نافذة المعاينة");' +
                'console.log("👨‍⚕️ اسم الطبيب في النافذة:", "' + settings.doctorName + '");' +
                'console.log("🏥 اسم العيادة في النافذة:", "' + settings.clinicName + '");' +
                '};' +
                '</script>' +
                '</head>' +
                '<body>';
            htmlContent += '<div class="preview-container">' +
                '<div class="control-bar no-print">' +
                '<h2 style="margin: 0 0 20px 0;">📋 معاينة الوصفة الطبية</h2>' +
                '<p style="margin: 0 0 25px 0; opacity: 0.9; font-size: 16px;">تأكد من صحة البيانات قبل الطباعة</p>' +
                '<button class="print-btn" onclick="window.print()">🖨️ طباعة الوصفة</button>' +
                '<button class="close-btn" onclick="window.close()">❌ إغلاق المعاينة</button>' +
                '</div>' +
                previewContent +
                '</div>' +
                '</body>' +
                '</html>';

            console.log('📄 محتوى HTML المكتمل:', htmlContent.length + ' حرف');

            // كتابة المحتوى إلى النافذة
            try {
                previewWindow.document.write(htmlContent);
                previewWindow.document.close();
                console.log('✅ تم كتابة وإغلاق document بنجاح');

                // التركيز على النافذة الجديدة
                previewWindow.focus();

                // إظهار رسالة نجاح
                alert('✅ تم حفظ الوصفة بنجاح!\n\nتم فتح نافذة المعاينة للطباعة.');
            } catch (error) {
                console.log('⚠️ خطأ في كتابة المحتوى:', error);
                alert('⚠️ حدث خطأ في عرض المعاينة. يرجى المحاولة مرة أخرى.');
                if (previewWindow) {
                    previewWindow.close();
                }
            }
        }

        // دالة جمع بيانات الوجبة
        function collectMealData(mealType) {
            var selectedFoods = [];
            var totalCalories = 0;

            // جمع الأطعمة المختارة من checkboxes
            var checkboxes = document.querySelectorAll('input[name*="' + mealType + '"]:checked');
            for (var i = 0; i < checkboxes.length; i++) {
                var foodItem = checkboxes[i].value;
                var calories = extractCalories(foodItem);
                var foodName = foodItem.split(' (')[0];

                selectedFoods.push({
                    name: foodName,
                    details: foodItem,
                    calories: calories
                });
                totalCalories += calories;
            }

            return {
                foods: selectedFoods,
                totalCalories: totalCalories,
                summary: selectedFoods.map(function(food) { return food.name; }).join(' + ')
            };
        }

        // دالة حساب إجمالي السعرات المحسنة
        function calculateTotalCalories() {
            var breakfastCalories = calculateMealCalories('breakfast');
            var morningSnackCalories = calculateMealCalories('morning-snack');
            var lunchCalories = calculateMealCalories('lunch');
            var eveningSnackCalories = calculateMealCalories('evening-snack');
            var dinnerCalories = calculateMealCalories('dinner');

            var total = breakfastCalories + morningSnackCalories + lunchCalories + eveningSnackCalories + dinnerCalories;

            return total;
        }

        // دالة حساب سعرات وجبة محددة
        function calculateMealCalories(mealType) {
            var totalCalories = 0;

            // حساب السعرات من checkboxes
            var checkboxes = document.querySelectorAll('input[name*="' + mealType + '"]:checked');
            for (var i = 0; i < checkboxes.length; i++) {
                var calories = extractCalories(checkboxes[i].value);
                totalCalories += calories;
            }

            return totalCalories;
        }

        // دالة حساب جميع الوجبات وتحديث العرض
        function calculateAllMealsCalories() {
            // حساب سعرات كل وجبة
            var breakfastCalories = calculateMealCalories('breakfast');
            var morningSnackCalories = calculateMealCalories('morning-snack');
            var lunchCalories = calculateMealCalories('lunch');
            var eveningSnackCalories = calculateMealCalories('evening-snack');
            var dinnerCalories = calculateMealCalories('dinner');

            // تحديث العرض لكل وجبة
            document.getElementById('breakfast-calories').textContent = breakfastCalories + ' سعرة';
            document.getElementById('morning-snack-calories').textContent = morningSnackCalories + ' سعرة';
            document.getElementById('lunch-calories').textContent = lunchCalories + ' سعرة';
            document.getElementById('evening-snack-calories').textContent = eveningSnackCalories + ' سعرة';
            document.getElementById('dinner-calories').textContent = dinnerCalories + ' سعرة';

            // حساب الإجمالي
            var totalCalories = breakfastCalories + morningSnackCalories + lunchCalories + eveningSnackCalories + dinnerCalories;
            document.getElementById('total-daily-calories').textContent = totalCalories + ' سعرة حرارية';

            // تحديث حقول السعرات في كل وجبة
            updateMealCaloriesInputs();

            // عرض رسالة تفصيلية
            var message = '🔥 تم حساب إجمالي السعرات الحرارية:\n\n';
            message += '🌅 الإفطار: ' + breakfastCalories + ' سعرة\n';
            message += '☕ وجبة خفيفة صباحية: ' + morningSnackCalories + ' سعرة\n';
            message += '🍽️ الغداء: ' + lunchCalories + ' سعرة\n';
            message += '🍎 وجبة خفيفة مسائية: ' + eveningSnackCalories + ' سعرة\n';
            message += '🌙 العشاء: ' + dinnerCalories + ' سعرة\n\n';
            message += '📊 الإجمالي النهائي: ' + totalCalories + ' سعرة حرارية';

            alert(message);

            return totalCalories;
        }

        // دالة تحديث حقول السعرات في الوجبات
        function updateMealCaloriesInputs() {
            var caloriesInputs = document.querySelectorAll('#create-nutrition-plan input[type="number"]');

            if (caloriesInputs.length >= 5) {
                caloriesInputs[0].value = calculateMealCalories('breakfast');
                caloriesInputs[1].value = calculateMealCalories('morning-snack');
                caloriesInputs[2].value = calculateMealCalories('lunch');
                caloriesInputs[3].value = calculateMealCalories('evening-snack');
                caloriesInputs[4].value = calculateMealCalories('dinner');
            }
        }

        // دالة تحديث تلقائي للسعرات عند تغيير الاختيارات
        function autoUpdateCalories() {
            setTimeout(function() {
                calculateAllMealsCalories();
            }, 500);
        }

        // إضافة مستمعات الأحداث للتحديث التلقائي
        document.addEventListener('change', function(event) {
            if (event.target.type === 'checkbox' && event.target.name &&
                (event.target.name.includes('breakfast') ||
                 event.target.name.includes('morning-snack') ||
                 event.target.name.includes('lunch') ||
                 event.target.name.includes('evening-snack') ||
                 event.target.name.includes('dinner'))) {
                autoUpdateCalories();
            }
        });

        // دالة تحميل الخطط المحفوظة
        function loadSavedPlans() {
            var savedPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            var container = document.getElementById('saved-plans-container');

            if (savedPlans.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 50px; color: #666;"><h3>📝 لا توجد خطط محفوظة</h3><p>ابدأ بإنشاء خطة غذائية جديدة</p></div>';
                return;
            }

            var html = '';
            for (var i = savedPlans.length - 1; i >= 0; i--) {
                var plan = savedPlans[i];
                html += createPlanCard(plan, i);
            }

            container.innerHTML = html;
        }

        // دالة إنشاء بطاقة الخطة
        function createPlanCard(plan, index) {
            return `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; border-right: 5px solid #3498db;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #2c3e50; margin: 0;">👤 ${plan.patientName} (${plan.patientAge} سنة)</h4>
                        <div>
                            <button onclick="previewPlan('${plan.id}')" class="btn" style="background: #3498db; padding: 8px 15px; font-size: 12px; margin: 2px;">👁️ معاينة</button>
                            <button onclick="printPlan('${plan.id}')" class="btn" style="background: #17a2b8; padding: 8px 15px; font-size: 12px; margin: 2px;">🖨️ طباعة</button>
                            <button onclick="deletePlan(${index})" class="btn" style="background: #dc3545; padding: 8px 15px; font-size: 12px; margin: 2px;">🗑️ حذف</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div><strong>📅 التاريخ (ميلادي):</strong> ${plan.date}</div>
                        <div><strong>🕐 الوقت:</strong> ${plan.time}</div>
                        <div><strong>🔥 إجمالي السعرات:</strong> ${plan.totalCalories} سعرة</div>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <strong>📋 ملخص الوجبات:</strong><br>
                        <div style="margin-top: 10px; line-height: 1.8;">
                            ${plan.meals.breakfast.summary ? '🌅 الإفطار: ' + plan.meals.breakfast.summary + '<br>' : ''}
                            ${plan.meals.morningSnack.summary ? '☕ وجبة خفيفة صباحية: ' + plan.meals.morningSnack.summary + '<br>' : ''}
                            ${plan.meals.lunch.summary ? '🍽️ الغداء: ' + plan.meals.lunch.summary + '<br>' : ''}
                            ${plan.meals.eveningSnack.summary ? '🍎 وجبة خفيفة مسائية: ' + plan.meals.eveningSnack.summary + '<br>' : ''}
                            ${plan.meals.dinner.summary ? '🌙 العشاء: ' + plan.meals.dinner.summary : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // دالة حذف خطة
        function deletePlan(index) {
            if (confirm('هل أنت متأكد من حذف هذه الخطة؟')) {
                var savedPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
                savedPlans.splice(index, 1);
                localStorage.setItem('nutritionPlans', JSON.stringify(savedPlans));
                loadSavedPlans();
                alert('✅ تم حذف الخطة بنجاح');
            }
        }

        // دالة مسح جميع الخطط
        function clearAllSavedPlans() {
            if (confirm('هل أنت متأكد من حذف جميع الخطط المحفوظة؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('nutritionPlans');
                loadSavedPlans();
                alert('✅ تم حذف جميع الخطط بنجاح');
            }
        }

        // دالة طباعة خطة محددة
        function printPlan(planId) {
            var savedPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            var plan = savedPlans.find(function(p) { return p.id === planId; });

            if (plan) {
                var printWindow = window.open('', '_blank');
                printWindow.document.write(generatePrintableHTML(plan));
                printWindow.document.close();
                printWindow.print();
            }
        }

        // دالة إنشاء HTML للطباعة
        function generatePrintableHTML(plan) {
            return `
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>خطة غذائية - ${plan.patientName}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }
                        .patient-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                        .meal-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                        .meal-title { color: #2c3e50; border-bottom: 1px solid #3498db; padding-bottom: 5px; }
                        .total-calories { background: #e7f3ff; padding: 15px; border-radius: 8px; text-align: center; font-size: 18px; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div>
                                <h1 style="margin: 0; color: #2c3e50;">🏥 عيادة الدكتور أحمد محمد علي</h1>
                                <p style="margin: 5px 0 0 0; color: #7f8c8d;">أخصائي الطب الباطني والتغذية العلاجية</p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #95a5a6;">📍 شارع الملك فهد، الرياض | 📞 ************</p>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 14px; color: #2c3e50; margin-bottom: 5px;">رقم الترخيص: 12345</div>
                                <div style="font-size: 12px; color: #7f8c8d;">تاريخ الطباعة: ${formatDateInArabic(new Date())}</div>
                            </div>
                        </div>
                        <h2 style="text-align: center; color: #3498db; margin: 20px 0; border-bottom: 2px solid #3498db; padding-bottom: 10px;">خطة غذائية مخصصة</h2>
                    </div>

                    <div class="patient-info">
                        <h3>👤 بيانات المريض</h3>
                        <p><strong>الاسم:</strong> ${plan.patientName}</p>
                        <p><strong>العمر:</strong> ${plan.patientAge} سنة</p>
                        <p><strong>تاريخ الخطة (ميلادي):</strong> ${plan.date}</p>
                        <p><strong>وقت الإنشاء:</strong> ${plan.time}</p>
                    </div>

                    ' + (plan.meals.breakfast.summary ? '<div class="meal-section"><h4 class="meal-title">🌅 الإفطار (' + plan.meals.breakfast.totalCalories + ' سعرة)</h4><p>' + plan.meals.breakfast.summary + '</p></div>' : '') + '

                    ' + (plan.meals.morningSnack.summary ? '<div class="meal-section"><h4 class="meal-title">☕ وجبة خفيفة صباحية (' + plan.meals.morningSnack.totalCalories + ' سعرة)</h4><p>' + plan.meals.morningSnack.summary + '</p></div>' : '') + '

                    ' + (plan.meals.lunch.summary ? '<div class="meal-section"><h4 class="meal-title">🍽️ الغداء (' + plan.meals.lunch.totalCalories + ' سعرة)</h4><p>' + plan.meals.lunch.summary + '</p></div>' : '') + '

                    ' + (plan.meals.eveningSnack.summary ? '<div class="meal-section"><h4 class="meal-title">🍎 وجبة خفيفة مسائية (' + plan.meals.eveningSnack.totalCalories + ' سعرة)</h4><p>' + plan.meals.eveningSnack.summary + '</p></div>' : '') + '

                    ' + (plan.meals.dinner.summary ? '<div class="meal-section"><h4 class="meal-title">🌙 العشاء (' + plan.meals.dinner.totalCalories + ' سعرة)</h4><p>' + plan.meals.dinner.summary + '</p></div>' : '') + '

                    <div class="total-calories">
                        🔥 إجمالي السعرات اليومية: ${plan.totalCalories} سعرة حرارية
                    </div>

                    ' + (plan.notes ? '<div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px;"><h4>💡 نصائح وتعليمات:</h4><p>' + plan.notes + '</p></div>' : '') + '

                    <div style="text-align: center; margin-top: 30px; border-top: 1px solid #ddd; padding-top: 20px;">
                        <p><strong>توقيع الطبيب:</strong> _______________</p>
                        <p style="font-size: 12px; color: #666;">تاريخ الطباعة (ميلادي): ${formatDateInArabic(new Date())}</p>
                    </div>
                </body>
                </html>
            `;
        }

        // متغير لحفظ الخطة الحالية للمعاينة
        var currentPreviewPlan = null;

        // دالة معاينة الخطة
        function previewPlan(planId) {
            var savedPlans = JSON.parse(localStorage.getItem('nutritionPlans') || '[]');
            var plan = savedPlans.find(function(p) { return p.id === planId; });

            if (plan) {
                currentPreviewPlan = plan;
                displayPlanPreview(plan);
                window.location.hash = '#plan-preview';
            } else {
                alert('❌ لم يتم العثور على الخطة المطلوبة');
            }
        }

        // دالة عرض معاينة الخطة
        function displayPlanPreview(plan) {
            var previewContent = document.getElementById('preview-content');

            var html = `
                <div style="border: 2px solid #3498db; border-radius: 15px; padding: 30px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                    <!-- رأس الخطة -->
                    <div style="border-bottom: 3px solid #3498db; padding-bottom: 25px; margin-bottom: 30px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <span style="font-size: 24px; color: white;">🏥</span>
                                </div>
                                <div>
                                    <h2 style="color: #2c3e50; margin: 0 0 5px 0; font-size: 24px;">عيادة الدكتور أحمد محمد علي</h2>
                                    <p style="color: #7f8c8d; font-size: 14px; margin: 0;">أخصائي الطب الباطني والتغذية العلاجية</p>
                                    <p style="color: #95a5a6; font-size: 12px; margin: 5px 0 0 0;">📍 شارع الملك فهد، الرياض | 📞 ************</p>
                                </div>
                            </div>
                            <div style="text-align: right; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 14px; color: #2c3e50; margin-bottom: 5px;">👨‍⚕️ د. أحمد محمد علي</div>
                                <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 3px;">رقم الترخيص: 12345</div>
                                <div style="font-size: 12px; color: #3498db;">معاينة قبل الطباعة</div>
                            </div>
                        </div>
                        <h3 style="text-align: center; color: #3498db; margin: 0; font-size: 22px;">📋 خطة غذائية مخصصة</h3>
                    </div>

                    <!-- بيانات المريض -->
                    <div style="background: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; margin: 0 0 20px 0; border-bottom: 2px solid #3498db; padding-bottom: 10px;">👤 بيانات المريض</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div><strong style="color: #34495e;">الاسم:</strong> <span style="color: #2c3e50;">${plan.patientName}</span></div>
                            <div><strong style="color: #34495e;">العمر:</strong> <span style="color: #2c3e50;">${plan.patientAge} سنة</span></div>
                            <div><strong style="color: #34495e;">تاريخ الخطة:</strong> <span style="color: #2c3e50;">${plan.date}</span></div>
                            <div><strong style="color: #34495e;">وقت الإنشاء:</strong> <span style="color: #2c3e50;">${plan.time}</span></div>
                        </div>
                    </div>

                    <!-- إجمالي السعرات -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        <h3 style="margin: 0 0 10px 0; font-size: 22px;">🔥 إجمالي السعرات اليومية</h3>
                        <div style="font-size: 32px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">${plan.totalCalories} سعرة حرارية</div>
                    </div>

                    <!-- الوجبات -->
                    <div style="display: grid; gap: 20px;">
                        ${generateMealPreview('🌅 الإفطار', plan.meals.breakfast)}
                        ${generateMealPreview('☕ وجبة خفيفة صباحية', plan.meals.morningSnack)}
                        ${generateMealPreview('🍽️ الغداء', plan.meals.lunch)}
                        ${generateMealPreview('🍎 وجبة خفيفة مسائية', plan.meals.eveningSnack)}
                        ${generateMealPreview('🌙 العشاء', plan.meals.dinner)}
                    </div>

                    ${plan.notes ? `
                    <div style="background: #fff3cd; padding: 20px; border-radius: 12px; margin-top: 25px; border-right: 5px solid #ffc107;">
                        <h4 style="color: #856404; margin: 0 0 15px 0;">💡 نصائح وتعليمات خاصة:</h4>
                        <p style="color: #856404; margin: 0; line-height: 1.6;">${plan.notes}</p>
                    </div>
                    ` : ''}

                    <!-- توقيع الطبيب -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #bdc3c7;">
                        <div style="display: inline-block; border-bottom: 2px solid #34495e; padding: 10px 50px; margin: 20px;">
                            <strong style="color: #2c3e50;">توقيع الطبيب</strong>
                        </div>
                        <p style="font-size: 14px; color: #7f8c8d; margin: 20px 0 0 0;">تاريخ المعاينة: ${formatDateInArabic(new Date())}</p>
                    </div>
                </div>
            `;

            previewContent.innerHTML = html;
        }

        // دالة إنشاء معاينة الوجبة
        function generateMealPreview(mealTitle, mealData) {
            if (!mealData.summary || mealData.summary === 'لم يتم اختيار أي أطعمة' || mealData.summary === 'لم يتم اختيار أي وجبات خفيفة') {
                return '';
            }

            return `
                <div style="background: white; padding: 20px; border-radius: 12px; border-right: 5px solid #3498db; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                    <h4 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 18px;">${mealTitle} (${mealData.totalCalories} سعرة)</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <p style="margin: 0; color: #495057; line-height: 1.6; font-size: 16px;">${mealData.summary}</p>
                    </div>
                    ${mealData.foods && mealData.foods.length > 0 ? `
                    <div style="margin-top: 15px;">
                        <strong style="color: #6c757d; font-size: 14px;">تفاصيل الأطعمة:</strong>
                        <ul style="margin: 10px 0 0 20px; color: #6c757d; font-size: 14px;">
                            ' + mealData.foods.map(function(food) { return '<li>' + food.details + '</li>'; }).join('') + '
                        </ul>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // دالة طباعة المعاينة الحالية
        function printCurrentPreview() {
            if (currentPreviewPlan) {
                var printWindow = window.open('', '_blank');
                printWindow.document.write(generatePrintableHTML(currentPreviewPlan));
                printWindow.document.close();
                printWindow.print();
            } else {
                alert('❌ لا توجد خطة للطباعة');
            }
        }

        // دالة تعديل الخطة الحالية
        function editCurrentPlan() {
            if (currentPreviewPlan) {
                // يمكن إضافة منطق تعديل الخطة هنا لاحقاً
                alert('🔧 ميزة التعديل ستكون متاحة قريباً!\nيمكنك إنشاء خطة جديدة في الوقت الحالي.');
                window.location.hash = '#create-nutrition-plan';
            } else {
                alert('❌ لا توجد خطة للتعديل');
            }
        }

        // دالة العودة الذكية من المعاينة
        function goBackFromPreview() {
            if (currentPreviewPlan && currentPreviewPlan.id === 'temp_preview') {
                // إذا كانت معاينة خطة مؤقتة، العودة لصفحة الإنشاء
                window.location.hash = '#create-nutrition-plan';
            } else {
                // إذا كانت معاينة خطة محفوظة، العودة للخطط المحفوظة
                window.location.hash = '#saved-plans';
            }
        }

        // تحميل الخطط المحفوظة عند فتح الصفحة
        window.addEventListener('hashchange', function() {
            if (window.location.hash === '#saved-plans') {
                loadSavedPlans();
            }
        });

        // دالة العودة للصفحة السابقة
        var navigationHistory = ['dashboard']; // تتبع تاريخ التنقل

        function goBack() {
            console.log('🔙 محاولة العودة للصفحة السابقة');
            console.log('📋 تاريخ التنقل الحالي:', navigationHistory);

            if (navigationHistory.length > 1) {
                // إزالة الصفحة الحالية
                navigationHistory.pop();
                // الحصول على الصفحة السابقة
                var previousPage = navigationHistory[navigationHistory.length - 1];
                console.log('⬅️ العودة إلى:', previousPage);

                // الانتقال للصفحة السابقة
                showPage(previousPage);
            } else {
                // إذا لم يكن هناك تاريخ، العودة للوحة التحكم
                console.log('🏠 العودة للوحة التحكم (افتراضي)');
                showPage('dashboard');
            }
        }

        // تحديث دالة showPage لتتبع التنقل
        var originalShowPage = showPage;
        function showPage(pageId) {
            console.log('📄 الانتقال إلى صفحة:', pageId);

            // إضافة الصفحة لتاريخ التنقل إذا لم تكن موجودة في النهاية
            if (navigationHistory[navigationHistory.length - 1] !== pageId) {
                navigationHistory.push(pageId);

                // الاحتفاظ بآخر 10 صفحات فقط لتوفير الذاكرة
                if (navigationHistory.length > 10) {
                    navigationHistory.shift();
                }
            }

            console.log('📋 تاريخ التنقل المحدث:', navigationHistory);

            // استدعاء الدالة الأصلية
            return originalShowPage(pageId);
        }

        // دالة العودة للوحة التحكم مباشرة
        function goHome() {
            console.log('🏠 العودة للوحة التحكم');
            navigationHistory = ['dashboard'];
            showPage('dashboard');
        }

        // دالة مسح تاريخ التنقل
        function clearNavigationHistory() {
            console.log('🗑️ مسح تاريخ التنقل');
            navigationHistory = ['dashboard'];
        }

        // دوال إدارة المواعيد والواتساب

        // دالة عرض المواعيد في الجدول
        function displayAppointments() {
            console.log('🔍 محاولة عرض المواعيد...');

            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            console.log('📋 المواعيد المحفوظة:', appointments);

            var today = new Date().toISOString().split('T')[0];
            console.log('📅 تاريخ اليوم:', today);

            var todayAppointments = appointments.filter(function(apt) {
                return apt.date === today;
            });
            console.log('📋 مواعيد اليوم:', todayAppointments);

            // البحث عن عنصر الجدول بطرق متعددة
            var tableBody = document.getElementById('appointments-table-body') ||
                           document.querySelector('#appointments-table-body') ||
                           document.querySelector('tbody[id="appointments-table-body"]');

            console.log('🔍 عنصر الجدول:', tableBody);

            if (!tableBody) {
                console.log('❌ لم يتم العثور على عنصر الجدول - محاولة إنشاؤه');

                // محاولة العثور على الجدول الأساسي وإنشاء tbody
                var table = document.querySelector('#appointments table');
                if (table) {
                    var tbody = table.querySelector('tbody');
                    if (!tbody) {
                        tbody = document.createElement('tbody');
                        tbody.id = 'appointments-table-body';
                        table.appendChild(tbody);
                        tableBody = tbody;
                        console.log('✅ تم إنشاء عنصر tbody جديد');
                    } else {
                        tbody.id = 'appointments-table-body';
                        tableBody = tbody;
                        console.log('✅ تم العثور على tbody وإضافة ID');
                    }
                } else {
                    console.log('❌ لم يتم العثور على الجدول الأساسي');
                    return;
                }
            }

            if (todayAppointments.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="9" style="padding: 30px; text-align: center; color: #666;">
                            📅 لا توجد مواعيد محجوزة لليوم<br>
                            <small>احجز موعد جديد لرؤية البيانات هنا</small>
                        </td>
                    </tr>
                `;
                return;
            }

            // ترتيب المواعيد حسب الوقت
            todayAppointments.sort(function(a, b) {
                return a.time.localeCompare(b.time);
            });

            var html = '';
            todayAppointments.forEach(function(apt) {
                var ageGender = '';
                if (apt.age && apt.gender) {
                    ageGender = apt.age + ' سنة / ' + apt.gender;
                } else if (apt.age) {
                    ageGender = apt.age + ' سنة';
                } else if (apt.gender) {
                    ageGender = apt.gender;
                } else {
                    ageGender = 'غير محدد';
                }

                var education = apt.education ? '🎓 ' + apt.education : '';
                var occupation = apt.occupation || 'غير محدد';
                var maritalStatus = apt.maritalStatus ? ' (' + apt.maritalStatus + ')' : '';

                // تحضير بيانات القياسات الجسمية
                var measurements = '';
                if (apt.height && apt.weight) {
                    measurements = '📏 ' + apt.height + ' سم<br>⚖️ ' + apt.weight + ' كجم';
                    if (apt.bmi && apt.bmiCategory) {
                        var bmiColor = '';
                        if (apt.bmiCategory === 'نقص الوزن') bmiColor = '#007bff';
                        else if (apt.bmiCategory === 'وزن طبيعي') bmiColor = '#28a745';
                        else if (apt.bmiCategory === 'زيادة وزن') bmiColor = '#ffc107';
                        else if (apt.bmiCategory === 'سمنة') bmiColor = '#dc3545';

                        measurements += '<br><span style="color: ' + bmiColor + '; font-weight: bold;">📊 ' + apt.bmi + '</span>';
                        measurements += '<br><small style="color: ' + bmiColor + ';">' + apt.bmiCategory + '</small>';
                    }
                } else {
                    measurements = '<small style="color: #999;">غير محدد</small>';
                }

                // تحديد حالة الموعد
                var statusClass = 'background: #28a745;'; // مؤكد (افتراضي)
                var statusText = 'مؤكد';

                var currentTime = new Date();
                var appointmentDateTime = new Date(apt.date + 'T' + apt.time);

                if (appointmentDateTime < currentTime) {
                    statusClass = 'background: #dc3545;';
                    statusText = 'متأخر';
                } else if (appointmentDateTime - currentTime < 3600000) { // أقل من ساعة
                    statusClass = 'background: #ffc107; color: #212529;';
                    statusText = 'قريب';
                }

                html += '<tr style="border-bottom: 1px solid #e9ecef;">' +
                    '<td style="padding: 15px; text-align: right; font-weight: bold;">' + formatTime(apt.time) + '</td>' +
                    '<td style="padding: 15px; text-align: right;">' +
                        '<div style="font-weight: bold;">' + apt.patientName + '</div>' +
                        (education ? '<div style="font-size: 12px; color: #666;">' + education + '</div>' : '') +
                        (maritalStatus ? '<div style="font-size: 11px; color: #999;">' + maritalStatus + '</div>' : '') +
                    '</td>' +
                    '<td style="padding: 15px; text-align: right;">' + ageGender + '</td>' +
                    '<td style="padding: 15px; text-align: right; font-size: 12px;">' + measurements + '</td>' +
                    '<td style="padding: 15px; text-align: right;">' + occupation + '</td>' +
                    '<td style="padding: 15px; text-align: right; direction: ltr; text-align: left;">' + formatPhoneNumber(apt.phoneNumber) + '</td>' +
                    '<td style="padding: 15px; text-align: right;">' + apt.visitType + '</td>' +
                    '<td style="padding: 15px; text-align: center;">' +
                        '<span style="' + statusClass + '; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">' + statusText + '</span>' +
                    '</td>' +
                    '<td style="padding: 15px; text-align: center;">' +
                        '<button onclick="sendWhatsAppReminder(\'' + apt.phoneNumber + '\', \'' + apt.patientName + '\', \'' + formatTime(apt.time) + '\')" class="btn" style="background: #25d366; padding: 5px 10px; font-size: 12px; margin: 2px;">📱 تذكير</button>' +
                        '<button onclick="editAppointment(\'' + apt.id + '\')" class="btn" style="background: #f39c12; padding: 5px 10px; font-size: 12px; margin: 2px;">✏️ تعديل</button>' +
                        '<button onclick="deleteAppointment(\'' + apt.id + '\')" class="btn" style="background: #dc3545; padding: 5px 10px; font-size: 12px; margin: 2px;">🗑️ حذف</button>' +
                    '</td>' +
                '</tr>';
            });

            console.log('✅ تحديث محتوى الجدول:', html.length > 0 ? 'تم' : 'فارغ');
            tableBody.innerHTML = html;
            console.log('✅ تم تحديث الجدول بنجاح');
        }

        // دالة تنسيق الوقت
        function formatTime(time24) {
            var parts = time24.split(':');
            var hours = parseInt(parts[0]);
            var minutes = parts[1];

            // عرض الوقت بنظام 24 ساعة مع التوضيح
            var period = '';
            var displayHours = hours;

            if (hours === 0) {
                period = ' (12:' + minutes + ' ص)';
                displayHours = '00';
            } else if (hours < 6) {
                period = ' (' + hours + ':' + minutes + ' ص - ليلاً)';
                displayHours = hours.toString().padStart(2, '0');
            } else if (hours < 12) {
                period = ' (' + hours + ':' + minutes + ' ص)';
                displayHours = hours.toString().padStart(2, '0');
            } else if (hours === 12) {
                period = ' (12:' + minutes + ' م)';
                displayHours = '12';
            } else {
                period = ' (' + (hours - 12) + ':' + minutes + ' م)';
                displayHours = hours.toString().padStart(2, '0');
            }

            return displayHours + ':' + minutes + period;
        }

        // دالة تنسيق الوقت للعرض المبسط
        function formatTimeSimple(time24) {
            var parts = time24.split(':');
            var hours = parseInt(parts[0]);
            var minutes = parts[1];

            if (hours === 0) {
                return '12:' + minutes + ' ص';
            } else if (hours < 12) {
                return hours + ':' + minutes + ' ص';
            } else if (hours === 12) {
                return '12:' + minutes + ' م';
            } else {
                return (hours - 12) + ':' + minutes + ' م';
            }
        }

        // دالة تحديث المواعيد
        function refreshAppointments() {
            displayAppointments();
            updateAppointmentStats();
            alert('✅ تم تحديث قائمة المواعيد');
        }

        // دالة حذف موعد
        function deleteAppointment(appointmentId) {
            if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
                var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
                appointments = appointments.filter(function(apt) {
                    return apt.id !== appointmentId;
                });
                localStorage.setItem('appointments', JSON.stringify(appointments));
                displayAppointments();
                updateAppointmentStats();
                alert('✅ تم حذف الموعد بنجاح');
            }
        }

        // دالة تحديث الإحصائيات
        function updateAppointmentStats() {
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            var today = new Date().toISOString().split('T')[0];
            var currentTime = new Date();

            var todayAppointments = appointments.filter(function(apt) {
                return apt.date === today;
            });

            var lateAppointments = todayAppointments.filter(function(apt) {
                var appointmentDateTime = new Date(apt.date + 'T' + apt.time);
                return appointmentDateTime < currentTime;
            });

            // تحديث الأرقام في البطاقات (إذا وجدت)
            var todayCountElement = document.querySelector('.cards .card:first-child .card-number');
            if (todayCountElement) {
                todayCountElement.textContent = todayAppointments.length;
            }
        }

        // دالة إرسال تذكير واتساب
        function sendWhatsAppReminder(phoneNumber, patientName, appointmentTime) {
            // استخدام رقم الهاتف كما هو مُدخل (مع كود البلد)
            var cleanPhone = phoneNumber.replace(/[^\d+]/g, ''); // إزالة المسافات والرموز فقط، الاحتفاظ بـ +

            // إنشاء رسالة التذكير
            var message = '🏥 *عيادة الدكتور أحمد محمد علي*\n\n' +
                'مرحباً ' + patientName + '،\n\n' +
                '🔔 تذكير بموعدك:\n' +
                '📅 التاريخ: ' + formatDateInArabic(new Date()) + '\n' +
                '🕐 الوقت: ' + appointmentTime + '\n' +
                '👨‍⚕️ الطبيب: د. أحمد محمد علي\n\n' +
                '📍 العنوان: شارع الملك فهد، الرياض\n' +
                '📞 للاستفسار: ************\n\n' +
                '⚠️ يرجى الحضور قبل 15 دقيقة من الموعد\n' +
                '💡 في حالة عدم التمكن من الحضور، يرجى الاتصال لإعادة الجدولة\n\n' +
                'شكراً لثقتكم بنا 🙏';

            // ترميز الرسالة للـ URL
            var encodedMessage = encodeURIComponent(message);

            // إنشاء رابط الواتساب
            var whatsappUrl = 'https://wa.me/' + cleanPhone + '?text=' + encodedMessage;

            // فتح الواتساب
            window.open(whatsappUrl, '_blank');

            // تسجيل إرسال التذكير
            logWhatsAppReminder(phoneNumber, patientName, appointmentTime);

            alert('✅ تم فتح واتساب لإرسال تذكير إلى ' + patientName);
        }

        // دالة اختبار الواتساب
        function sendTestWhatsApp() {
            var phoneNumber = document.getElementById('patient-phone').value;
            var patientName = document.getElementById('patient-name').value;

            if (!phoneNumber || !patientName) {
                alert('يرجى إدخال اسم المريض ورقم الهاتف أولاً');
                return;
            }

            sendWhatsAppReminder(phoneNumber, patientName, 'اختبار');
        }

        // دالة اختبار إعدادات العيادة
        function testClinicSettings() {
            console.log('🔧 اختبار إعدادات العيادة');

            var settings = getSafeClinicSettings();

            var settingsInfo = `🔧 إعدادات العيادة الحالية:

👨‍⚕️ اسم الطبيب: ${settings.doctorName}
🏥 اسم العيادة: ${settings.clinicName}
🩺 التخصص: ${settings.specialty}
📄 رقم الترخيص: ${settings.licenseNumber}
📍 العنوان: ${settings.address}
📞 الهاتف: ${settings.phone}
📧 البريد: ${settings.email}
🎨 الشعار: ${settings.logo}
🖼️ نوع الشعار: ${settings.logoType}

${settings.logoImage ? '✅ يوجد صورة شعار مخصصة' : '❌ لا توجد صورة شعار'}`;

            alert(settingsInfo);

            console.log('📋 إعدادات العيادة المفصلة:', settings);
        }

        // دالة حفظ الموعد
        function saveAppointment(appointmentData) {
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            appointmentData.id = 'apt_' + Date.now();
            appointmentData.createdAt = new Date().toISOString();
            appointmentData.status = 'مؤكد';

            appointments.push(appointmentData);
            localStorage.setItem('appointments', JSON.stringify(appointments));

            return appointmentData;
        }

        // دالة تسجيل إرسال التذكير
        function logWhatsAppReminder(phoneNumber, patientName, appointmentTime) {
            var reminders = JSON.parse(localStorage.getItem('whatsappReminders') || '[]');
            var reminder = {
                id: 'rem_' + Date.now(),
                phoneNumber: phoneNumber,
                patientName: patientName,
                appointmentTime: appointmentTime,
                sentAt: new Date().toISOString(),
                sentDate: formatDateInArabic(new Date())
            };

            reminders.push(reminder);
            localStorage.setItem('whatsappReminders', JSON.stringify(reminders));
        }

        // دالة إرسال تأكيد الحجز
        function sendBookingConfirmation(appointmentData) {
            var patientInfo = '';
            if (appointmentData.age) patientInfo += '👤 العمر: ' + appointmentData.age + ' سنة\n';
            if (appointmentData.gender) patientInfo += '⚧ الجنس: ' + appointmentData.gender + '\n';
            if (appointmentData.occupation) patientInfo += '💼 الوظيفة: ' + appointmentData.occupation + '\n';
            if (appointmentData.education) patientInfo += '🎓 المؤهل: ' + appointmentData.education + '\n';

            // إضافة القياسات الجسمية
            var bodyMeasurements = '';
            if (appointmentData.height && appointmentData.weight) {
                bodyMeasurements += '📏 الطول: ' + appointmentData.height + ' سم\n';
                bodyMeasurements += '⚖️ الوزن: ' + appointmentData.weight + ' كجم\n';
                if (appointmentData.bmi) {
                    bodyMeasurements += '📊 مؤشر كتلة الجسم: ' + appointmentData.bmi;
                    if (appointmentData.bmiCategory) {
                        bodyMeasurements += ' (' + appointmentData.bmiCategory + ')';
                    }
                    bodyMeasurements += '\n';

                    // إضافة نصيحة حسب مؤشر كتلة الجسم
                    var bmiValue = parseFloat(appointmentData.bmi);
                    if (bmiValue) {
                        bodyMeasurements += '💡 ' + getBMIAdvice(bmiValue) + '\n';
                    }
                }
            }

            // إضافة التاريخ الطبي المهم
            var medicalInfo = '';
            if (appointmentData.medicalHistory) {
                var mh = appointmentData.medicalHistory;

                // الحساسية
                if (mh.allergies && mh.allergies.answer === 'نعم') {
                    medicalInfo += '🤧 حساسية: ' + (mh.allergies.details || 'نعم') + '\n';
                }

                // الأدوية
                if (mh.medications && mh.medications.answer === 'نعم') {
                    medicalInfo += '💊 أدوية حالية: ' + (mh.medications.details || 'نعم') + '\n';
                }

                // الأمراض المزمنة
                if (mh['chronic-diseases'] && mh['chronic-diseases'].answer === 'نعم') {
                    medicalInfo += '🩺 أمراض مزمنة: ' + (mh['chronic-diseases'].details || 'نعم') + '\n';
                }

                // الحمل
                if (mh.pregnancy && mh.pregnancy.answer === 'نعم') {
                    medicalInfo += '🤱 حامل: ' + (mh.pregnancy.details || 'نعم') + '\n';
                }

                // الرضاعة
                if (mh.breastfeeding && mh.breastfeeding.answer === 'نعم') {
                    medicalInfo += `🍼 رضاعة طبيعية: نعم\n`;
                }
            }

            var message = `🏥 *عيادة الدكتور أحمد محمد علي*

مرحباً ${appointmentData.patientName}،

✅ تم تأكيد حجز موعدك بنجاح:

📅 التاريخ: ${appointmentData.date}
🕐 الوقت: ${appointmentData.time}
🏥 نوع الزيارة: ${appointmentData.visitType}
👨‍⚕️ الطبيب: د. أحمد محمد علي

${patientInfo ? '📋 *بياناتك المسجلة:*\n' + patientInfo : ''}${bodyMeasurements ? '\n📏 *القياسات الجسمية:*\n' + bodyMeasurements : ''}${medicalInfo ? '\n🏥 *معلومات طبية مهمة:*\n' + medicalInfo : ''}
📍 العنوان: شارع الملك فهد، الرياض
📞 للاستفسار: ************

💡 نصائح مهمة:
• احضر قبل 15 دقيقة من الموعد
• أحضر معك الهوية الشخصية
• أحضر التقارير الطبية السابقة (إن وجدت)
${medicalInfo ? '• أحضر قائمة بالأدوية الحالية\n• أحضر تقارير الحساسية (إن وجدت)' : ''}

شكراً لثقتكم بنا 🙏`;

            // استخدام رقم الهاتف كما هو مُدخل (مع كود البلد)
            var cleanPhone = appointmentData.phoneNumber.replace(/[^\d+]/g, ''); // إزالة المسافات والرموز فقط

            var encodedMessage = encodeURIComponent(message);
            var whatsappUrl = 'https://wa.me/' + cleanPhone + '?text=' + encodedMessage;

            window.open(whatsappUrl, '_blank');
        }

        // دالة التحقق من صحة رقم الهاتف
        function validatePhoneNumber(phoneNumber) {
            // إزالة المسافات والرموز
            var cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

            // التحقق من وجود كود البلد
            if (cleanPhone.startsWith('+')) {
                // رقم بصيغة +966xxxxxxxxx
                return cleanPhone.length >= 12 && cleanPhone.length <= 15;
            } else if (cleanPhone.match(/^[1-9]\d{10,14}$/)) {
                // رقم بصيغة 966xxxxxxxxx (بدون +)
                return true;
            }

            return false;
        }

        // دالة تنسيق رقم الهاتف للعرض
        function formatPhoneNumber(phoneNumber) {
            var cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

            if (cleanPhone.startsWith('+966')) {
                // تنسيق الرقم السعودي: +966 50 123 4567
                return cleanPhone.replace(/(\+966)(\d{2})(\d{3})(\d{4})/, '$1 $2 $3 $4');
            } else if (cleanPhone.startsWith('966')) {
                // تنسيق الرقم السعودي: 966 50 123 4567
                return cleanPhone.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '$1 $2 $3 $4');
            }

            return phoneNumber; // إرجاع الرقم كما هو إذا لم يكن سعودي
        }

        // دالة حساب مؤشر كتلة الجسم
        function calculateBMI() {
            var height = parseFloat(document.getElementById('patient-height').value);
            var weight = parseFloat(document.getElementById('patient-weight').value);
            var bmiField = document.getElementById('patient-bmi');
            var categoryField = document.getElementById('bmi-category');

            if (height && weight && height > 0 && weight > 0) {
                // تحويل الطول من سم إلى متر
                var heightInMeters = height / 100;

                // حساب مؤشر كتلة الجسم
                var bmi = weight / (heightInMeters * heightInMeters);

                // عرض النتيجة مع رقمين عشريين
                bmiField.value = bmi.toFixed(1);

                // تحديد التصنيف واللون
                var category = '';
                var color = '';

                if (bmi < 18.5) {
                    category = 'نقص الوزن';
                    color = '#007bff';
                } else if (bmi >= 18.5 && bmi < 25) {
                    category = 'وزن طبيعي';
                    color = '#28a745';
                } else if (bmi >= 25 && bmi < 30) {
                    category = 'زيادة وزن';
                    color = '#ffc107';
                } else {
                    category = 'سمنة';
                    color = '#dc3545';
                }

                categoryField.value = category;
                categoryField.style.color = color;
                categoryField.style.fontWeight = 'bold';

                // تغيير لون حقل مؤشر كتلة الجسم حسب التصنيف
                bmiField.style.color = color;
                bmiField.style.fontWeight = 'bold';

            } else {
                // مسح القيم إذا كانت البيانات غير مكتملة
                bmiField.value = '';
                categoryField.value = '';
                bmiField.style.color = '#6c757d';
                categoryField.style.color = '#6c757d';
            }
        }

        // دالة الحصول على نصائح مؤشر كتلة الجسم
        function getBMIAdvice(bmi) {
            if (bmi < 18.5) {
                return 'يُنصح بزيادة الوزن تدريجياً من خلال نظام غذائي صحي ومتوازن';
            } else if (bmi >= 18.5 && bmi < 25) {
                return 'وزنك مثالي! حافظ على نمط حياة صحي ونشاط بدني منتظم';
            } else if (bmi >= 25 && bmi < 30) {
                return 'يُنصح بفقدان الوزن تدريجياً من خلال نظام غذائي متوازن وممارسة الرياضة';
            } else {
                return 'يُنصح بشدة بفقدان الوزن تحت إشراف طبي مع نظام غذائي مناسب وممارسة الرياضة';
            }
        }

        // دالة إظهار/إخفاء تفاصيل الحالات المرضية
        function toggleDetails(detailsId, show) {
            var detailsElement = document.getElementById(detailsId);
            if (detailsElement) {
                detailsElement.style.display = show ? 'block' : 'none';

                // إضافة required للحقول المطلوبة
                var inputs = detailsElement.querySelectorAll('input, textarea');
                inputs.forEach(function(input) {
                    if (show) {
                        input.required = true;
                    } else {
                        input.required = false;
                        input.value = '';
                    }
                });
            }
        }

        // دالة جمع بيانات الحالات المرضية
        function collectMedicalHistory() {
            var medicalHistory = {};

            // جمع الإجابات
            var questions = [
                'allergies', 'medications', 'surgeries', 'chronic-diseases',
                'exercise', 'pregnancy', 'breastfeeding', 'smoking',
                'alcohol', 'previous-diets', 'eating-disorders'
            ];

            questions.forEach(function(question) {
                var selectedRadio = document.querySelector('input[name="' + question + '"]:checked');
                if (selectedRadio) {
                    medicalHistory[question] = {
                        answer: selectedRadio.value,
                        details: ''
                    };

                    // جمع التفاصيل إذا كانت موجودة
                    var detailsElement = document.getElementById(question + '-details');
                    if (detailsElement && detailsElement.style.display !== 'none') {
                        var detailInput = detailsElement.querySelector('input, textarea');
                        if (detailInput) {
                            medicalHistory[question].details = detailInput.value;
                        }
                    }
                }
            });

            // إضافة الملاحظات الطبية
            var medicalNotes = document.getElementById('medical-notes');
            if (medicalNotes) {
                medicalHistory.additionalNotes = medicalNotes.value;
            }

            return medicalHistory;
        }

        // دالة تنسيق التاريخ الطبي للعرض
        function formatMedicalHistory(medicalHistory) {
            var formatted = '';

            var questionLabels = {
                'allergies': '🤧 الحساسية',
                'medications': '💊 الأدوية الحالية',
                'surgeries': '🏥 العمليات الجراحية',
                'chronic-diseases': '🩺 الأمراض المزمنة',
                'exercise': '🏃‍♂️ النشاط البدني',
                'pregnancy': '🤱 الحمل',
                'breastfeeding': '🍼 الرضاعة',
                'smoking': '🚬 التدخين',
                'alcohol': '🍷 المشروبات الكحولية',
                'previous-diets': '📋 الخطط الغذائية السابقة',
                'eating-disorders': '🍽️ اضطرابات الأكل'
            };

            Object.keys(medicalHistory).forEach(function(key) {
                if (key !== 'additionalNotes' && medicalHistory[key]) {
                    var label = questionLabels[key] || key;
                    formatted += label + ': ' + medicalHistory[key].answer;
                    if (medicalHistory[key].details) {
                        formatted += ' (' + medicalHistory[key].details + ')';
                    }
                    formatted += '\n';
                }
            });

            if (medicalHistory.additionalNotes) {
                formatted += '\n📝 ملاحظات إضافية: ' + medicalHistory.additionalNotes;
            }

            return formatted;
        }

        // تم دمج معالج نموذج حجز الموعد مع DOMContentLoaded الرئيسي أعلاه

        // دوال إضافية لإدارة التذكيرات

        // دالة إرسال تذكيرات جماعية
        function sendBulkReminders() {
            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            var today = new Date().toISOString().split('T')[0];

            var todayAppointments = appointments.filter(function(apt) {
                return apt.date === today && apt.status !== 'ملغي';
            });

            if (todayAppointments.length === 0) {
                alert('لا توجد مواعيد اليوم لإرسال تذكيرات لها');
                return;
            }

            var confirmMessage = 'هل تريد إرسال تذكيرات لجميع مرضى اليوم؟\n\nعدد المواعيد: ' + todayAppointments.length;

            if (confirm(confirmMessage)) {
                todayAppointments.forEach(function(apt) {
                    setTimeout(function() {
                        sendWhatsAppReminder(apt.phoneNumber, apt.patientName, apt.time);
                    }, 1000); // تأخير ثانية واحدة بين كل رسالة
                });

                alert('✅ تم إرسال ' + todayAppointments.length + ' تذكير عبر واتساب');
            }
        }

        // دالة تحميل سجل التذكيرات
        function loadReminderHistory() {
            var reminders = JSON.parse(localStorage.getItem('whatsappReminders') || '[]');

            if (reminders.length === 0) {
                alert('لا يوجد سجل تذكيرات حتى الآن');
                return;
            }

            var historyHtml = '<h3>📋 سجل التذكيرات (' + reminders.length + ' رسالة)</h3>';
            historyHtml += '<div style="max-height: 400px; overflow-y: auto;">';

            reminders.reverse().forEach(function(reminder) {
                historyHtml += `
                    <div style="background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-right: 4px solid #25d366;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${reminder.patientName}</strong> - ${reminder.phoneNumber}<br>
                                <small style="color: #666;">موعد: ${reminder.appointmentTime} | تم الإرسال: ${reminder.sentDate}</small>
                            </div>
                            <span style="background: #25d366; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">✅ تم</span>
                        </div>
                    </div>
                `;
            });

            historyHtml += '</div>';

            // عرض النافذة المنبثقة
            var popup = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');
            var popupContent = '<html dir="rtl">' +
                '<head>' +
                '<title>سجل تذكيرات الواتساب</title>' +
                '<style>' +
                'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
                'h3 { color: #2c3e50; border-bottom: 2px solid #25d366; padding-bottom: 10px; }' +
                '</style>' +
                '</head>' +
                '<body>' + historyHtml + '</body>' +
                '</html>';
            popup.document.write(popupContent);
        }

        // دالة إعداد التذكيرات التلقائية
        function setupAutoReminders() {
            var settings = {
                autoReminder: confirm('هل تريد تفعيل التذكيرات التلقائية؟'),
                reminderTime: prompt('كم ساعة قبل الموعد تريد إرسال التذكير؟ (افتراضي: 2)', '2'),
                workingHours: {
                    start: prompt('ساعة بداية العمل (افتراضي: 09:00)', '09:00'),
                    end: prompt('ساعة نهاية العمل (افتراضي: 17:00)', '17:00')
                }
            };

            localStorage.setItem('reminderSettings', JSON.stringify(settings));

            alert(`✅ تم حفظ إعدادات التذكير:

• التذكيرات التلقائية: ${settings.autoReminder ? 'مفعلة' : 'معطلة'}
• وقت التذكير: ${settings.reminderTime} ساعة قبل الموعد
• ساعات العمل: من ${settings.workingHours.start} إلى ${settings.workingHours.end}`);
        }

        // دالة استخدام قالب الرسالة
        function useTemplate(templateType) {
            var templates = {
                reminder: `🏥 *عيادة الدكتور أحمد محمد علي*

مرحباً [اسم المريض]،

🔔 تذكير بموعدك:
📅 التاريخ: [التاريخ]
🕐 الوقت: [الوقت]
👨‍⚕️ الطبيب: د. أحمد محمد علي

📍 العنوان: شارع الملك فهد، الرياض
📞 للاستفسار: ************

⚠️ يرجى الحضور قبل 15 دقيقة من الموعد
💡 في حالة عدم التمكن من الحضور، يرجى الاتصال لإعادة الجدولة

شكراً لثقتكم بنا 🙏`,

                confirmation: `🏥 *عيادة الدكتور أحمد محمد علي*

مرحباً [اسم المريض]،

✅ تم تأكيد حجز موعدك بنجاح:

📅 التاريخ: [التاريخ]
🕐 الوقت: [الوقت]
🏥 نوع الزيارة: [نوع الزيارة]
👨‍⚕️ الطبيب: د. أحمد محمد علي

📍 العنوان: شارع الملك فهد، الرياض
📞 للاستفسار: ************

💡 نصائح مهمة:
• احضر قبل 15 دقيقة من الموعد
• أحضر معك الهوية الشخصية
• أحضر التقارير الطبية السابقة (إن وجدت)

شكراً لثقتكم بنا 🙏`
            };

            var template = templates[templateType];
            if (template) {
                // نسخ القالب للحافظة
                navigator.clipboard.writeText(template).then(function() {
                    var templateName = templateType === 'reminder' ? 'التذكير' : 'التأكيد';
                    alert('✅ تم نسخ قالب ' + templateName + ' للحافظة!\n\nيمكنك الآن لصقه في أي مكان وتعديل المتغيرات حسب الحاجة.');
                }).catch(function() {
                    // في حالة فشل النسخ، عرض القالب في نافذة منبثقة
                    var popup = window.open('', '_blank', 'width=500,height=600,scrollbars=yes');
                    var templateName = templateType === 'reminder' ? 'التذكير' : 'التأكيد';
                    var popupContent = '<html dir="rtl">' +
                        '<head>' +
                        '<title>قالب الرسالة</title>' +
                        '<style>' +
                        'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
                        'textarea { width: 100%; height: 400px; font-family: Arial, sans-serif; }' +
                        '</style>' +
                        '</head>' +
                        '<body>' +
                        '<h3>قالب ' + templateName + '</h3>' +
                        '<textarea readonly>' + template + '</textarea>' +
                        '<p>انسخ النص أعلاه واستخدمه حسب الحاجة</p>' +
                        '</body>' +
                        '</html>';
                    popup.document.write(popupContent);
                });
            }
        }

        // دالة تعديل الموعد
        function editAppointment(appointmentId) {
            alert('🔧 ميزة تعديل المواعيد ستكون متاحة قريباً!\nيمكنك حجز موعد جديد في الوقت الحالي.');
            window.location.hash = '#new-appointment';
        }

        // دالة إضافة موعد تجريبي
        function addTestAppointment() {
            console.log('🧪 إضافة موعد تجريبي...');

            var today = new Date().toISOString().split('T')[0];
            var testAppointment = {
                id: 'test_' + Date.now(),
                patientName: 'أحمد محمد علي',
                phoneNumber: '+966501234567',
                age: '35',
                gender: 'ذكر',
                maritalStatus: 'متزوج',
                education: 'بكالوريوس',
                occupation: 'مهندس',
                height: '175',
                weight: '80',
                bmi: '26.1',
                bmiCategory: 'زيادة وزن',
                medicalHistory: {
                    'allergies': { answer: 'نعم', details: 'حساسية من البنسلين' },
                    'medications': { answer: 'نعم', details: 'فيتامين د 1000 وحدة يومياً' },
                    'surgeries': { answer: 'لا', details: '' },
                    'chronic-diseases': { answer: 'نعم', details: 'ضغط دم مرتفع' },
                    'exercise': { answer: 'نعم', details: 'مشي 3 مرات أسبوعياً' },
                    'pregnancy': { answer: 'غير مطبق', details: '' },
                    'breastfeeding': { answer: 'غير مطبق', details: '' },
                    'smoking': { answer: 'سابقاً', details: 'توقفت منذ سنتين' },
                    'alcohol': { answer: 'لا', details: '' },
                    'previous-diets': { answer: 'نعم', details: 'نظام كيتو لمدة 6 أشهر' },
                    'eating-disorders': { answer: 'لا', details: '' },
                    'additionalNotes': 'يعاني من حساسية موسمية في الربيع'
                },
                date: today,
                time: '14:30',
                visitType: 'كشف أولي',
                notes: 'موعد تجريبي للاختبار',
                status: 'مؤكد',
                createdAt: new Date().toISOString()
            };

            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            appointments.push(testAppointment);
            localStorage.setItem('appointments', JSON.stringify(appointments));

            console.log('✅ تم حفظ الموعد التجريبي:', testAppointment);
            console.log('📊 إجمالي المواعيد:', appointments.length);

            // تحديث فوري للعرض
            setTimeout(function() {
                displayAppointments();
                updateAppointmentStats();
            }, 100);

            alert('✅ تم إضافة موعد تجريبي بنجاح!\n\nالمريض: أحمد محمد علي\nالعمر: 35 سنة\nالوقت: ' + formatTimeSimple('14:30') + '\nالطول: 175 سم\nالوزن: 80 كجم\nمؤشر كتلة الجسم: 26.1 (زيادة وزن)');
        }

        // دالة إضافة مواعيد تجريبية متعددة
        function addMultipleTestAppointments() {
            console.log('🧪 إضافة مواعيد تجريبية متعددة...');

            var today = new Date().toISOString().split('T')[0];
            var testAppointments = [
                {
                    id: 'test_morning_' + Date.now(),
                    patientName: 'سارة أحمد محمد',
                    phoneNumber: '+966507654321',
                    age: '28',
                    gender: 'أنثى',
                    maritalStatus: 'متزوج',
                    education: 'ماجستير',
                    occupation: 'معلم',
                    height: '160',
                    weight: '55',
                    bmi: '21.5',
                    bmiCategory: 'وزن طبيعي',
                    date: today,
                    time: '08:30',
                    visitType: 'متابعة',
                    status: 'مؤكد'
                },
                {
                    id: 'test_afternoon_' + Date.now() + 1,
                    patientName: 'محمد حسن علي',
                    phoneNumber: '+966551234567',
                    age: '42',
                    gender: 'ذكر',
                    maritalStatus: 'متزوج',
                    education: 'دكتوراه',
                    occupation: 'طبيب',
                    height: '180',
                    weight: '95',
                    bmi: '29.3',
                    bmiCategory: 'زيادة وزن',
                    date: today,
                    time: '16:00',
                    visitType: 'استشارة تغذية',
                    status: 'مؤكد'
                },
                {
                    id: 'test_evening_' + Date.now() + 2,
                    patientName: 'فاطمة عبدالله',
                    phoneNumber: '+966509876543',
                    age: '35',
                    gender: 'أنثى',
                    maritalStatus: 'أعزب',
                    education: 'بكالوريوس',
                    occupation: 'مهندس',
                    height: '165',
                    weight: '70',
                    bmi: '25.7',
                    bmiCategory: 'زيادة وزن',
                    date: today,
                    time: '20:30',
                    visitType: 'كشف أولي',
                    status: 'مؤكد'
                },
                {
                    id: 'test_night_' + Date.now() + 3,
                    patientName: 'عبدالرحمن محمد',
                    phoneNumber: '+966502468135',
                    age: '50',
                    gender: 'ذكر',
                    maritalStatus: 'متزوج',
                    education: 'ثانوي',
                    occupation: 'تاجر',
                    height: '175',
                    weight: '85',
                    bmi: '27.8',
                    bmiCategory: 'زيادة وزن',
                    date: today,
                    time: '23:00',
                    visitType: 'فحص دوري',
                    status: 'مؤكد'
                }
            ];

            var appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            testAppointments.forEach(function(apt) {
                appointments.push(apt);
            });
            localStorage.setItem('appointments', JSON.stringify(appointments));

            console.log('✅ تم حفظ ' + testAppointments.length + ' مواعيد تجريبية');

            // تحديث فوري للعرض
            setTimeout(function() {
                displayAppointments();
                updateAppointmentStats();
            }, 100);

            alert('✅ تم إضافة ' + testAppointments.length + ' مواعيد تجريبية بنجاح!\n\n' +
                  '🌅 08:30 - سارة أحمد (صباحاً)\n' +
                  '☀️ 16:00 - محمد حسن (بعد الظهر)\n' +
                  '🌆 20:30 - فاطمة عبدالله (مساءً)\n' +
                  '🌙 23:00 - عبدالرحمن محمد (ليلاً)');
        }

        // دالة مسح جميع المواعيد
        function clearAllAppointments() {
            if (confirm('⚠️ هل أنت متأكد من حذف جميع المواعيد؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('appointments');
                console.log('🗑️ تم مسح جميع المواعيد');

                displayAppointments();
                updateAppointmentStats();

                alert('✅ تم مسح جميع المواعيد بنجاح!');
            }
        }

        // دوال إدارة إعدادات العيادة

        // دالة آمنة للحصول على إعدادات العيادة
        function getSafeClinicSettings() {
            try {
                var settings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');

                // القيم الافتراضية
                var defaults = {
                    doctorName: 'د. أحمد محمد علي',
                    clinicName: 'عيادة الدكتور أحمد محمد علي',
                    specialty: 'أخصائي الطب الباطني والتغذية العلاجية',
                    licenseNumber: '12345',
                    address: 'شارع الملك فهد، الرياض',
                    phone: '************',
                    email: '<EMAIL>',
                    logo: '🏥',
                    logoType: 'emoji'
                };

                // دمج الإعدادات المحفوظة مع الافتراضية
                var finalSettings = Object.assign({}, defaults, settings);
                console.log('✅ إعدادات العيادة الآمنة:', finalSettings);
                return finalSettings;

            } catch (error) {
                console.log('⚠️ خطأ في تحميل الإعدادات، استخدام الافتراضية:', error);
                return {
                    doctorName: 'د. أحمد محمد علي',
                    clinicName: 'عيادة الدكتور أحمد محمد علي',
                    specialty: 'أخصائي الطب الباطني والتغذية العلاجية',
                    licenseNumber: '12345',
                    address: 'شارع الملك فهد، الرياض',
                    phone: '************',
                    email: '<EMAIL>',
                    logo: '🏥',
                    logoType: 'emoji'
                };
            }
        }

        // دالة تحميل إعدادات العيادة
        function loadClinicSettings() {
            var settings = JSON.parse(localStorage.getItem('clinicSettings') || '{}');

            // تحميل القيم الافتراضية
            var defaults = {
                doctorName: 'د. أحمد محمد علي',
                clinicName: 'عيادة الدكتور أحمد محمد علي',
                specialty: 'أخصائي الطب الباطني والتغذية العلاجية',
                licenseNumber: '12345',
                address: 'شارع الملك فهد، الرياض',
                phone: '************',
                email: '<EMAIL>',
                logo: '🏥',
                logoType: 'emoji' // 'emoji' أو 'image'
            };

            // دمج الإعدادات المحفوظة مع الافتراضية
            return Object.assign(defaults, settings);
        }

        // دالة حفظ إعدادات العيادة
        function saveClinicSettings(settings) {
            localStorage.setItem('clinicSettings', JSON.stringify(settings));
            console.log('✅ تم حفظ إعدادات العيادة:', settings);
        }

        // دالة تطبيق الإعدادات على الواجهة
        function applySettingsToInterface() {
            var settings = getSafeClinicSettings();

            // تحديث الـ Sidebar
            var sidebarLogoElement = document.querySelector('.sidebar-logo span');
            var sidebarClinicNameElement = document.querySelector('.sidebar-clinic-name');
            var sidebarDoctorNameElement = document.querySelector('.sidebar-doctor-name');

            if (sidebarLogoElement) {
                if (settings.logoType === 'image' && settings.logoImage) {
                    sidebarLogoElement.parentElement.innerHTML = '<img src="' + settings.logoImage + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                } else {
                    sidebarLogoElement.textContent = settings.logo;
                }
            }

            if (sidebarClinicNameElement) sidebarClinicNameElement.textContent = settings.clinicName;
            if (sidebarDoctorNameElement) sidebarDoctorNameElement.textContent = '👨‍⚕️ ' + settings.doctorName;

            // تحديث الهيدر الرئيسي
            var logoElement = document.querySelector('.header-logo span');
            var clinicNameElement = document.querySelector('.clinic-name');
            var specialtyElement = document.querySelector('.clinic-specialty');
            var contactElement = document.querySelector('.clinic-contact');
            var doctorNameElement = document.querySelector('.doctor-name');
            var licenseElement = document.querySelector('.license-number');

            if (logoElement) {
                if (settings.logoType === 'image' && settings.logoImage) {
                    logoElement.parentElement.innerHTML = '<img src="' + settings.logoImage + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                } else {
                    logoElement.textContent = settings.logo;
                }
            }

            if (clinicNameElement) clinicNameElement.textContent = settings.clinicName;
            if (specialtyElement) specialtyElement.textContent = settings.specialty;
            if (contactElement) contactElement.textContent = '📍 ' + settings.address + ' | 📞 ' + settings.phone + ' | 📧 ' + settings.email;
            if (doctorNameElement) doctorNameElement.textContent = '👨‍⚕️ ' + settings.doctorName;
            if (licenseElement) licenseElement.textContent = 'رقم الترخيص: ' + settings.licenseNumber;

            console.log('✅ تم تطبيق إعدادات العيادة على الواجهة والـ Sidebar');
        }

        // دالة تحميل النموذج بالإعدادات المحفوظة
        function loadSettingsForm() {
            var settings = loadClinicSettings();

            document.getElementById('doctor-name').value = settings.doctorName;
            document.getElementById('clinic-name').value = settings.clinicName;
            document.getElementById('doctor-specialty').value = settings.specialty;
            document.getElementById('license-number').value = settings.licenseNumber;
            document.getElementById('clinic-address').value = settings.address;
            document.getElementById('clinic-phone').value = settings.phone;
            document.getElementById('clinic-email').value = settings.email;
            document.getElementById('custom-emoji').value = settings.logo;

            // تحديث المعاينة
            updatePreview();
        }

        // دالة اختيار إيموجي
        function selectEmoji(emoji) {
            document.getElementById('custom-emoji').value = emoji;
            document.getElementById('emoji-preview').textContent = emoji;

            // إزالة التحديد من جميع الأزرار
            var emojiButtons = document.querySelectorAll('.emoji-btn');
            emojiButtons.forEach(function(btn) {
                btn.style.border = '2px solid #e9ecef';
                btn.style.background = 'white';
            });

            // تحديد الزر المختار
            event.target.style.border = '2px solid #007bff';
            event.target.style.background = '#e7f3ff';

            updatePreview();
        }

        // دالة معالجة رفع الشعار
        function handleLogoUpload(event) {
            var file = event.target.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                alert('يرجى اختيار ملف صورة صحيح (JPG, PNG, GIF)');
                return;
            }

            // التحقق من حجم الملف (2 ميجابايت)
            if (file.size > 2 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت');
                return;
            }

            var reader = new FileReader();
            reader.onload = function(e) {
                var imageData = e.target.result;

                // عرض المعاينة
                document.getElementById('current-logo').innerHTML =
                    '<img src="' + imageData + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';

                // حفظ الصورة مؤقتاً
                window.tempLogoImage = imageData;

                updatePreview();
            };

            reader.readAsDataURL(file);
        }

        // دالة تحديث المعاينة
        function updatePreview() {
            var doctorName = document.getElementById('doctor-name').value || 'د. أحمد محمد علي';
            var clinicName = document.getElementById('clinic-name').value || 'عيادة الدكتور أحمد محمد علي';
            var specialty = document.getElementById('doctor-specialty').value || 'أخصائي الطب الباطني والتغذية العلاجية';
            var licenseNumber = document.getElementById('license-number').value || '12345';
            var address = document.getElementById('clinic-address').value || 'شارع الملك فهد، الرياض';
            var phone = document.getElementById('clinic-phone').value || '************';
            var logo = document.getElementById('custom-emoji').value || '🏥';

            // تحديث المعاينة في صفحة الإعدادات
            document.getElementById('preview-doctor-name').textContent = '👨‍⚕️ ' + doctorName;
            document.getElementById('preview-clinic-name').textContent = clinicName;
            document.getElementById('preview-specialty').textContent = specialty;
            document.getElementById('preview-license').textContent = 'رقم الترخيص: ' + licenseNumber;
            document.getElementById('preview-address').textContent = '📍 ' + address + ' | 📞 ' + phone;

            // تحديث الشعار في المعاينة
            var previewLogo = document.getElementById('preview-logo');
            var previewSidebarLogo = document.getElementById('preview-sidebar-logo');
            var previewSidebarClinicName = document.getElementById('preview-sidebar-clinic-name');
            var previewSidebarDoctorName = document.getElementById('preview-sidebar-doctor-name');

            if (window.tempLogoImage) {
                previewLogo.innerHTML = '<img src="' + window.tempLogoImage + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                if (previewSidebarLogo) {
                    previewSidebarLogo.innerHTML = '<img src="' + window.tempLogoImage + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                }
            } else {
                previewLogo.textContent = logo;
                if (previewSidebarLogo) {
                    previewSidebarLogo.innerHTML = '<span style="font-size: 20px; color: white;">' + logo + '</span>';
                }
            }

            // تحديث معاينة الـ sidebar
            if (previewSidebarClinicName) previewSidebarClinicName.textContent = clinicName;
            if (previewSidebarDoctorName) previewSidebarDoctorName.textContent = '👨‍⚕️ ' + doctorName;

            // تحديث الـ Sidebar مباشرة (معاينة مباشرة)
            var sidebarClinicName = document.querySelector('.sidebar-clinic-name');
            var sidebarDoctorName = document.querySelector('.sidebar-doctor-name');
            var sidebarLogo = document.querySelector('.sidebar-logo span');

            if (sidebarClinicName) sidebarClinicName.textContent = clinicName;
            if (sidebarDoctorName) sidebarDoctorName.textContent = '👨‍⚕️ ' + doctorName;
            if (sidebarLogo) {
                if (window.tempLogoImage) {
                    sidebarLogo.parentElement.innerHTML = '<img src="' + window.tempLogoImage + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">';
                } else {
                    sidebarLogo.textContent = logo;
                }
            }
        }

        // دالة حفظ إعدادات الشعار
        function saveLogoSettings() {
            var settings = loadClinicSettings();

            var emoji = document.getElementById('custom-emoji').value || '🏥';

            if (window.tempLogoImage) {
                settings.logoType = 'image';
                settings.logoImage = window.tempLogoImage;
                settings.logo = emoji; // احتفاظ بالإيموجي كبديل
            } else {
                settings.logoType = 'emoji';
                settings.logo = emoji;
                delete settings.logoImage;
            }

            saveClinicSettings(settings);
            applySettingsToInterface();

            alert('✅ تم حفظ إعدادات الشعار بنجاح!');
        }

        // دالة إعادة تعيين الشعار
        function resetLogo() {
            if (confirm('هل تريد إعادة تعيين الشعار للافتراضي؟')) {
                document.getElementById('custom-emoji').value = '🏥';
                document.getElementById('emoji-preview').textContent = '🏥';
                document.getElementById('current-logo').innerHTML = '🏥';

                // إزالة الصورة المؤقتة
                delete window.tempLogoImage;

                // إزالة التحديد من الأزرار
                var emojiButtons = document.querySelectorAll('.emoji-btn');
                emojiButtons.forEach(function(btn) {
                    btn.style.border = '2px solid #e9ecef';
                    btn.style.background = 'white';
                });

                updatePreview();

                alert('✅ تم إعادة تعيين الشعار!');
            }
        }

        // دالة تطبيق التغييرات
        function applyChanges() {
            var settings = {
                doctorName: document.getElementById('doctor-name').value || 'د. أحمد محمد علي',
                clinicName: document.getElementById('clinic-name').value || 'عيادة الدكتور أحمد محمد علي',
                specialty: document.getElementById('doctor-specialty').value || 'أخصائي الطب الباطني والتغذية العلاجية',
                licenseNumber: document.getElementById('license-number').value || '12345',
                address: document.getElementById('clinic-address').value || 'شارع الملك فهد، الرياض',
                phone: document.getElementById('clinic-phone').value || '************',
                email: document.getElementById('clinic-email').value || '<EMAIL>',
                logo: document.getElementById('custom-emoji').value || '🏥',
                logoType: window.tempLogoImage ? 'image' : 'emoji'
            };

            if (window.tempLogoImage) {
                settings.logoImage = window.tempLogoImage;
            }

            saveClinicSettings(settings);
            applySettingsToInterface();

            alert('🚀 تم تطبيق جميع التغييرات بنجاح!\n\nسيتم تحديث جميع أجزاء النظام بالمعلومات الجديدة.');

            // إعادة تحميل الصفحة لتطبيق التغييرات
            setTimeout(function() {
                location.reload();
            }, 1000);
        }

        // معالج نموذج معلومات العيادة
        document.addEventListener('DOMContentLoaded', function() {
            var clinicInfoForm = document.getElementById('clinic-info-form');
            if (clinicInfoForm) {
                clinicInfoForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    var settings = loadClinicSettings();

                    settings.doctorName = document.getElementById('doctor-name').value;
                    settings.clinicName = document.getElementById('clinic-name').value;
                    settings.specialty = document.getElementById('doctor-specialty').value;
                    settings.licenseNumber = document.getElementById('license-number').value;
                    settings.address = document.getElementById('clinic-address').value;
                    settings.phone = document.getElementById('clinic-phone').value;
                    settings.email = document.getElementById('clinic-email').value;

                    saveClinicSettings(settings);
                    updatePreview();

                    alert('✅ تم حفظ معلومات العيادة بنجاح!');
                });
            }

            // تحميل الإعدادات عند فتح الصفحة
            if (window.location.hash === '#clinic-settings') {
                setTimeout(function() {
                    loadSettingsForm();
                }, 100);
            }

            // تطبيق الإعدادات عند تحميل الصفحة
            applySettingsToInterface();

            // إضافة مستمعين للتحديث المباشر
            var inputs = ['doctor-name', 'clinic-name', 'doctor-specialty', 'license-number', 'clinic-address', 'clinic-phone', 'custom-emoji'];
            inputs.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });
        });

        // دوال إضافة الأطعمة المخصصة
        function addCustomCarb() {
            var foodName = prompt('أدخل اسم الكربوهيدرات الجديد:\n(مثال: خبز الشعير)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 75)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: شريحة واحدة)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن منطقة checkboxes الكربوهيدرات
            var carbContainer = document.querySelector('input[name="breakfast-carbs"]');
            if (carbContainer) {
                var parentDiv = carbContainer.closest('div[style*="grid-template-columns"]');
                if (!parentDiv) {
                    // البحث عن أي div يحتوي على checkboxes الكربوهيدرات
                    var allCarbCheckboxes = document.querySelectorAll('input[name="breakfast-carbs"]');
                    if (allCarbCheckboxes.length > 0) {
                        parentDiv = allCarbCheckboxes[allCarbCheckboxes.length - 1].closest('div[style*="grid-template-columns"]');
                    }
                }

                if (parentDiv) {
                    // إنشاء checkbox جديد
                    var newLabel = document.createElement('label');
                    newLabel.style.display = 'flex';
                    newLabel.style.alignItems = 'center';
                    newLabel.style.cursor = 'pointer';

                    var newCheckbox = document.createElement('input');
                    newCheckbox.type = 'checkbox';
                    newCheckbox.name = 'breakfast-carbs';
                    newCheckbox.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                    newCheckbox.style.marginLeft = '8px';
                    newCheckbox.checked = true; // اختياره تلقائياً

                    var newSpan = document.createElement('span');
                    newSpan.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                    newLabel.appendChild(newCheckbox);
                    newLabel.appendChild(newSpan);

                    // إضافة العنصر الجديد
                    parentDiv.appendChild(newLabel);

                    alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً.');
                } else {
                    alert('لم يتم العثور على منطقة الكربوهيدرات');
                }
            } else {
                alert('لم يتم العثور على قائمة الكربوهيدرات');
            }
        }

        function addCustomProtein() {
            var foodName = prompt('أدخل اسم البروتين الجديد:\n(مثال: جبنة حلوم)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 90)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: قطعة متوسطة)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن قائمة البروتين (ثاني select في الإفطار)
            var proteinSelect = document.querySelector('#breakfast-protein');
            if (!proteinSelect) {
                var allSelects = document.querySelectorAll('#create-nutrition-plan select');
                if (allSelects.length > 1) {
                    proteinSelect = allSelects[1];
                }
            }

            if (proteinSelect) {
                var newOption = document.createElement('option');
                newOption.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                newOption.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                proteinSelect.appendChild(newOption);
                proteinSelect.value = newOption.value;

                alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً في القائمة.');
            } else {
                alert('لم يتم العثور على قائمة البروتين');
            }
        }

        function addCustomVegetable() {
            var foodName = prompt('أدخل اسم الخضروات الجديدة:\n(مثال: فجل أحمر)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 10)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: حبة متوسطة)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن قائمة الخضروات (ثالث select في الإفطار)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');
            var vegSelect = allSelects.length > 2 ? allSelects[2] : null;

            if (vegSelect) {
                var newOption = document.createElement('option');
                newOption.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                newOption.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                vegSelect.appendChild(newOption);
                vegSelect.value = newOption.value;

                alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً في القائمة.');
            } else {
                alert('لم يتم العثور على قائمة الخضروات');
            }
        }

        function addCustomFruit() {
            var foodName = prompt('أدخل اسم الفاكهة الجديدة:\n(مثال: مانجو)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 120)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: حبة متوسطة)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن قائمة الفواكه (رابع select في الإفطار)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');
            var fruitSelect = allSelects.length > 3 ? allSelects[3] : null;

            if (fruitSelect) {
                var newOption = document.createElement('option');
                newOption.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                newOption.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                fruitSelect.appendChild(newOption);
                fruitSelect.value = newOption.value;

                alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً في القائمة.');
            } else {
                alert('لم يتم العثور على قائمة الفواكه');
            }
        }

        function addCustomDrink() {
            var foodName = prompt('أدخل اسم المشروب الجديد:\n(مثال: عصير الرمان)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 85)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: كوب واحد)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن قائمة المشروبات (خامس select في الإفطار)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');
            var drinkSelect = allSelects.length > 4 ? allSelects[4] : null;

            if (drinkSelect) {
                var newOption = document.createElement('option');
                newOption.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                newOption.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                drinkSelect.appendChild(newOption);
                drinkSelect.value = newOption.value;

                alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً في القائمة.');
            } else {
                alert('لم يتم العثور على قائمة المشروبات');
            }
        }

        function addCustomMorningSnack() {
            var foodName = prompt('أدخل اسم الوجبة الخفيفة الجديدة:\n(مثال: كعك الشوفان)');
            if (!foodName) return;

            var calories = prompt('أدخل عدد السعرات الحرارية:\n(مثال: 95)');
            if (!calories || isNaN(calories)) {
                alert('يرجى إدخال رقم صحيح للسعرات الحرارية');
                return;
            }

            var quantity = prompt('أدخل الكمية:\n(مثال: قطعة واحدة)');
            if (!quantity) quantity = 'حصة واحدة';

            // البحث عن قائمة الوجبة الخفيفة الصباحية (سادس select)
            var allSelects = document.querySelectorAll('#create-nutrition-plan select');
            var snackSelect = allSelects.length > 5 ? allSelects[5] : null;

            if (snackSelect) {
                var newOption = document.createElement('option');
                newOption.value = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';
                newOption.textContent = foodName + ' (' + quantity + ' - ' + calories + ' سعرة)';

                snackSelect.appendChild(newOption);
                snackSelect.value = newOption.value;

                alert('تم إضافة ' + foodName + ' بنجاح!\nتم اختياره تلقائياً في القائمة.');
            } else {
                alert('لم يتم العثور على قائمة الوجبة الخفيفة');
            }
        }


    </script>
</body>
</html>
