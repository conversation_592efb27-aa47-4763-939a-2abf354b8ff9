from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from .models import Patient, InBodyResult, WeightRecord

User = get_user_model()


class PatientForm(forms.ModelForm):
    """نموذج إضافة/تعديل المريض"""
    
    # حقول المستخدم
    first_name = forms.Char<PERSON>ield(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    phone_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    whatsapp_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    date_of_birth = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    class Meta:
        model = Patient
        fields = [
            'gender', 'height', 'current_weight', 'target_weight',
            'activity_level', 'health_goal', 'medical_conditions',
            'allergies', 'medications', 'notes'
        ]
        widgets = {
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'height': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'current_weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'target_weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'activity_level': forms.Select(attrs={'class': 'form-select'}),
            'health_goal': forms.Select(attrs={'class': 'form-select'}),
            'medical_conditions': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'allergies': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'medications': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        # استخراج المستخدم إذا كان موجوداً
        self.user_instance = None
        if 'instance' in kwargs and kwargs['instance']:
            self.user_instance = kwargs['instance'].user
            # تعبئة حقول المستخدم
            kwargs['initial'] = kwargs.get('initial', {})
            kwargs['initial'].update({
                'first_name': self.user_instance.first_name,
                'last_name': self.user_instance.last_name,
                'email': self.user_instance.email,
                'phone_number': self.user_instance.phone_number,
                'whatsapp_number': self.user_instance.whatsapp_number,
                'date_of_birth': self.user_instance.date_of_birth,
            })
        
        super().__init__(*args, **kwargs)

    def save(self, commit=True):
        patient = super().save(commit=False)
        
        # إنشاء أو تحديث المستخدم
        if self.user_instance:
            # تحديث مستخدم موجود
            user = self.user_instance
        else:
            # إنشاء مستخدم جديد
            username = f"patient_{self.cleaned_data['email'].split('@')[0]}"
            # التأكد من عدم تكرار اسم المستخدم
            counter = 1
            original_username = username
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1
            
            user = User.objects.create_user(
                username=username,
                email=self.cleaned_data['email'],
                user_type='patient'
            )
            patient.user = user
        
        # تحديث بيانات المستخدم
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']
        user.phone_number = self.cleaned_data['phone_number']
        user.whatsapp_number = self.cleaned_data['whatsapp_number']
        user.date_of_birth = self.cleaned_data['date_of_birth']
        
        if commit:
            user.save()
            patient.save()
        
        return patient


class InBodyResultForm(forms.ModelForm):
    """نموذج إضافة نتيجة InBody"""
    
    class Meta:
        model = InBodyResult
        fields = [
            'weight', 'muscle_mass', 'body_fat_mass', 'body_fat_percentage',
            'total_body_water', 'protein_mass', 'mineral_mass',
            'basal_metabolic_rate', 'test_date', 'notes'
        ]
        widgets = {
            'weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'muscle_mass': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'body_fat_mass': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'body_fat_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'total_body_water': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'protein_mass': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'mineral_mass': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'basal_metabolic_rate': forms.NumberInput(attrs={'class': 'form-control'}),
            'test_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class WeightRecordForm(forms.ModelForm):
    """نموذج إضافة سجل وزن"""
    
    class Meta:
        model = WeightRecord
        fields = ['weight', 'recorded_date', 'notes']
        widgets = {
            'weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
            'recorded_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.pk:
            from django.utils import timezone
            self.fields['recorded_date'].initial = timezone.now().strftime('%Y-%m-%dT%H:%M')


class PatientSearchForm(forms.Form):
    """نموذج البحث عن المرضى"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('البحث بالاسم أو البريد الإلكتروني...')
        })
    )
    gender = forms.ChoiceField(
        choices=[('', _('جميع الأجناس'))] + Patient.GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    health_goal = forms.ChoiceField(
        choices=[('', _('جميع الأهداف'))] + Patient.GOAL_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
