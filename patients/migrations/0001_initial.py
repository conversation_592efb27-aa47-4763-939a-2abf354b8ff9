# Generated by Django 5.2.3 on 2025-06-20 18:01

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gender', models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('height', models.FloatField(validators=[django.core.validators.MinValueValidator(50), django.core.validators.MaxValueValidator(250)], verbose_name='الطول (سم)')),
                ('current_weight', models.FloatField(validators=[django.core.validators.MinValueValidator(20), django.core.validators.MaxValueValidator(300)], verbose_name='الوزن الحالي (كغ)')),
                ('target_weight', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(20), django.core.validators.MaxValueValidator(300)], verbose_name='الوزن المستهدف (كغ)')),
                ('activity_level', models.CharField(choices=[('sedentary', 'قليل الحركة'), ('light', 'نشاط خفيف'), ('moderate', 'نشاط متوسط'), ('active', 'نشط'), ('very_active', 'نشط جداً')], default='moderate', max_length=20, verbose_name='مستوى النشاط')),
                ('health_goal', models.CharField(choices=[('lose_weight', 'إنقاص الوزن'), ('gain_weight', 'زيادة الوزن'), ('maintain_weight', 'المحافظة على الوزن'), ('build_muscle', 'بناء العضلات'), ('improve_health', 'تحسين الصحة العامة')], max_length=20, verbose_name='الهدف الصحي')),
                ('medical_conditions', models.TextField(blank=True, verbose_name='الأمراض المزمنة')),
                ('allergies', models.TextField(blank=True, verbose_name='الحساسيات الغذائية')),
                ('medications', models.TextField(blank=True, verbose_name='الأدوية الحالية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('doctor', models.ForeignKey(limit_choices_to={'user_type': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='patients', to=settings.AUTH_USER_MODEL, verbose_name='الطبيب المعالج')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='patient_profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'مريض',
                'verbose_name_plural': 'المرضى',
            },
        ),
        migrations.CreateModel(
            name='InBodyResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight', models.FloatField(verbose_name='الوزن (كغ)')),
                ('muscle_mass', models.FloatField(verbose_name='كتلة العضلات (كغ)')),
                ('body_fat_mass', models.FloatField(verbose_name='كتلة الدهون (كغ)')),
                ('body_fat_percentage', models.FloatField(verbose_name='نسبة الدهون (%)')),
                ('total_body_water', models.FloatField(blank=True, null=True, verbose_name='إجمالي ماء الجسم (لتر)')),
                ('protein_mass', models.FloatField(blank=True, null=True, verbose_name='كتلة البروتين (كغ)')),
                ('mineral_mass', models.FloatField(blank=True, null=True, verbose_name='كتلة المعادن (كغ)')),
                ('basal_metabolic_rate', models.IntegerField(blank=True, null=True, verbose_name='معدل الأيض الأساسي (سعرة)')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('test_date', models.DateTimeField(verbose_name='تاريخ الفحص')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإدخال')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inbody_results', to='patients.patient', verbose_name='المريض')),
            ],
            options={
                'verbose_name': 'نتيجة InBody',
                'verbose_name_plural': 'نتائج InBody',
                'ordering': ['-test_date'],
            },
        ),
        migrations.CreateModel(
            name='WeightRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight', models.FloatField(validators=[django.core.validators.MinValueValidator(20), django.core.validators.MaxValueValidator(300)], verbose_name='الوزن (كغ)')),
                ('recorded_date', models.DateTimeField(verbose_name='تاريخ التسجيل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإدخال')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weight_records', to='patients.patient', verbose_name='المريض')),
            ],
            options={
                'verbose_name': 'سجل وزن',
                'verbose_name_plural': 'سجلات الوزن',
                'ordering': ['-recorded_date'],
            },
        ),
    ]
