from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Patient, InBodyResult, WeightRecord


class InBodyResultInline(admin.TabularInline):
    """نتائج InBody كـ inline"""
    model = InBodyResult
    extra = 0
    readonly_fields = ('created_at',)


class WeightRecordInline(admin.TabularInline):
    """سجلات الوزن كـ inline"""
    model = WeightRecord
    extra = 0
    readonly_fields = ('created_at',)


@admin.register(Patient)
class PatientAdmin(admin.ModelAdmin):
    """إدارة المرضى"""
    list_display = ('user', 'doctor', 'gender', 'current_weight', 'height', 'bmi', 'health_goal')
    list_filter = ('gender', 'activity_level', 'health_goal', 'doctor')
    search_fields = ('user__first_name', 'user__last_name', 'user__email')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('user', 'doctor')
        }),
        (_('المعلومات الجسدية'), {
            'fields': ('gender', 'height', 'current_weight', 'target_weight')
        }),
        (_('النشاط والأهداف'), {
            'fields': ('activity_level', 'health_goal')
        }),
        (_('الحالة الصحية'), {
            'fields': ('medical_conditions', 'allergies', 'medications')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
    inlines = [InBodyResultInline, WeightRecordInline]

    def bmi(self, obj):
        return obj.bmi
    bmi.short_description = _('مؤشر كتلة الجسم')


@admin.register(InBodyResult)
class InBodyResultAdmin(admin.ModelAdmin):
    """إدارة نتائج InBody"""
    list_display = ('patient', 'test_date', 'weight', 'body_fat_percentage', 'muscle_mass')
    list_filter = ('test_date', 'patient__doctor')
    search_fields = ('patient__user__first_name', 'patient__user__last_name')
    date_hierarchy = 'test_date'

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('patient', 'test_date')
        }),
        (_('القياسات الأساسية'), {
            'fields': ('weight', 'muscle_mass', 'body_fat_mass', 'body_fat_percentage')
        }),
        (_('قياسات إضافية'), {
            'fields': ('total_body_water', 'protein_mass', 'mineral_mass', 'basal_metabolic_rate')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ('created_at',)


@admin.register(WeightRecord)
class WeightRecordAdmin(admin.ModelAdmin):
    """إدارة سجلات الوزن"""
    list_display = ('patient', 'weight', 'recorded_date')
    list_filter = ('recorded_date', 'patient__doctor')
    search_fields = ('patient__user__first_name', 'patient__user__last_name')
    date_hierarchy = 'recorded_date'

    fieldsets = (
        (_('معلومات السجل'), {
            'fields': ('patient', 'weight', 'recorded_date')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ('created_at',)
