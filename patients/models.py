from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator


class Patient(models.Model):
    """نموذج المريض"""
    GENDER_CHOICES = [
        ('M', _('ذكر')),
        ('F', _('أنثى')),
    ]

    ACTIVITY_LEVEL_CHOICES = [
        ('sedentary', _('قليل الحركة')),
        ('light', _('نشاط خفيف')),
        ('moderate', _('نشاط متوسط')),
        ('active', _('نشط')),
        ('very_active', _('نشط جداً')),
    ]

    GOAL_CHOICES = [
        ('lose_weight', _('إنقاص الوزن')),
        ('gain_weight', _('زيادة الوزن')),
        ('maintain_weight', _('المحافظة على الوزن')),
        ('build_muscle', _('بناء العضلات')),
        ('improve_health', _('تحسين الصحة العامة')),
    ]

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='patient_profile',
        verbose_name=_('المستخدم')
    )
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='patients',
        limit_choices_to={'user_type': 'doctor'},
        verbose_name=_('الطبيب المعالج')
    )

    # معلومات شخصية
    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        verbose_name=_('الجنس')
    )
    height = models.FloatField(
        validators=[MinValueValidator(50), MaxValueValidator(250)],
        verbose_name=_('الطول (سم)')
    )
    current_weight = models.FloatField(
        validators=[MinValueValidator(20), MaxValueValidator(300)],
        verbose_name=_('الوزن الحالي (كغ)')
    )
    target_weight = models.FloatField(
        validators=[MinValueValidator(20), MaxValueValidator(300)],
        blank=True,
        null=True,
        verbose_name=_('الوزن المستهدف (كغ)')
    )

    # مستوى النشاط والأهداف
    activity_level = models.CharField(
        max_length=20,
        choices=ACTIVITY_LEVEL_CHOICES,
        default='moderate',
        verbose_name=_('مستوى النشاط')
    )
    health_goal = models.CharField(
        max_length=20,
        choices=GOAL_CHOICES,
        verbose_name=_('الهدف الصحي')
    )

    # الحالات الصحية
    medical_conditions = models.TextField(
        blank=True,
        verbose_name=_('الأمراض المزمنة')
    )
    allergies = models.TextField(
        blank=True,
        verbose_name=_('الحساسيات الغذائية')
    )
    medications = models.TextField(
        blank=True,
        verbose_name=_('الأدوية الحالية')
    )

    # ملاحظات
    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات إضافية')
    )

    # تواريخ
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مريض')
        verbose_name_plural = _('المرضى')

    def __str__(self):
        full_name = self.user.get_full_name()
        if full_name.strip():
            return full_name
        return self.user.username

    @property
    def bmi(self):
        """حساب مؤشر كتلة الجسم"""
        if self.height and self.current_weight:
            height_m = self.height / 100
            return round(self.current_weight / (height_m ** 2), 2)
        return None

    @property
    def bmi_category(self):
        """تصنيف مؤشر كتلة الجسم"""
        bmi = self.bmi
        if bmi is None:
            return None

        if bmi < 18.5:
            return _('نقص في الوزن')
        elif bmi < 25:
            return _('وزن طبيعي')
        elif bmi < 30:
            return _('زيادة في الوزن')
        else:
            return _('سمنة')


class InBodyResult(models.Model):
    """نتائج فحص InBody"""
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='inbody_results',
        verbose_name=_('المريض')
    )

    # القياسات الأساسية
    weight = models.FloatField(
        verbose_name=_('الوزن (كغ)')
    )
    muscle_mass = models.FloatField(
        verbose_name=_('كتلة العضلات (كغ)')
    )
    body_fat_mass = models.FloatField(
        verbose_name=_('كتلة الدهون (كغ)')
    )
    body_fat_percentage = models.FloatField(
        verbose_name=_('نسبة الدهون (%)')
    )

    # قياسات إضافية
    total_body_water = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_('إجمالي ماء الجسم (لتر)')
    )
    protein_mass = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_('كتلة البروتين (كغ)')
    )
    mineral_mass = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_('كتلة المعادن (كغ)')
    )

    # معدل الأيض
    basal_metabolic_rate = models.IntegerField(
        blank=True,
        null=True,
        verbose_name=_('معدل الأيض الأساسي (سعرة)')
    )

    # ملاحظات
    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    # تاريخ الفحص
    test_date = models.DateTimeField(
        verbose_name=_('تاريخ الفحص')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإدخال')
    )

    class Meta:
        verbose_name = _('نتيجة InBody')
        verbose_name_plural = _('نتائج InBody')
        ordering = ['-test_date']

    def __str__(self):
        return f"{self.patient} - {self.test_date.strftime('%Y-%m-%d')}"


class WeightRecord(models.Model):
    """سجل الوزن"""
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='weight_records',
        verbose_name=_('المريض')
    )
    weight = models.FloatField(
        validators=[MinValueValidator(20), MaxValueValidator(300)],
        verbose_name=_('الوزن (كغ)')
    )
    recorded_date = models.DateTimeField(
        verbose_name=_('تاريخ التسجيل')
    )
    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإدخال')
    )

    class Meta:
        verbose_name = _('سجل وزن')
        verbose_name_plural = _('سجلات الوزن')
        ordering = ['-recorded_date']

    def __str__(self):
        return f"{self.patient} - {self.weight}كغ - {self.recorded_date.strftime('%Y-%m-%d')}"
