from django.urls import path
from . import views

app_name = 'patients'

urlpatterns = [
    # قائمة المرضى
    path('', views.patient_list, name='patient_list'),
    
    # إضافة مريض جديد
    path('add/', views.add_patient, name='add_patient'),
    
    # تفاصيل المريض
    path('<int:patient_id>/', views.patient_detail, name='patient_detail'),
    
    # تعديل بيانات المريض
    path('<int:patient_id>/edit/', views.edit_patient, name='edit_patient'),
    
    # حذف المريض
    path('<int:patient_id>/delete/', views.delete_patient, name='delete_patient'),
    
    # إضافة نتيجة InBody
    path('<int:patient_id>/inbody/add/', views.add_inbody_result, name='add_inbody_result'),
    
    # إضافة سجل وزن
    path('<int:patient_id>/weight/add/', views.add_weight_record, name='add_weight_record'),
    
    # تقرير تطور المريض
    path('<int:patient_id>/progress/', views.patient_progress, name='patient_progress'),
]
