from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Patient
from .forms import PatientForm, InBodyResultForm, WeightRecordForm


@login_required
def patient_list(request):
    """قائمة المرضى"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patients = Patient.objects.filter(doctor=request.user).select_related('user').order_by('-created_at')

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        patients = patients.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query)
        )

    # التصفية
    gender_filter = request.GET.get('gender')
    if gender_filter:
        patients = patients.filter(gender=gender_filter)

    goal_filter = request.GET.get('goal')
    if goal_filter:
        patients = patients.filter(health_goal=goal_filter)

    # الترقيم
    paginator = Paginator(patients, 10)
    page_number = request.GET.get('page')
    patients = paginator.get_page(page_number)

    context = {
        'patients': patients,
        'search_query': search_query,
        'gender_filter': gender_filter,
        'goal_filter': goal_filter,
    }
    return render(request, 'patients/patient_list.html', context)


@login_required
def add_patient(request):
    """إضافة مريض جديد"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            patient = form.save(commit=False)
            patient.doctor = request.user
            patient.save()
            messages.success(request, _('تم إضافة المريض بنجاح!'))
            return redirect('patients:patient_detail', patient_id=patient.id)
    else:
        form = PatientForm()

    return render(request, 'patients/add_patient.html', {'form': form})


@login_required
def patient_detail(request, patient_id):
    """تفاصيل المريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    # آخر نتائج InBody
    latest_inbody = patient.inbody_results.first()

    # آخر سجلات الوزن
    weight_records = patient.weight_records.all()[:10]

    # الخطط الغذائية
    nutrition_plans = patient.nutrition_plans.all()[:5]

    context = {
        'patient': patient,
        'latest_inbody': latest_inbody,
        'weight_records': weight_records,
        'nutrition_plans': nutrition_plans,
    }
    return render(request, 'patients/patient_detail.html', context)


@login_required
def edit_patient(request, patient_id):
    """تعديل بيانات المريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث بيانات المريض بنجاح!'))
            return redirect('patients:patient_detail', patient_id=patient.id)
    else:
        form = PatientForm(instance=patient)

    return render(request, 'patients/edit_patient.html', {'form': form, 'patient': patient})


@login_required
def delete_patient(request, patient_id):
    """حذف المريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        patient_name = patient.user.get_full_name()
        patient.delete()
        messages.success(request, _('تم حذف المريض {} بنجاح!').format(patient_name))
        return redirect('patients:patient_list')

    return render(request, 'patients/delete_patient.html', {'patient': patient})


@login_required
def add_inbody_result(request, patient_id):
    """إضافة نتيجة InBody"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = InBodyResultForm(request.POST)
        if form.is_valid():
            inbody_result = form.save(commit=False)
            inbody_result.patient = patient
            inbody_result.save()
            messages.success(request, _('تم إضافة نتيجة InBody بنجاح!'))
            return redirect('patients:patient_detail', patient_id=patient.id)
    else:
        form = InBodyResultForm()

    return render(request, 'patients/add_inbody_result.html', {'form': form, 'patient': patient})


@login_required
def add_weight_record(request, patient_id):
    """إضافة سجل وزن"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = WeightRecordForm(request.POST)
        if form.is_valid():
            weight_record = form.save(commit=False)
            weight_record.patient = patient
            weight_record.save()

            # تحديث الوزن الحالي للمريض
            patient.current_weight = weight_record.weight
            patient.save()

            messages.success(request, _('تم إضافة سجل الوزن بنجاح!'))
            return redirect('patients:patient_detail', patient_id=patient.id)
    else:
        form = WeightRecordForm()

    return render(request, 'patients/add_weight_record.html', {'form': form, 'patient': patient})


@login_required
def patient_progress(request, patient_id):
    """تقرير تطور المريض"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    # سجلات الوزن للرسم البياني
    weight_records = patient.weight_records.all()[:30]  # آخر 30 سجل

    # نتائج InBody
    inbody_results = patient.inbody_results.all()[:10]  # آخر 10 نتائج

    context = {
        'patient': patient,
        'weight_records': weight_records,
        'inbody_results': inbody_results,
    }
    return render(request, 'patients/patient_progress.html', context)
