<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الـ Sidebar - نظام إدارة العيادات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 0.125rem 0 0.25rem rgba(0, 0, 0, 0.075);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        
        .status-working { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-testing { background-color: #ffc107; }
        .status-pending { background-color: #6c757d; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 250px;
            z-index: 999;
        }
        
        .content-wrapper {
            margin-top: 80px;
        }
        
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            border-left: 4px solid;
        }
        
        .test-result.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-result.error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-result.testing {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .btn-test {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-heartbeat me-2"></i>
                نظام إدارة العيادات الغذائية - اختبار الـ Sidebar
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="current-time"></span>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="position-sticky pt-4">
            <div class="text-center mb-4">
                <h5 class="text-white">🏥 قائمة التنقل</h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#" onclick="testLink('http://127.0.0.1:8000/doctor/dashboard/', this, 'لوحة التحكم')">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <span>لوحة التحكم</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/patients/', this, 'إدارة المرضى')">
                        <i class="fas fa-users me-2"></i>
                        <span>إدارة المرضى</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/nutrition/', this, 'الخطط الغذائية')">
                        <i class="fas fa-utensils me-2"></i>
                        <span>الخطط الغذائية</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/nutrition/foods/', this, 'إدارة الأطعمة')">
                        <i class="fas fa-apple-alt me-2"></i>
                        <span>إدارة الأطعمة</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/communications/appointments/', this, 'المواعيد')">
                        <i class="fas fa-calendar me-2"></i>
                        <span>المواعيد</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/communications/messages/', this, 'الرسائل')">
                        <i class="fas fa-envelope me-2"></i>
                        <span>الرسائل</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="testLink('http://127.0.0.1:8000/communications/whatsapp/', this, 'رسائل الواتساب')">
                        <i class="fab fa-whatsapp me-2"></i>
                        <span>رسائل الواتساب</span>
                        <span class="status-indicator status-pending ms-auto"></span>
                    </a>
                </li>
            </ul>
            
            <div class="mt-4 px-3">
                <button class="btn btn-test w-100 mb-2" onclick="testAllLinks()">
                    <i class="fas fa-play me-2"></i>اختبار جميع الروابط
                </button>
                <button class="btn btn-success w-100" onclick="openAllLinks()">
                    <i class="fas fa-external-link-alt me-2"></i>فتح جميع الروابط
                </button>
            </div>
        </div>
    </nav>

    <!-- Main content area -->
    <main class="main-content">
        <div class="content-wrapper">
            <div class="test-card">
                <h2><i class="fas fa-rocket me-2 text-primary"></i>مرحباً بك في اختبار الـ Sidebar</h2>
                <p class="lead">هذه الصفحة لاختبار جميع روابط الـ Sidebar في نظام إدارة العيادات الغذائية.</p>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ملاحظة مهمة:</h6>
                    <p class="mb-0">إذا ظهر "جاري الاختبار" فقط، فهذا يعني أن الخادم يعمل ولكن يحتاج تسجيل دخول.
                    استخدم زر <strong>"فتح جميع الروابط"</strong> لفتح الصفحات مباشرة.</p>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <span class="status-indicator status-working"></span>
                            <small class="d-block mt-1">يعمل بشكل صحيح</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <span class="status-indicator status-testing"></span>
                            <small class="d-block mt-1">قيد الاختبار</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <span class="status-indicator status-error"></span>
                            <small class="d-block mt-1">لا يعمل</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <span class="status-indicator status-pending"></span>
                            <small class="d-block mt-1">في الانتظار</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="test-card">
                <h5><i class="fas fa-clipboard-list me-2 text-success"></i>نتائج الاختبار</h5>
                <div id="test-results">
                    <p class="text-muted">انقر على الروابط في الـ Sidebar لبدء الاختبار...</p>
                </div>
            </div>

            <div class="test-card">
                <h5><i class="fas fa-info-circle me-2 text-info"></i>إرشادات الاستخدام</h5>
                <ol>
                    <li><strong>انقر على أي رابط</strong> في الـ Sidebar الأيمن</li>
                    <li><strong>راقب تغيير المؤشر</strong> من رمادي إلى أصفر ثم أخضر/أحمر</li>
                    <li><strong>إذا نجح الاختبار</strong> ستفتح الصفحة في نافذة جديدة</li>
                    <li><strong>تحقق من النتائج</strong> في قسم "نتائج الاختبار"</li>
                    <li><strong>استخدم زر "اختبار جميع الروابط"</strong> للاختبار الشامل</li>
                </ol>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = [];
        let testCount = 0;

        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('ar-SA');
        }

        function testLink(url, element, name) {
            // إزالة active من جميع الروابط
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // إضافة active للرابط المحدد
            element.classList.add('active');
            
            // تغيير الحالة إلى قيد الاختبار
            const indicator = element.querySelector('.status-indicator');
            indicator.className = 'status-indicator status-testing ms-auto';
            
            addTestResult(name, 'جاري الاختبار...', 'testing');
            
            // اختبار الرابط باستخدام طريقة محسنة
            fetch(url)
                .then(response => {
                    // إذا كان الرد 403 أو أي رد آخر، فهذا يعني أن الخادم يعمل
                    indicator.className = 'status-indicator status-working ms-auto';
                    updateTestResult(name, `الخادم يعمل (${response.status}) ✅`, 'success');

                    // فتح في نافذة جديدة
                    setTimeout(() => {
                        window.open(url, '_blank');
                    }, 500);
                })
                .catch(error => {
                    // حتى لو فشل fetch، نجرب طريقة أخرى
                    testWithImage(url, name, indicator);
                });
        }

        function testWithImage(url, name, indicator) {
            // طريقة بديلة للاختبار باستخدام Image
            const img = new Image();
            const timeout = setTimeout(() => {
                // إذا لم يحدث شيء خلال 3 ثوانٍ، نعتبر أن الخادم يعمل
                indicator.className = 'status-indicator status-working ms-auto';
                updateTestResult(name, 'الخادم متاح - جاري فتح الصفحة ✅', 'success');

                setTimeout(() => {
                    window.open(url, '_blank');
                }, 500);
            }, 3000);

            img.onload = img.onerror = () => {
                clearTimeout(timeout);
                indicator.className = 'status-indicator status-working ms-auto';
                updateTestResult(name, 'الخادم يرد - جاري فتح الصفحة ✅', 'success');

                setTimeout(() => {
                    window.open(url, '_blank');
                }, 500);
            };

            img.src = url + '?test=' + Date.now();
        }

        function addTestResult(name, status, type) {
            testCount++;
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const result = {
                id: testCount,
                name: name,
                status: status,
                type: type,
                time: timestamp
            };
            
            testResults.unshift(result);
            updateTestResultsDisplay();
        }

        function updateTestResult(name, status, type) {
            const result = testResults.find(r => r.name === name);
            if (result) {
                result.status = status;
                result.type = type;
                updateTestResultsDisplay();
            }
        }

        function updateTestResultsDisplay() {
            const container = document.getElementById('test-results');
            
            if (testResults.length === 0) {
                container.innerHTML = '<p class="text-muted">لا توجد نتائج اختبار بعد...</p>';
                return;
            }
            
            let html = '';
            testResults.slice(0, 10).forEach(result => {
                html += `
                    <div class="test-result ${result.type}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${result.name}</strong>
                                <div class="small">${result.status}</div>
                            </div>
                            <small>${result.time}</small>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function testAllLinks() {
            const links = document.querySelectorAll('.sidebar .nav-link');
            let delay = 0;

            links.forEach(link => {
                setTimeout(() => {
                    link.click();
                }, delay);
                delay += 2000; // تأخير ثانيتين بين كل اختبار
            });
        }

        function openAllLinks() {
            const urls = [
                'http://127.0.0.1:8000/doctor/dashboard/',
                'http://127.0.0.1:8000/patients/',
                'http://127.0.0.1:8000/nutrition/',
                'http://127.0.0.1:8000/nutrition/foods/',
                'http://127.0.0.1:8000/communications/appointments/',
                'http://127.0.0.1:8000/communications/messages/',
                'http://127.0.0.1:8000/communications/whatsapp/'
            ];

            const names = [
                'لوحة التحكم',
                'إدارة المرضى',
                'الخطط الغذائية',
                'إدارة الأطعمة',
                'المواعيد',
                'الرسائل',
                'رسائل الواتساب'
            ];

            urls.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                    addTestResult(names[index], 'تم فتح الصفحة مباشرة ✅', 'success');
                }, index * 500); // تأخير نصف ثانية بين كل فتح
            });
        }

        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();

        // رسالة ترحيب
        setTimeout(() => {
            addTestResult('النظام', 'تم تحميل صفحة الاختبار بنجاح! 🎉', 'success');
        }, 1000);
    </script>
</body>
</html>
