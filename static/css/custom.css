/* Custom CSS for Clinic Management System */

/* Root variables for consistent theming */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    --border-radius: 0.75rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.3s ease;
}

/* Typography */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: var(--gradient-primary);
    color: white;
    border: none;
}

.stats-card.success {
    background: var(--gradient-success);
}

.stats-card.warning {
    background: var(--gradient-warning);
}

.stats-card.info {
    background: var(--gradient-info);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: var(--gradient-primary);
}

.btn-success {
    background: var(--gradient-success);
}

.btn-warning {
    background: var(--gradient-warning);
}

.btn-info {
    background: var(--gradient-info);
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Sidebar */
.sidebar {
    background: white;
    box-shadow: var(--box-shadow);
    min-height: calc(100vh - 76px);
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 1rem 1.5rem;
    border-radius: 0;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-left-color: var(--primary-color);
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%);
    color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.05) 100%);
    color: #055160;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Language selector */
.dropdown-item-form {
    margin: 0;
    padding: 0;
}

.dropdown-item-form button {
    background: none;
    border: none;
    width: 100%;
    text-align: right;
    padding: 0.5rem 1rem;
    color: var(--dark-color);
    transition: var(--transition);
    font-weight: 500;
}

.dropdown-item-form button:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item-form button.active {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .stats-number {
        font-size: 1.75rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Custom utilities */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

.bg-gradient-info {
    background: var(--gradient-info);
}
