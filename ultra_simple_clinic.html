<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام العيادات</title>
    <style>
        body {
            margin: 0;
            font-family: Arial;
            direction: rtl;
        }
        
        .container {
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .menu-btn {
            display: block;
            width: 100%;
            color: white;
            padding: 15px 20px;
            border: none;
            background: none;
            text-align: right;
            cursor: pointer;
            font-size: 16px;
        }
        
        .menu-btn:hover {
            background: #34495e;
        }
        
        .menu-btn.active {
            background: #3498db;
        }
        
        .content {
            flex: 1;
            padding: 30px;
            background: #f5f5f5;
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
        }
        
        .page.active {
            display: block;
        }
        
        .alert {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
        }
        
        .card:hover {
            background: #2980b9;
        }
        
        .card-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .money-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .money-card {
            background: #27ae60;
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .money-card.red {
            background: #e74c3c;
        }
        
        .money-card.orange {
            background: #f39c12;
        }
        
        .money-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .currency {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <button class="menu-btn active" onclick="showPage('dashboard')">
                📊 لوحة التحكم
            </button>
            
            <button class="menu-btn" onclick="showPage('patients')">
                👥 إدارة المرضى
            </button>
            
            <button class="menu-btn" onclick="showPage('nutrition')">
                🍽️ الخطط الغذائية
            </button>
            
            <button class="menu-btn" onclick="showPage('prescriptions')">
                💊 الوصفات الطبية
            </button>
            
            <button class="menu-btn" onclick="showPage('accounting')">
                💰 إدارة الحسابات
            </button>
        </div>
        
        <div class="content">
            <div id="dashboard" class="page active">
                <h1>📊 لوحة التحكم</h1>
                <div class="alert">
                    ✅ مرحباً بك! الـ Sidebar يعمل بشكل مثالي!
                </div>
                
                <div class="cards">
                    <div class="card" onclick="alert('المرضى: 25 مريض')">
                        <div class="card-number">25</div>
                        <div>إجمالي المرضى</div>
                    </div>
                    <div class="card" onclick="alert('الخطط: 12 خطة')">
                        <div class="card-number">12</div>
                        <div>خطط نشطة</div>
                    </div>
                    <div class="card" onclick="alert('المواعيد: 8 مواعيد')">
                        <div class="card-number">8</div>
                        <div>مواعيد اليوم</div>
                    </div>
                    <div class="card" onclick="alert('الرسائل: 45 رسالة')">
                        <div class="card-number">45</div>
                        <div>رسائل جديدة</div>
                    </div>
                </div>
                
                <h3>🎯 ميزات النظام:</h3>
                <ul>
                    <li>✅ إدارة شاملة للمرضى</li>
                    <li>✅ كتابة الوصفات الطبية</li>
                    <li>✅ نظام محاسبي بالدينار العراقي</li>
                    <li>✅ إدارة الخطط الغذائية</li>
                </ul>
            </div>
            
            <div id="patients" class="page">
                <h1>👥 إدارة المرضى</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>
                
                <h3>🔧 الوظائف المتاحة:</h3>
                <ul>
                    <li>إضافة مرضى جدد</li>
                    <li>تعديل بيانات المرضى</li>
                    <li>عرض التاريخ الطبي</li>
                    <li>متابعة تقدم المرضى</li>
                </ul>
                
                <button class="btn btn-success" onclick="alert('إضافة مريض جديد')">
                    ➕ إضافة مريض جديد
                </button>
                <button class="btn" onclick="alert('عرض قائمة المرضى')">
                    📋 عرض قائمة المرضى
                </button>
            </div>
            
            <div id="nutrition" class="page">
                <h1>🍽️ الخطط الغذائية</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>
                
                <div class="cards">
                    <div class="card" style="background: #e74c3c;" onclick="alert('خطط إنقاص الوزن: 12 خطة')">
                        <div class="card-number">12</div>
                        <div>خطط إنقاص الوزن</div>
                    </div>
                    <div class="card" style="background: #27ae60;" onclick="alert('خطط زيادة الوزن: 8 خطط')">
                        <div class="card-number">8</div>
                        <div>خطط زيادة الوزن</div>
                    </div>
                    <div class="card" style="background: #f39c12;" onclick="alert('خطط الحفاظ على الوزن: 5 خطط')">
                        <div class="card-number">5</div>
                        <div>خطط الحفاظ على الوزن</div>
                    </div>
                </div>
                
                <button class="btn btn-success" onclick="alert('إنشاء خطة جديدة')">
                    ➕ إنشاء خطة جديدة
                </button>
            </div>
            
            <div id="prescriptions" class="page">
                <h1>💊 الوصفات الطبية</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>
                
                <div class="cards">
                    <div class="card" onclick="alert('وصفات اليوم: 8 وصفات')">
                        <div class="card-number">8</div>
                        <div>وصفات اليوم</div>
                    </div>
                    <div class="card" onclick="alert('إجمالي الوصفات: 156 وصفة')">
                        <div class="card-number">156</div>
                        <div>إجمالي الوصفات</div>
                    </div>
                    <div class="card" onclick="alert('وصفات مطبوعة: 142 وصفة')">
                        <div class="card-number">142</div>
                        <div>وصفات مطبوعة</div>
                    </div>
                </div>
                
                <button class="btn btn-success" onclick="alert('كتابة وصفة جديدة')">
                    📝 كتابة وصفة جديدة
                </button>
                <button class="btn" onclick="printPrescription()">
                    🖨️ طباعة وصفة تجريبية
                </button>
            </div>
            
            <div id="accounting" class="page">
                <h1>💰 إدارة الحسابات</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>
                
                <div class="money-cards">
                    <div class="money-card">
                        <h3>💚 الإيرادات</h3>
                        <div class="money-amount">58,500,000</div>
                        <div class="currency">د.ع</div>
                    </div>
                    <div class="money-card red">
                        <h3>💸 المصروفات</h3>
                        <div class="money-amount">24,050,000</div>
                        <div class="currency">د.ع</div>
                    </div>
                    <div class="money-card orange">
                        <h3>📈 صافي الربح</h3>
                        <div class="money-amount">34,450,000</div>
                        <div class="currency">د.ع</div>
                    </div>
                </div>
                
                <h3>💼 الخدمات المالية:</h3>
                <ul>
                    <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                    <li>إدارة الفواتير والمدفوعات</li>
                    <li>تقارير مالية شهرية</li>
                    <li>حساب الضرائب المستحقة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            console.log('تم النقر على:', pageId);
            
            // إخفاء جميع الصفحات
            var pages = document.getElementsByClassName('page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }
            
            // إزالة active من جميع الأزرار
            var buttons = document.getElementsByClassName('menu-btn');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }
            
            // إظهار الصفحة المحددة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('تم عرض الصفحة:', pageId);
            }
            
            // إضافة active للزر المحدد
            event.target.classList.add('active');
            
            // رسالة تأكيد
            alert('✅ تم الانتقال إلى: ' + getPageName(pageId));
        }
        
        function getPageName(pageId) {
            var names = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'prescriptions': 'الوصفات الطبية',
                'accounting': 'إدارة الحسابات'
            };
            return names[pageId] || pageId;
        }
        
        function printPrescription() {
            var printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body { font-family: Arial; padding: 20px; direction: rtl; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .patient { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                        .medicine { border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>🏥 عيادة التغذية العلاجية</h2>
                        <p>د. [اسم الطبيب] - أخصائي التغذية</p>
                    </div>
                    
                    <div class="patient">
                        <strong>المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>التاريخ:</strong> ${new Date().toLocaleDateString()}<br>
                        <strong>التشخيص:</strong> نقص فيتامين د
                    </div>
                    
                    <h3>الأدوية المطلوبة:</h3>
                    <div class="medicine">
                        <strong>1. فيتامين د3 1000 وحدة</strong><br>
                        الجرعة: حبة واحدة يومياً<br>
                        المدة: 3 أشهر
                    </div>
                    
                    <div class="medicine">
                        <strong>2. كالسيوم 500 ملغ</strong><br>
                        الجرعة: حبة واحدة مساءً<br>
                        المدة: شهر واحد
                    </div>
                    
                    <p><strong>التعليمات:</strong> التعرض لأشعة الشمس، شرب الحليب</p>
                    
                    <div style="margin-top: 50px;">
                        <p>توقيع الطبيب: _______________</p>
                    </div>
                    
                    <script>window.print();</script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
        
        console.log('✅ تم تحميل النظام بنجاح!');
        alert('🎉 مرحباً بك في نظام إدارة العيادات!\n\nالـ Sidebar يعمل الآن!\n\nجرب النقر على الأزرار.');
    </script>
</body>
</html>
