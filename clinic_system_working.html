<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            right: 0;
            top: 0;
        }
        
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            padding: 0 20px;
            color: #3498db;
            border-bottom: 2px solid #34495e;
            padding-bottom: 15px;
        }
        
        .nav-item {
            display: block;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            font-size: 16px;
        }
        
        .nav-item:hover {
            background: #34495e;
            padding-right: 30px;
        }
        
        .nav-item.active {
            background: #3498db;
            border-right: 4px solid #2980b9;
        }
        
        .content {
            margin-right: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
            font-weight: bold;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .stat-card:hover::before {
            animation: shine 0.6s ease-in-out;
            opacity: 1;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .stat-card.patients {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.plans {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card.appointments {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card.messages {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: countUp 2s ease-out;
        }

        .stat-label {
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: slideRight 3s infinite;
        }

        @keyframes slideRight {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .dashboard-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .dashboard-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .financial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .financial-card {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .financial-card.expense {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .financial-card.profit {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .money-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .currency {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .category-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .category-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <button class="nav-item active" onclick="showPage('dashboard', this)">
                📊 لوحة التحكم
            </button>
            
            <button class="nav-item" onclick="showPage('patients', this)">
                👥 إدارة المرضى
            </button>
            
            <button class="nav-item" onclick="showPage('nutrition', this)">
                🍽️ الخطط الغذائية
            </button>
            
            <button class="nav-item" onclick="showPage('foods', this)">
                🍎 إدارة الأطعمة
            </button>
            
            <button class="nav-item" onclick="showPage('appointments', this)">
                📅 المواعيد
            </button>
            
            <button class="nav-item" onclick="showPage('messages', this)">
                ✉️ الرسائل
            </button>
            
            <button class="nav-item" onclick="showPage('prescriptions', this)">
                💊 الوصفات الطبية
            </button>

            <button class="nav-item" onclick="showPage('whatsapp', this)">
                📱 رسائل الواتساب
            </button>

            <button class="nav-item" onclick="showPage('accounting', this)">
                💰 إدارة الحسابات
            </button>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Dashboard -->
            <div id="dashboard" class="section active">
                <div class="dashboard-header">
                    <div class="dashboard-title">🏥 لوحة التحكم الذكية</div>
                    <div class="dashboard-subtitle">مرحباً بك في نظام إدارة العيادات المتطور</div>
                </div>

                <div class="success-msg" style="background: linear-gradient(135deg, #d4edda, #c3e6cb); border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    ✨ النظام يعمل بكامل طاقته! جميع الأنظمة متصلة ومتزامنة
                </div>

                <div class="stats-grid">
                    <div class="stat-card patients" onclick="animateCard(this); showMessage('المرضى')">
                        <div class="stat-icon">👥</div>
                        <div class="stat-number" id="patients-count">0</div>
                        <div class="stat-label">إجمالي المرضى</div>
                        <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.8;">+3 هذا الأسبوع</div>
                    </div>
                    <div class="stat-card plans" onclick="animateCard(this); showMessage('الخطط')">
                        <div class="stat-icon">🍽️</div>
                        <div class="stat-number" id="plans-count">0</div>
                        <div class="stat-label">خطط نشطة</div>
                        <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.8;">+2 خطة جديدة</div>
                    </div>
                    <div class="stat-card appointments" onclick="animateCard(this); showMessage('المواعيد')">
                        <div class="stat-icon">📅</div>
                        <div class="stat-number" id="appointments-count">0</div>
                        <div class="stat-label">مواعيد اليوم</div>
                        <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.8;">4 متبقية</div>
                    </div>
                    <div class="stat-card messages" onclick="animateCard(this); showMessage('الرسائل')">
                        <div class="stat-icon">💬</div>
                        <div class="stat-number" id="messages-count">0</div>
                        <div class="stat-label">رسائل جديدة</div>
                        <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.8;">12 غير مقروءة</div>
                    </div>
                </div>

                <div class="card" style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">🎯 ميزات النظام المتطورة:</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">👥 إدارة المرضى</h4>
                            <p style="color: #6c757d; line-height: 1.6;">إدارة شاملة للمرضى والملفات الطبية مع نظام متابعة متقدم</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #f5576c; margin-bottom: 10px;">🍽️ الخطط الغذائية</h4>
                            <p style="color: #6c757d; line-height: 1.6;">إنشاء وإدارة خطط غذائية مخصصة مع حساب السعرات والقيم الغذائية</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #00f2fe; margin-bottom: 10px;">💰 النظام المالي</h4>
                            <p style="color: #6c757d; line-height: 1.6;">نظام محاسبي متكامل بالدينار العراقي مع تقارير مالية مفصلة</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #38f9d7; margin-bottom: 10px;">📱 تكامل الواتساب</h4>
                            <p style="color: #6c757d; line-height: 1.6;">إرسال الرسائل والتذكيرات للمرضى عبر الواتساب مباشرة</p>
                        </div>
                    </div>
                </div>

                <div class="card" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <h3 style="margin-bottom: 20px;">📊 إحصائيات سريعة</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; text-align: center;">
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">98%</div>
                            <div style="opacity: 0.9;">معدل الرضا</div>
                        </div>
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">156</div>
                            <div style="opacity: 0.9;">استشارة هذا الشهر</div>
                        </div>
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">89%</div>
                            <div style="opacity: 0.9;">نجاح الخطط</div>
                        </div>
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">24/7</div>
                            <div style="opacity: 0.9;">دعم متواصل</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Patients -->
            <div id="patients" class="section">
                <div class="card">
                    <h1>👥 إدارة المرضى</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                    </div>
                    
                    <h3>🔧 الوظائف المتاحة:</h3>
                    <ul style="line-height: 2;">
                        <li>إضافة مرضى جدد مع جميع البيانات</li>
                        <li>تعديل وتحديث بيانات المرضى</li>
                        <li>عرض التاريخ الطبي الكامل</li>
                        <li>متابعة تقدم المرضى</li>
                        <li>إدارة المواعيد والزيارات</li>
                    </ul>
                    
                    <button class="btn btn-success" onclick="showMessage('إضافة مريض جديد')">
                        ➕ إضافة مريض جديد
                    </button>
                    <button class="btn" onclick="showMessage('عرض قائمة المرضى')">
                        📋 عرض قائمة المرضى
                    </button>
                    <button class="btn" onclick="showMessage('البحث عن مريض')">
                        🔍 البحث عن مريض
                    </button>
                </div>
            </div>
            
            <!-- Nutrition -->
            <div id="nutrition" class="section">
                <div class="card">
                    <h1>🍽️ الخطط الغذائية</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                    </div>
                    
                    <div class="category-grid">
                        <div class="category-item" onclick="showMessage('خطط إنقاص الوزن')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">⚖️</div>
                            <h4>خطط إنقاص الوزن</h4>
                            <p>12 خطة نشطة</p>
                        </div>
                        <div class="category-item" onclick="showMessage('خطط زيادة الوزن')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">📈</div>
                            <h4>خطط زيادة الوزن</h4>
                            <p>8 خطط نشطة</p>
                        </div>
                        <div class="category-item" onclick="showMessage('خطط الحفاظ على الوزن')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">⚖️</div>
                            <h4>خطط الحفاظ على الوزن</h4>
                            <p>5 خطط نشطة</p>
                        </div>
                    </div>
                    
                    <button class="btn btn-success" onclick="showMessage('إنشاء خطة غذائية جديدة')">
                        ➕ إنشاء خطة جديدة
                    </button>
                </div>
            </div>
            
            <!-- Foods -->
            <div id="foods" class="section">
                <div class="card">
                    <h1>🍎 إدارة الأطعمة</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم إدارة الأطعمة بنجاح!
                    </div>
                    
                    <div class="category-grid">
                        <div class="category-item" onclick="showMessage('الحبوب والنشويات')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">🌾</div>
                            <h4>الحبوب والنشويات</h4>
                            <p>45 صنف</p>
                        </div>
                        <div class="category-item" onclick="showMessage('البروتينات')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">🥩</div>
                            <h4>البروتينات</h4>
                            <p>32 صنف</p>
                        </div>
                        <div class="category-item" onclick="showMessage('الخضروات والفواكه')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">🥕</div>
                            <h4>الخضروات والفواكه</h4>
                            <p>78 صنف</p>
                        </div>
                        <div class="category-item" onclick="showMessage('الألبان ومنتجاتها')">
                            <div style="font-size: 3rem; margin-bottom: 10px;">🥛</div>
                            <h4>الألبان ومنتجاتها</h4>
                            <p>25 صنف</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Prescriptions -->
            <div id="prescriptions" class="section">
                <div class="card">
                    <h1>💊 الوصفات الطبية</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                    </div>

                    <div class="stats-grid" style="margin-bottom: 30px;">
                        <div class="stat-card" style="background: linear-gradient(135deg, #667eea, #764ba2);" onclick="showMessage('وصفات اليوم')">
                            <div class="stat-icon">📝</div>
                            <div class="stat-number">8</div>
                            <div class="stat-label">وصفات اليوم</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #f093fb, #f5576c);" onclick="showMessage('إجمالي الوصفات')">
                            <div class="stat-icon">📋</div>
                            <div class="stat-number">156</div>
                            <div class="stat-label">إجمالي الوصفات</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #4facfe, #00f2fe);" onclick="showMessage('وصفات مطبوعة')">
                            <div class="stat-icon">🖨️</div>
                            <div class="stat-number">142</div>
                            <div class="stat-label">وصفات مطبوعة</div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div class="category-item" onclick="showCreatePrescriptionForm()">
                            <div style="font-size: 3rem; margin-bottom: 15px; color: #28a745;">📝</div>
                            <h4 style="color: #28a745;">كتابة وصفة جديدة</h4>
                            <p>إنشاء وصفة طبية جديدة للمريض</p>
                            <button class="btn btn-success" style="margin-top: 10px;">
                                ➕ وصفة جديدة
                            </button>
                        </div>

                        <div class="category-item" onclick="showPrescriptionsList()">
                            <div style="font-size: 3rem; margin-bottom: 15px; color: #007bff;">📚</div>
                            <h4 style="color: #007bff;">أرشيف الوصفات</h4>
                            <p>عرض وإدارة الوصفات السابقة</p>
                            <button class="btn" style="margin-top: 10px;">
                                📋 عرض الأرشيف
                            </button>
                        </div>

                        <div class="category-item" onclick="printSamplePrescription()">
                            <div style="font-size: 3rem; margin-bottom: 15px; color: #17a2b8;">🖨️</div>
                            <h4 style="color: #17a2b8;">طباعة وصفة تجريبية</h4>
                            <p>طباعة نموذج وصفة للاختبار</p>
                            <button class="btn" style="background: #17a2b8; color: white; margin-top: 10px;">
                                🖨️ طباعة تجريبية
                            </button>
                        </div>
                    </div>

                    <!-- نموذج كتابة وصفة جديدة -->
                    <div id="prescriptionForm" style="display: none; background: #f8f9fa; padding: 30px; border-radius: 15px; margin-top: 20px;">
                        <h3 style="color: #2c3e50; margin-bottom: 20px;">📝 كتابة وصفة طبية جديدة</h3>

                        <form onsubmit="createPrescription(event)">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المريض:</label>
                                    <select id="prescriptionPatient" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" required>
                                        <option value="">اختر المريض</option>
                                        <option value="أحمد محمد">أحمد محمد - 35 سنة</option>
                                        <option value="فاطمة علي">فاطمة علي - 28 سنة</option>
                                        <option value="سارة أحمد">سارة أحمد - 32 سنة</option>
                                        <option value="محمد حسن">محمد حسن - 45 سنة</option>
                                        <option value="نور الدين">نور الدين - 29 سنة</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">التشخيص:</label>
                                    <input type="text" id="prescriptionDiagnosis" placeholder="مثال: نقص فيتامين د، ضعف عام" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" required>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 10px; font-weight: bold;">الأدوية:</label>
                                <div id="medicinesList">
                                    <div class="medicine-item" style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px; border: 1px solid #ddd;">
                                        <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 15px; align-items: center;">
                                            <input type="text" placeholder="اسم الدواء" class="medicine-name" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                                            <input type="text" placeholder="الجرعة" class="medicine-dosage" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                                            <input type="text" placeholder="المدة" class="medicine-duration" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                                            <button type="button" onclick="removeMedicine(this)" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">🗑️</button>
                                        </div>
                                        <textarea placeholder="تعليمات الاستخدام" class="medicine-instructions" style="width: 100%; margin-top: 10px; padding: 8px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;" rows="2"></textarea>
                                    </div>
                                </div>
                                <button type="button" onclick="addMedicine()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">➕ إضافة دواء آخر</button>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">تعليمات عامة:</label>
                                <textarea id="prescriptionInstructions" rows="4" placeholder="تعليمات عامة للمريض..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
                            </div>

                            <div style="text-align: center;">
                                <button type="submit" class="btn btn-success" style="margin: 0 10px;">
                                    💾 حفظ الوصفة
                                </button>
                                <button type="button" onclick="printCurrentPrescription()" class="btn" style="background: #17a2b8; color: white; margin: 0 10px;">
                                    🖨️ حفظ وطباعة
                                </button>
                                <button type="button" onclick="hidePrescriptionForm()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 0 10px;">
                                    ❌ إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- قائمة الوصفات السابقة -->
                    <div id="prescriptionsList" style="display: none; margin-top: 20px;">
                        <h3 style="color: #2c3e50; margin-bottom: 20px;">📚 أرشيف الوصفات</h3>
                        <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead style="background: #f8f9fa;">
                                    <tr>
                                        <th style="padding: 15px; text-align: right; border-bottom: 1px solid #dee2e6;">المريض</th>
                                        <th style="padding: 15px; text-align: right; border-bottom: 1px solid #dee2e6;">التشخيص</th>
                                        <th style="padding: 15px; text-align: right; border-bottom: 1px solid #dee2e6;">التاريخ</th>
                                        <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">أحمد محمد</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">نقص فيتامين د</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">2024-01-15</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                                            <button onclick="viewPrescription('أحمد محمد')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">👁️ عرض</button>
                                            <button onclick="printPrescription('أحمد محمد')" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">🖨️ طباعة</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">فاطمة علي</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">فقر الدم</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">2024-01-12</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                                            <button onclick="viewPrescription('فاطمة علي')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">👁️ عرض</button>
                                            <button onclick="printPrescription('فاطمة علي')" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">🖨️ طباعة</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">سارة أحمد</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">ارتفاع الكوليسترول</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">2024-01-10</td>
                                        <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                                            <button onclick="viewPrescription('سارة أحمد')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">👁️ عرض</button>
                                            <button onclick="printPrescription('سارة أحمد')" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin: 0 2px; cursor: pointer;">🖨️ طباعة</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="hidePrescriptionsList()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer;">
                                ❌ إغلاق الأرشيف
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Appointments -->
            <div id="appointments" class="section">
                <div class="card">
                    <h1>📅 المواعيد</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم المواعيد بنجاح!
                    </div>
                    
                    <h3>📋 مواعيد اليوم:</h3>
                    <ul style="line-height: 2;">
                        <li>🕘 9:00 ص - أحمد محمد (استشارة غذائية)</li>
                        <li>🕙 10:30 ص - فاطمة علي (متابعة خطة)</li>
                        <li>🕐 1:00 م - سارة أحمد (قياس الوزن)</li>
                        <li>🕕 6:00 م - محمد حسن (استشارة جديدة)</li>
                    </ul>
                    
                    <button class="btn btn-success" onclick="showMessage('حجز موعد جديد')">
                        ➕ حجز موعد جديد
                    </button>
                </div>
            </div>
            
            <!-- Messages -->
            <div id="messages" class="section">
                <div class="card">
                    <h1>✉️ الرسائل</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم الرسائل بنجاح!
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                            <div class="stat-number">45</div>
                            <div>رسائل جديدة</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                            <div class="stat-number">128</div>
                            <div>رسائل مرسلة</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                            <div class="stat-number">12</div>
                            <div>رسائل مهمة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- WhatsApp -->
            <div id="whatsapp" class="section">
                <div class="card">
                    <h1>📱 رسائل الواتساب</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم رسائل الواتساب بنجاح!
                    </div>
                    
                    <h3>📊 إحصائيات الرسائل:</h3>
                    <div class="stats-grid">
                        <div class="stat-card" style="background: linear-gradient(135deg, #25d366, #128c7e);">
                            <div class="stat-number">156</div>
                            <div>رسائل مرسلة</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #34b7f1, #0088cc);">
                            <div class="stat-number">142</div>
                            <div>رسائل مسلمة</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #128c7e, #075e54);">
                            <div class="stat-number">98</div>
                            <div>رسائل مقروءة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Accounting -->
            <div id="accounting" class="section">
                <div class="card">
                    <h1>💰 إدارة الحسابات</h1>
                    <div class="success-msg">
                        ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                    </div>
                    
                    <div class="financial-grid">
                        <div class="financial-card">
                            <h3>💚 إجمالي الإيرادات</h3>
                            <div class="money-amount">58,500,000</div>
                            <div class="currency">د.ع</div>
                            <small>+15% هذا الشهر</small>
                        </div>
                        <div class="financial-card expense">
                            <h3>💸 إجمالي المصروفات</h3>
                            <div class="money-amount">24,050,000</div>
                            <div class="currency">د.ع</div>
                            <small>-5% عن الشهر الماضي</small>
                        </div>
                        <div class="financial-card profit">
                            <h3>📈 صافي الربح</h3>
                            <div class="money-amount">34,450,000</div>
                            <div class="currency">د.ع</div>
                            <small>+25% نمو</small>
                        </div>
                    </div>
                    
                    <h3>💼 الخدمات المالية:</h3>
                    <ul style="line-height: 2;">
                        <li>تتبع الإيرادات والمصروفات بالدينار العراقي</li>
                        <li>إدارة الفواتير والمدفوعات</li>
                        <li>تقارير مالية شهرية ومفصلة</li>
                        <li>حساب الضرائب المستحقة</li>
                        <li>تحليل الأداء المالي</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId, element) {
            console.log('تم النقر على:', pageId);
            
            // إخفاء جميع الأقسام
            var sections = document.querySelectorAll('.section');
            sections.forEach(function(section) {
                section.classList.remove('active');
            });
            
            // إزالة active من جميع الأزرار
            var navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(function(item) {
                item.classList.remove('active');
            });
            
            // إظهار القسم المحدد
            var targetSection = document.getElementById(pageId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // إضافة active للزر المحدد
            if (element) {
                element.classList.add('active');
            }
            
            // رسالة تأكيد
            showMessage('تم الانتقال إلى قسم: ' + getPageName(pageId));
        }
        
        function getPageName(pageId) {
            var names = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'foods': 'إدارة الأطعمة',
                'appointments': 'المواعيد',
                'messages': 'الرسائل',
                'whatsapp': 'رسائل الواتساب',
                'accounting': 'إدارة الحسابات'
            };
            return names[pageId] || pageId;
        }
        
        function showMessage(message) {
            // إنشاء رسالة منبثقة جميلة
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                animation: slideInRight 0.5s ease;
                max-width: 300px;
            `;
            notification.innerHTML = `✨ ${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.5s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);

            console.log('رسالة:', message);
        }

        function animateCard(card) {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'translateY(-10px) scale(1.05)';
            }, 100);
            setTimeout(() => {
                card.style.transform = 'translateY(-10px) scale(1.05)';
            }, 300);
        }

        function animateNumber(elementId, targetNumber, duration = 2000) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const startNumber = 0;
            const increment = targetNumber / (duration / 16);
            let currentNumber = startNumber;

            const timer = setInterval(() => {
                currentNumber += increment;
                if (currentNumber >= targetNumber) {
                    currentNumber = targetNumber;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentNumber);
            }, 16);
        }

        function startDashboardAnimations() {
            // تحريك الأرقام
            setTimeout(() => animateNumber('patients-count', 25, 2000), 500);
            setTimeout(() => animateNumber('plans-count', 12, 1800), 700);
            setTimeout(() => animateNumber('appointments-count', 8, 1600), 900);
            setTimeout(() => animateNumber('messages-count', 45, 2200), 1100);

            // تأثيرات إضافية للبطاقات
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = 'float 3s ease-in-out infinite';
                    card.style.animationDelay = `${index * 0.2}s`;
                }, 2000 + (index * 200));
            });
        }

        function updateRealTimeData() {
            // محاكاة تحديث البيانات في الوقت الفعلي
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.title = `نظام العيادات - ${timeString}`;

            // تأثير وميض خفيف للبطاقات كل دقيقة
            if (now.getSeconds() === 0) {
                const cards = document.querySelectorAll('.stat-card');
                cards.forEach(card => {
                    card.style.boxShadow = '0 20px 40px rgba(255,255,255,0.3)';
                    setTimeout(() => {
                        card.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
                    }, 1000);
                });
            }
        }

        // إضافة CSS للرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // وظائف الوصفات الطبية
        function showCreatePrescriptionForm() {
            document.getElementById('prescriptionForm').style.display = 'block';
            document.getElementById('prescriptionsList').style.display = 'none';
            document.getElementById('prescriptionForm').scrollIntoView({ behavior: 'smooth' });
            showMessage('تم فتح نموذج كتابة وصفة جديدة');
        }

        function hidePrescriptionForm() {
            document.getElementById('prescriptionForm').style.display = 'none';
            document.querySelector('#prescriptionForm form').reset();
            showMessage('تم إلغاء كتابة الوصفة');
        }

        function showPrescriptionsList() {
            document.getElementById('prescriptionsList').style.display = 'block';
            document.getElementById('prescriptionForm').style.display = 'none';
            document.getElementById('prescriptionsList').scrollIntoView({ behavior: 'smooth' });
            showMessage('تم فتح أرشيف الوصفات');
        }

        function hidePrescriptionsList() {
            document.getElementById('prescriptionsList').style.display = 'none';
            showMessage('تم إغلاق أرشيف الوصفات');
        }

        function addMedicine() {
            const medicinesList = document.getElementById('medicinesList');
            const newMedicine = document.createElement('div');
            newMedicine.className = 'medicine-item';
            newMedicine.style.cssText = 'background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px; border: 1px solid #ddd;';
            newMedicine.innerHTML = `
                <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 15px; align-items: center;">
                    <input type="text" placeholder="اسم الدواء" class="medicine-name" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                    <input type="text" placeholder="الجرعة" class="medicine-dosage" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                    <input type="text" placeholder="المدة" class="medicine-duration" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;" required>
                    <button type="button" onclick="removeMedicine(this)" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">🗑️</button>
                </div>
                <textarea placeholder="تعليمات الاستخدام" class="medicine-instructions" style="width: 100%; margin-top: 10px; padding: 8px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;" rows="2"></textarea>
            `;
            medicinesList.appendChild(newMedicine);
            showMessage('تم إضافة دواء جديد');
        }

        function removeMedicine(button) {
            const medicineItem = button.closest('.medicine-item');
            const medicinesList = document.getElementById('medicinesList');
            if (medicinesList.children.length > 1) {
                medicineItem.remove();
                showMessage('تم حذف الدواء');
            } else {
                showMessage('يجب أن تحتوي الوصفة على دواء واحد على الأقل');
            }
        }

        function createPrescription(event) {
            event.preventDefault();

            const patient = document.getElementById('prescriptionPatient').value;
            const diagnosis = document.getElementById('prescriptionDiagnosis').value;
            const instructions = document.getElementById('prescriptionInstructions').value;

            const medicines = [];
            const medicineItems = document.querySelectorAll('.medicine-item');
            medicineItems.forEach(item => {
                const name = item.querySelector('.medicine-name').value;
                const dosage = item.querySelector('.medicine-dosage').value;
                const duration = item.querySelector('.medicine-duration').value;
                const medicineInstructions = item.querySelector('.medicine-instructions').value;

                if (name && dosage && duration) {
                    medicines.push({ name, dosage, duration, instructions: medicineInstructions });
                }
            });

            if (medicines.length === 0) {
                showMessage('يجب إضافة دواء واحد على الأقل');
                return;
            }

            console.log('بيانات الوصفة:', { patient, diagnosis, medicines, instructions });

            showMessage('جاري حفظ الوصفة...');
            setTimeout(() => {
                hidePrescriptionForm();
                showMessage(`تم حفظ الوصفة للمريض ${patient} بنجاح!`);
            }, 1500);
        }

        function printCurrentPrescription() {
            const patient = document.getElementById('prescriptionPatient').value;
            const diagnosis = document.getElementById('prescriptionDiagnosis').value;
            const instructions = document.getElementById('prescriptionInstructions').value;

            if (!patient || !diagnosis) {
                showMessage('يرجى ملء بيانات المريض والتشخيص أولاً');
                return;
            }

            const medicines = [];
            const medicineItems = document.querySelectorAll('.medicine-item');
            medicineItems.forEach(item => {
                const name = item.querySelector('.medicine-name').value;
                const dosage = item.querySelector('.medicine-dosage').value;
                const duration = item.querySelector('.medicine-duration').value;
                const medicineInstructions = item.querySelector('.medicine-instructions').value;

                if (name && dosage && duration) {
                    medicines.push({ name, dosage, duration, instructions: medicineInstructions });
                }
            });

            if (medicines.length === 0) {
                showMessage('يجب إضافة دواء واحد على الأقل');
                return;
            }

            printPrescriptionWithData(patient, diagnosis, medicines, instructions);
        }

        function printSamplePrescription() {
            const sampleData = {
                patient: 'أحمد محمد - 35 سنة',
                diagnosis: 'نقص فيتامين د وضعف عام',
                medicines: [
                    {
                        name: 'فيتامين د3 (Vitamin D3)',
                        dosage: '1000 وحدة دولية',
                        duration: '3 أشهر',
                        instructions: 'حبة واحدة يومياً مع الطعام'
                    },
                    {
                        name: 'كالسيوم (Calcium Carbonate)',
                        dosage: '500 ملغ',
                        duration: 'شهر واحد',
                        instructions: 'حبة واحدة مع الطعام مساءً'
                    }
                ],
                instructions: 'التعرض لأشعة الشمس صباحاً لمدة 15-20 دقيقة يومياً، شرب كمية كافية من الماء، المتابعة بعد شهر واحد'
            };

            printPrescriptionWithData(sampleData.patient, sampleData.diagnosis, sampleData.medicines, sampleData.instructions);
        }

        function printPrescriptionWithData(patient, diagnosis, medicines, instructions) {
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            const content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية - ${patient}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            line-height: 1.6;
                            direction: rtl;
                            background: white;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #2c3e50;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .clinic-name {
                            font-size: 28px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                        .doctor-info {
                            font-size: 16px;
                            color: #666;
                        }
                        .patient-info {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                            border-right: 5px solid #3498db;
                        }
                        .medicine {
                            background: #fff;
                            border: 2px solid #e9ecef;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 8px;
                            border-right: 4px solid #27ae60;
                        }
                        .medicine-name {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 8px;
                        }
                        .dosage {
                            color: #666;
                            margin: 5px 0;
                        }
                        .instructions {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .signature {
                            margin-top: 50px;
                            text-align: left;
                        }
                        .signature-line {
                            border-bottom: 2px solid #333;
                            width: 200px;
                            margin-bottom: 10px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="clinic-name">🏥 عيادة التغذية العلاجية</div>
                        <div class="doctor-info">د. [اسم الطبيب] - أخصائي التغذية العلاجية</div>
                        <div class="doctor-info">العنوان: [عنوان العيادة] | الهاتف: [رقم الهاتف]</div>
                    </div>

                    <div class="patient-info">
                        <strong>👤 بيانات المريض:</strong> ${patient}<br>
                        <strong>📅 تاريخ الوصفة:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                        <strong>🆔 رقم الوصفة:</strong> RX-${Math.floor(Math.random() * 100000)}<br>
                        <strong>🔍 التشخيص:</strong> ${diagnosis}
                    </div>

                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">💊 الأدوية المطلوبة:</h3>

                    ${medicines.map((medicine, index) => `
                        <div class="medicine">
                            <div class="medicine-name">${index + 1}. ${medicine.name}</div>
                            <div class="dosage"><strong>الجرعة:</strong> ${medicine.dosage}</div>
                            <div class="dosage"><strong>المدة:</strong> ${medicine.duration}</div>
                            ${medicine.instructions ? `<div class="dosage"><strong>تعليمات:</strong> ${medicine.instructions}</div>` : ''}
                        </div>
                    `).join('')}

                    ${instructions ? `
                        <div class="instructions">
                            <h4 style="color: #856404; margin-top: 0;">⚠️ التعليمات العامة:</h4>
                            <p style="margin: 10px 0;">${instructions}</p>
                        </div>
                    ` : ''}

                    <div class="signature">
                        <div>توقيع الطبيب:</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 14px; color: #666;">د. [اسم الطبيب]</div>
                        <div style="font-size: 12px; color: #999; margin-top: 20px;">
                            تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                        </div>
                    </div>

                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `;

            printWindow.document.write(content);
            printWindow.document.close();
            showMessage('تم فتح نافذة الطباعة');
        }

        function viewPrescription(patientName) {
            showMessage(`عرض وصفة المريض: ${patientName}`);
        }

        function printPrescription(patientName) {
            // بيانات وهمية للوصفات المحفوظة
            const prescriptionsData = {
                'أحمد محمد': {
                    diagnosis: 'نقص فيتامين د',
                    medicines: [
                        { name: 'فيتامين د3', dosage: '1000 وحدة', duration: '3 أشهر', instructions: 'مع الطعام' }
                    ],
                    instructions: 'التعرض للشمس يومياً'
                },
                'فاطمة علي': {
                    diagnosis: 'فقر الدم',
                    medicines: [
                        { name: 'حديد (Iron)', dosage: '65 ملغ', duration: '2 شهر', instructions: 'على معدة فارغة' }
                    ],
                    instructions: 'تناول الأطعمة الغنية بالحديد'
                },
                'سارة أحمد': {
                    diagnosis: 'ارتفاع الكوليسترول',
                    medicines: [
                        { name: 'أوميغا 3', dosage: '1000 ملغ', duration: '6 أشهر', instructions: 'مع الوجبات' }
                    ],
                    instructions: 'تجنب الدهون المشبعة'
                }
            };

            const data = prescriptionsData[patientName];
            if (data) {
                printPrescriptionWithData(patientName, data.diagnosis, data.medicines, data.instructions);
            } else {
                showMessage('لم يتم العثور على بيانات الوصفة');
            }
        }

        // تأكيد تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل نظام إدارة العيادات بنجاح!');
            console.log('جميع الوظائف جاهزة للاستخدام!');

            // بدء الرسوم المتحركة
            startDashboardAnimations();

            // تحديث البيانات كل ثانية
            setInterval(updateRealTimeData, 1000);

            // رسالة ترحيب
            setTimeout(() => {
                showMessage('مرحباً بك في نظام إدارة العيادات المتطور!');
            }, 1000);
        });
    </script>
</body>
</html>
