<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام العيادات</title>
    <style>
        body {
            margin: 0;
            font-family: Arial;
            direction: rtl;
        }
        
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 200px;
            height: 100%;
            background: #333;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar h3 {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .menu-btn {
            display: block;
            width: 100%;
            padding: 15px 20px;
            background: none;
            border: none;
            color: white;
            text-align: right;
            cursor: pointer;
            font-size: 16px;
        }
        
        .menu-btn:hover {
            background: #555;
        }
        
        .menu-btn.active {
            background: #007bff;
        }
        
        .content {
            margin-right: 200px;
            padding: 20px;
        }
        
        .page {
            display: none;
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .page.active {
            display: block;
        }
        
        .alert {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-box {
            background: #007bff;
            color: white;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
            flex: 1;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h3>🏥 نظام العيادات</h3>
        
        <button class="menu-btn active" onclick="showPage('page1', this)">
            📊 لوحة التحكم
        </button>
        
        <button class="menu-btn" onclick="showPage('page2', this)">
            👥 إدارة المرضى
        </button>
        
        <button class="menu-btn" onclick="showPage('page3', this)">
            💊 الوصفات الطبية
        </button>
        
        <button class="menu-btn" onclick="showPage('page4', this)">
            💰 إدارة الحسابات
        </button>
        
        <button class="menu-btn" onclick="showPage('page5', this)">
            🍎 الخطط الغذائية
        </button>
    </div>
    
    <div class="content">
        <div id="page1" class="page active">
            <h2>📊 لوحة التحكم</h2>
            <div class="alert">
                ✅ مرحباً بك! الـ Sidebar يعمل بشكل مثالي!
            </div>
            
            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number">25</div>
                    <div>المرضى</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">12</div>
                    <div>الخطط</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">8</div>
                    <div>الوصفات</div>
                </div>
            </div>
            
            <p>هذا هو قسم لوحة التحكم. يمكنك من هنا متابعة جميع أنشطة العيادة.</p>
        </div>
        
        <div id="page2" class="page">
            <h2>👥 إدارة المرضى</h2>
            <div class="alert">
                ✅ تم الانتقال إلى قسم إدارة المرضى!
            </div>
            
            <p>في هذا القسم يمكنك:</p>
            <ul>
                <li>إضافة مرضى جدد</li>
                <li>تعديل بيانات المرضى</li>
                <li>عرض التاريخ الطبي</li>
                <li>متابعة تقدم المرضى</li>
            </ul>
            
            <button class="btn">➕ إضافة مريض جديد</button>
            <button class="btn">📋 عرض قائمة المرضى</button>
        </div>
        
        <div id="page3" class="page">
            <h2>💊 الوصفات الطبية</h2>
            <div class="alert">
                ✅ تم الانتقال إلى قسم الوصفات الطبية!
            </div>
            
            <p>نظام متكامل لكتابة وإدارة الوصفات:</p>
            <ul>
                <li>كتابة وصفات طبية جديدة</li>
                <li>طباعة الوصفات</li>
                <li>حفظ وأرشفة الوصفات</li>
                <li>متابعة تاريخ الوصفات</li>
            </ul>
            
            <button class="btn" onclick="printPrescription()">🖨️ طباعة وصفة تجريبية</button>
            <button class="btn">📝 كتابة وصفة جديدة</button>
        </div>
        
        <div id="page4" class="page">
            <h2>💰 إدارة الحسابات</h2>
            <div class="alert">
                ✅ تم الانتقال إلى قسم إدارة الحسابات!
            </div>
            
            <div class="stats">
                <div class="stat-box" style="background: #28a745;">
                    <div class="stat-number">58,500,000</div>
                    <div>د.ع - الإيرادات</div>
                </div>
                <div class="stat-box" style="background: #dc3545;">
                    <div class="stat-number">24,050,000</div>
                    <div>د.ع - المصروفات</div>
                </div>
                <div class="stat-box" style="background: #ffc107;">
                    <div class="stat-number">34,450,000</div>
                    <div>د.ع - صافي الربح</div>
                </div>
            </div>
            
            <p>نظام مالي متكامل لإدارة حسابات العيادة بالدينار العراقي.</p>
        </div>
        
        <div id="page5" class="page">
            <h2>🍎 الخطط الغذائية</h2>
            <div class="alert">
                ✅ تم الانتقال إلى قسم الخطط الغذائية!
            </div>
            
            <p>إدارة شاملة للخطط الغذائية:</p>
            <ul>
                <li>إنشاء خطط غذائية مخصصة</li>
                <li>متابعة تقدم المرضى</li>
                <li>تعديل الخطط حسب الحاجة</li>
                <li>تقارير التقدم والنتائج</li>
            </ul>
            
            <div class="stats">
                <div class="stat-box" style="background: #dc3545;">
                    <div class="stat-number">12</div>
                    <div>خطط إنقاص الوزن</div>
                </div>
                <div class="stat-box" style="background: #28a745;">
                    <div class="stat-number">8</div>
                    <div>خطط زيادة الوزن</div>
                </div>
                <div class="stat-box" style="background: #17a2b8;">
                    <div class="stat-number">5</div>
                    <div>خطط الحفاظ على الوزن</div>
                </div>
            </div>
            
            <button class="btn">➕ إنشاء خطة جديدة</button>
            <button class="btn">📊 عرض الخطط النشطة</button>
        </div>
    </div>

    <script>
        function showPage(pageId, button) {
            // إخفاء جميع الصفحات
            var pages = document.getElementsByClassName('page');
            for (var i = 0; i < pages.length; i++) {
                pages[i].classList.remove('active');
            }
            
            // إزالة active من جميع الأزرار
            var buttons = document.getElementsByClassName('menu-btn');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }
            
            // إظهار الصفحة المحددة
            document.getElementById(pageId).classList.add('active');
            
            // إضافة active للزر المحدد
            button.classList.add('active');
            
            console.log('تم التبديل إلى: ' + pageId);
        }
        
        function printPrescription() {
            var newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body { font-family: Arial; padding: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }
                        .patient { background: #f5f5f5; padding: 15px; margin: 20px 0; }
                        .medicine { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>🏥 عيادة التغذية العلاجية</h2>
                        <p>د. [اسم الطبيب] - أخصائي التغذية</p>
                    </div>
                    
                    <div class="patient">
                        <strong>المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>التاريخ:</strong> ${new Date().toLocaleDateString()}<br>
                        <strong>التشخيص:</strong> نقص فيتامين د
                    </div>
                    
                    <h3>الأدوية المطلوبة:</h3>
                    <div class="medicine">
                        <strong>1. فيتامين د3 1000 وحدة</strong><br>
                        الجرعة: حبة واحدة يومياً<br>
                        المدة: 3 أشهر
                    </div>
                    
                    <p><strong>التعليمات:</strong> التعرض لأشعة الشمس، شرب الحليب</p>
                    
                    <div style="margin-top: 50px;">
                        <p>توقيع الطبيب: _______________</p>
                    </div>
                    
                    <script>window.print();</script>
                </body>
                </html>
            `);
            newWindow.document.close();
        }
        
        console.log('تم تحميل النظام بنجاح!');
    </script>
</body>
</html>
