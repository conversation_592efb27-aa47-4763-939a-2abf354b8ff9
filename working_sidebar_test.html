<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار Sidebar</title>
    <style>
        body {
            margin: 0;
            font-family: Arial;
            direction: rtl;
        }
        
        .container {
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .nav-link {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            font-size: 16px;
        }
        
        .nav-link:hover {
            background: #34495e;
        }
        
        .nav-link.active {
            background: #3498db;
        }
        
        .content {
            flex: 1;
            padding: 30px;
            background: #f5f5f5;
        }
        
        .page {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page.active {
            display: block;
        }
        
        .alert {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>🏥 نظام العيادات</h2>
            
            <a href="javascript:void(0)" class="nav-link active" onclick="changePage('dashboard', this)">
                📊 لوحة التحكم
            </a>
            
            <a href="javascript:void(0)" class="nav-link" onclick="changePage('patients', this)">
                👥 إدارة المرضى
            </a>
            
            <a href="javascript:void(0)" class="nav-link" onclick="changePage('nutrition', this)">
                🍽️ الخطط الغذائية
            </a>
            
            <a href="javascript:void(0)" class="nav-link" onclick="changePage('prescriptions', this)">
                💊 الوصفات الطبية
            </a>
            
            <a href="javascript:void(0)" class="nav-link" onclick="changePage('accounting', this)">
                💰 إدارة الحسابات
            </a>
        </div>
        
        <div class="content">
            <div id="dashboard" class="page active">
                <h1>📊 لوحة التحكم</h1>
                <div class="alert">
                    ✅ مرحباً بك! الـ Sidebar يعمل بشكل مثالي!
                </div>
                <p>هذا هو قسم لوحة التحكم. إذا رأيت هذه الرسالة، فإن الـ Sidebar يعمل!</p>
                <ul>
                    <li>✅ إدارة شاملة للمرضى</li>
                    <li>✅ كتابة الوصفات الطبية</li>
                    <li>✅ نظام محاسبي بالدينار العراقي</li>
                    <li>✅ إدارة الخطط الغذائية</li>
                </ul>
            </div>
            
            <div id="patients" class="page">
                <h1>👥 إدارة المرضى</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم إدارة المرضى بنجاح!
                </div>
                <p>هذا هو قسم إدارة المرضى. الـ Sidebar يعمل بشكل صحيح!</p>
                <ul>
                    <li>إضافة مرضى جدد</li>
                    <li>تعديل بيانات المرضى</li>
                    <li>عرض التاريخ الطبي</li>
                    <li>متابعة تقدم المرضى</li>
                </ul>
            </div>
            
            <div id="nutrition" class="page">
                <h1>🍽️ الخطط الغذائية</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم الخطط الغذائية بنجاح!
                </div>
                <p>هذا هو قسم الخطط الغذائية. الـ Sidebar يعمل بشكل ممتاز!</p>
                <ul>
                    <li>إنشاء خطط غذائية مخصصة</li>
                    <li>متابعة تقدم المرضى</li>
                    <li>تعديل الخطط حسب الحاجة</li>
                    <li>تقارير التقدم والنتائج</li>
                </ul>
            </div>
            
            <div id="prescriptions" class="page">
                <h1>💊 الوصفات الطبية</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم الوصفات الطبية بنجاح!
                </div>
                <p>هذا هو قسم الوصفات الطبية. الـ Sidebar يعمل بشكل رائع!</p>
                <ul>
                    <li>كتابة وصفات طبية جديدة</li>
                    <li>طباعة الوصفات</li>
                    <li>حفظ وأرشفة الوصفات</li>
                    <li>متابعة تاريخ الوصفات</li>
                </ul>
                <button onclick="printPrescription()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">
                    🖨️ طباعة وصفة تجريبية
                </button>
            </div>
            
            <div id="accounting" class="page">
                <h1>💰 إدارة الحسابات</h1>
                <div class="alert">
                    ✅ تم الانتقال إلى قسم إدارة الحسابات بنجاح!
                </div>
                <p>هذا هو قسم إدارة الحسابات. الـ Sidebar يعمل بشكل مثالي!</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h3>💚 الإيرادات</h3>
                        <div style="font-size: 24px; font-weight: bold;">58,500,000</div>
                        <div>د.ع</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h3>💸 المصروفات</h3>
                        <div style="font-size: 24px; font-weight: bold;">24,050,000</div>
                        <div>د.ع</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h3>📈 صافي الربح</h3>
                        <div style="font-size: 24px; font-weight: bold;">34,450,000</div>
                        <div>د.ع</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function changePage(pageId, linkElement) {
            console.log('تم النقر على الصفحة:', pageId);
            
            // إخفاء جميع الصفحات
            var allPages = document.getElementsByClassName('page');
            for (var i = 0; i < allPages.length; i++) {
                allPages[i].classList.remove('active');
            }
            
            // إزالة active من جميع الروابط
            var allLinks = document.getElementsByClassName('nav-link');
            for (var i = 0; i < allLinks.length; i++) {
                allLinks[i].classList.remove('active');
            }
            
            // إظهار الصفحة المحددة
            var targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('تم عرض الصفحة:', pageId);
            } else {
                console.error('لم يتم العثور على الصفحة:', pageId);
            }
            
            // إضافة active للرابط المحدد
            if (linkElement) {
                linkElement.classList.add('active');
                console.log('تم تفعيل الرابط');
            }
            
            // رسالة تأكيد
            alert('✅ تم الانتقال بنجاح إلى: ' + getPageName(pageId));
        }
        
        function getPageName(pageId) {
            var pageNames = {
                'dashboard': 'لوحة التحكم',
                'patients': 'إدارة المرضى',
                'nutrition': 'الخطط الغذائية',
                'prescriptions': 'الوصفات الطبية',
                'accounting': 'إدارة الحسابات'
            };
            return pageNames[pageId] || pageId;
        }
        
        function printPrescription() {
            var printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>وصفة طبية</title>
                    <style>
                        body { font-family: Arial; padding: 20px; direction: rtl; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .patient { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                        .medicine { border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>🏥 عيادة التغذية العلاجية</h2>
                        <p>د. [اسم الطبيب] - أخصائي التغذية</p>
                    </div>
                    
                    <div class="patient">
                        <strong>المريض:</strong> أحمد محمد - 35 سنة<br>
                        <strong>التاريخ:</strong> ${new Date().toLocaleDateString()}<br>
                        <strong>التشخيص:</strong> نقص فيتامين د
                    </div>
                    
                    <h3>الأدوية المطلوبة:</h3>
                    <div class="medicine">
                        <strong>1. فيتامين د3 1000 وحدة</strong><br>
                        الجرعة: حبة واحدة يومياً<br>
                        المدة: 3 أشهر
                    </div>
                    
                    <p><strong>التعليمات:</strong> التعرض لأشعة الشمس، شرب الحليب</p>
                    
                    <div style="margin-top: 50px;">
                        <p>توقيع الطبيب: _______________</p>
                    </div>
                    
                    <script>window.print();</script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
        
        // تأكيد تحميل الصفحة
        console.log('✅ تم تحميل الصفحة بنجاح!');
        console.log('✅ الـ Sidebar جاهز للاستخدام!');
        
        // رسالة ترحيب
        setTimeout(function() {
            alert('🎉 مرحباً بك في نظام إدارة العيادات!\n\nالـ Sidebar يعمل بشكل مثالي!\n\nجرب النقر على الأزرار في الجانب الأيمن.');
        }, 1000);
    </script>
</body>
</html>
