<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>اختبار أساسي</title>
    <style>
        body {
            font-family: Arial;
            direction: rtl;
            margin: 20px;
        }
        
        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .content {
            background: #f5f5f5;
            padding: 20px;
            margin: 10px 0;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>🔧 اختبار أساسي للـ JavaScript</h1>
    
    <div class="sidebar">
        <h2>🏥 قائمة الأقسام</h2>
        
        <button onclick="test1()">
            اختبار 1 - تنبيه بسيط
        </button>
        
        <button onclick="test2()">
            اختبار 2 - تغيير النص
        </button>
        
        <button onclick="test3()">
            اختبار 3 - إظهار/إخفاء
        </button>
        
        <button onclick="showSection('section1')">
            📊 لوحة التحكم
        </button>
        
        <button onclick="showSection('section2')">
            👥 إدارة المرضى
        </button>
    </div>
    
    <div class="content">
        <h3>نتائج الاختبار:</h3>
        <p id="result">انقر على أي زر لاختبار JavaScript</p>
    </div>
    
    <div id="section1" class="content">
        <h2>📊 لوحة التحكم</h2>
        <p>✅ تم عرض لوحة التحكم بنجاح!</p>
        <p>إذا رأيت هذه الرسالة، فإن الـ JavaScript يعمل!</p>
    </div>
    
    <div id="section2" class="content hidden">
        <h2>👥 إدارة المرضى</h2>
        <p>✅ تم عرض قسم إدارة المرضى بنجاح!</p>
        <p>الـ Sidebar يعمل بشكل صحيح!</p>
    </div>
    
    <div class="content">
        <h3>📋 تعليمات الاختبار:</h3>
        <ol>
            <li><strong>اختبار 1:</strong> يجب أن يظهر تنبيه منبثق</li>
            <li><strong>اختبار 2:</strong> يجب أن يتغير النص أدناه</li>
            <li><strong>اختبار 3:</strong> يجب أن يظهر/يختفي محتوى</li>
            <li><strong>لوحة التحكم:</strong> يجب أن يظهر قسم لوحة التحكم</li>
            <li><strong>إدارة المرضى:</strong> يجب أن يظهر قسم المرضى</li>
        </ol>
    </div>
    
    <div id="toggleContent" class="content hidden">
        <h3>🎉 ممتاز!</h3>
        <p>إذا رأيت هذه الرسالة، فإن JavaScript يعمل بشكل مثالي!</p>
    </div>

    <script>
        // اختبار 1 - تنبيه بسيط
        function test1() {
            alert('✅ JavaScript يعمل! الاختبار الأول نجح!');
            console.log('تم تشغيل الاختبار الأول');
        }
        
        // اختبار 2 - تغيير النص
        function test2() {
            document.getElementById('result').innerHTML = '✅ تم تغيير النص بنجاح! JavaScript يعمل!';
            console.log('تم تشغيل الاختبار الثاني');
        }
        
        // اختبار 3 - إظهار/إخفاء
        function test3() {
            var element = document.getElementById('toggleContent');
            if (element.classList.contains('hidden')) {
                element.classList.remove('hidden');
                document.getElementById('result').innerHTML = '✅ تم إظهار المحتوى! JavaScript يعمل!';
            } else {
                element.classList.add('hidden');
                document.getElementById('result').innerHTML = '✅ تم إخفاء المحتوى! JavaScript يعمل!';
            }
            console.log('تم تشغيل الاختبار الثالث');
        }
        
        // دالة عرض الأقسام
        function showSection(sectionId) {
            console.log('تم النقر على القسم:', sectionId);
            
            // إخفاء جميع الأقسام
            document.getElementById('section1').classList.add('hidden');
            document.getElementById('section2').classList.add('hidden');
            
            // إظهار القسم المحدد
            var targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.remove('hidden');
                document.getElementById('result').innerHTML = '✅ تم عرض القسم: ' + sectionId;
                console.log('تم عرض القسم:', sectionId);
            } else {
                console.error('لم يتم العثور على القسم:', sectionId);
            }
            
            alert('تم النقر على: ' + sectionId);
        }
        
        // تأكيد تحميل الصفحة
        console.log('✅ تم تحميل الصفحة بنجاح!');
        console.log('✅ JavaScript جاهز للعمل!');
        
        // رسالة ترحيب
        setTimeout(function() {
            alert('🎉 مرحباً!\n\nهذا اختبار أساسي للـ JavaScript.\n\nجرب النقر على الأزرار لاختبار الوظائف.');
        }, 1000);
    </script>
</body>
</html>
