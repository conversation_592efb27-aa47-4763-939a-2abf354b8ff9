# نظام إدارة العيادات الغذائية 🏥

نظام شامل لإدارة العيادات الغذائية مبني بـ Django مع دعم اللغات المتعددة (العربية، الإنجليزية، الكردية) وتكامل WhatsApp API.

## 🌟 الميزات الرئيسية

### 👥 إدارة المستخدمين
- **نظام مصادقة متقدم** مع أنواع مستخدمين مختلفة (طبيب، مريض)
- **ملفات شخصية مفصلة** مع صور وبيانات طبية
- **لوحات تحكم مخصصة** لكل نوع مستخدم
- **إدارة الصلاحيات** والوصول الآمن

### 🩺 إدارة المرضى
- **ملفات طبية شاملة** مع التاريخ المرضي
- **تتبع الوزن والقياسات** مع الرسوم البيانية
- **حساب مؤشر كتلة الجسم** التلقائي
- **إدارة الحساسيات والأدوية**

### 🍎 الخطط الغذائية
- **إنشاء خطط غذائية مخصصة**
- **حساب السعرات والعناصر الغذائية**
- **قاعدة بيانات الأطعمة** مع القيم الغذائية
- **تتبع التقدم والالتزام**

### 📱 نظام التواصل
- **رسائل داخلية** بين الأطباء والمرضى
- **تكامل WhatsApp API** لإرسال الخطط والتذكيرات
- **إدارة المواعيد** مع التذكيرات التلقائية
- **إشعارات فورية**

### 🌍 دعم اللغات المتعددة
- **العربية** (اللغة الافتراضية)
- **الإنجليزية**
- **الكردية**
- **تبديل سهل بين اللغات**

### 🎨 واجهة مستخدم متقدمة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **ألوان وتدرجات جذابة**
- **رسوم متحركة وتأثيرات**
- **تجربة مستخدم محسنة**

## 🛠️ التقنيات المستخدمة

### Backend
- **Django 5.2.3** - إطار العمل الرئيسي
- **Python 3.13** - لغة البرمجة
- **SQLite** - قاعدة البيانات (قابلة للتطوير لـ PostgreSQL)
- **Django REST Framework** - للـ APIs

### Frontend
- **Bootstrap 5.3** - إطار العمل للتصميم
- **Font Awesome 6.0** - الأيقونات
- **CSS3 مخصص** - التصميم المتقدم
- **JavaScript** - التفاعل

### التكاملات
- **WhatsApp Business API** - إرسال الرسائل
- **Twilio** - بديل لـ WhatsApp
- **Django i18n** - دعم اللغات المتعددة

## 📁 هيكل المشروع

```
clinic_management/
├── accounts/                 # إدارة المستخدمين والمصادقة
├── patients/                 # إدارة المرضى
├── nutrition_plans/          # الخطط الغذائية
├── communications/           # التواصل والمواعيد
├── templates/               # قوالب HTML
├── static/                  # الملفات الثابتة
├── locale/                  # ملفات الترجمة
└── media/                   # ملفات المستخدمين
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.11+
- pip
- virtualenv

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd clinic_management
```

2. **إنشاء البيئة الافتراضية**
```bash
python -m venv clinic_env
source clinic_env/bin/activate  # Linux/Mac
# أو
clinic_env\Scripts\activate     # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **تشغيل الخادم**
```bash
python manage.py runserver
```

7. **فتح المتصفح**
```
http://127.0.0.1:8000
```

## ⚙️ الإعدادات

### إعدادات WhatsApp API
في ملف `settings.py`:
```python
# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN = 'your_access_token'
WHATSAPP_PHONE_NUMBER_ID = 'your_phone_number_id'
WHATSAPP_API_URL = 'https://graph.facebook.com/v17.0/'

# Twilio (بديل)
TWILIO_ACCOUNT_SID = 'your_account_sid'
TWILIO_AUTH_TOKEN = 'your_auth_token'
TWILIO_PHONE_NUMBER = 'your_twilio_number'
```

### إعدادات اللغات
```python
LANGUAGES = [
    ('ar', 'العربية'),
    ('en', 'English'),
    ('ku', 'کوردی'),
]
```

## 👥 أنواع المستخدمين

### 🩺 الطبيب
- إدارة المرضى
- إنشاء الخطط الغذائية
- إرسال الرسائل والتذكيرات
- جدولة المواعيد
- عرض التقارير والإحصائيات

### 🧑‍⚕️ المريض
- عرض الملف الطبي
- تتبع الوزن والتقدم
- استقبال الخطط الغذائية
- التواصل مع الطبيب
- عرض المواعيد

## 📊 الميزات المتقدمة

### 📈 التقارير والإحصائيات
- تقارير تقدم المرضى
- إحصائيات الوزن والقياسات
- تحليل الالتزام بالخطط
- رسوم بيانية تفاعلية

### 🔔 نظام التذكيرات
- تذكيرات المواعيد
- تذكيرات تسجيل الوزن
- تذكيرات الأدوية
- إشعارات الخطط الجديدة

### 📱 تكامل WhatsApp
- إرسال الخطط الغذائية
- تذكيرات المواعيد
- رسائل المتابعة
- إشعارات فورية

## 🔒 الأمان

- **مصادقة قوية** مع كلمات مرور آمنة
- **حماية CSRF** في جميع النماذج
- **تشفير البيانات الحساسة**
- **صلاحيات محددة** لكل نوع مستخدم
- **حماية من SQL Injection**

## 🌐 دعم اللغات

النظام يدعم ثلاث لغات:
- **العربية** 🇮🇶 (افتراضية)
- **الإنجليزية** 🇺🇸
- **الكردية** 🟡🔴🟢

يمكن التبديل بين اللغات من القائمة العلوية.

## 📱 التصميم المتجاوب

- **متوافق مع الهواتف الذكية**
- **تحسين للأجهزة اللوحية**
- **واجهة سطح المكتب الكاملة**
- **تجربة موحدة عبر الأجهزة**

## 🚀 التطوير المستقبلي

- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أجهزة القياس الذكية
- [ ] نظام الدفع الإلكتروني
- [ ] تقارير PDF متقدمة
- [ ] تكامل مع التأمين الصحي

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 XXX XXX XXXX

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

---

**تم تطوير هذا النظام بعناية فائقة لتلبية احتياجات العيادات الغذائية الحديثة** 💚
