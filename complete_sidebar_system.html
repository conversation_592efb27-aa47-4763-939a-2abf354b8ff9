<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العيادات - Sidebar يعمل 100%</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            padding: 0;
            box-shadow: -2px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar-header {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-nav {
            padding: 20px 0;
        }
        
        .sidebar-link {
            display: block;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 15px 25px;
            margin: 3px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            background: none;
            width: calc(100% - 30px);
            text-align: right;
        }
        
        .sidebar-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-link.active {
            background: #3498db;
            color: white;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }
        
        .main-content {
            margin-right: 280px;
            padding: 0;
            min-height: 100vh;
        }
        
        .content-section {
            display: none;
            padding: 30px;
            min-height: 100vh;
        }
        
        .content-section.active {
            display: block;
        }
        
        .page-header {
            background: white;
            padding: 20px 30px;
            margin: -30px -30px 30px -30px;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .success-badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .category-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-3px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .stat-card:hover::before {
            animation: shimmer 1.5s ease-in-out;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stat-card.patients {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.plans {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card.appointments {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card.messages {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }
        
        .navbar-top {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            margin: -30px -30px 30px -30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .activity-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .activity-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .progress-bar-animated {
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: progressMove 2s linear infinite;
        }

        @keyframes progressMove {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .pulse-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-card {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .gradient-text {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chart-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            margin-bottom: 20px;
        }

        .notification-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 50px;
            padding: 5px 12px;
            font-size: 0.8em;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .stat-card {
            cursor: pointer;
            user-select: none;
        }

        .stat-card:active {
            transform: scale(0.95) !important;
        }

        .financial-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .financial-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .financial-card:hover::before {
            animation: shimmer 1.5s ease-in-out;
        }

        .financial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .financial-card.income {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .financial-card.expense {
            background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        }

        .financial-card.profit {
            background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        }

        .financial-card.balance {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .money-amount {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            line-height: 1.2;
        }

        .currency-symbol {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.85;
            margin-right: 5px;
            vertical-align: middle;
        }

        .financial-card .money-amount {
            display: block;
            text-align: center;
        }

        .money-number {
            font-size: 22px;
            font-weight: bold;
            display: block;
            line-height: 1;
        }

        .currency-below {
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            margin-top: 5px;
            display: block;
            letter-spacing: 1px;
        }

        .transaction-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .transaction-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .transaction-item.income {
            border-left-color: #28a745;
        }

        .transaction-item.expense {
            border-left-color: #dc3545;
        }

        .invoice-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .invoice-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .chart-container-small {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-heartbeat"></i> نظام العيادات</h4>
            <small>إدارة شاملة ومتكاملة</small>
        </div>
        
        <div class="sidebar-nav">
            <a href="#" class="sidebar-link active" onclick="showSection('dashboard', this)">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('patients', this)">
                <i class="fas fa-users me-2"></i>
                إدارة المرضى
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('nutrition', this)">
                <i class="fas fa-utensils me-2"></i>
                الخطط الغذائية
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('foods', this)">
                <i class="fas fa-apple-alt me-2"></i>
                إدارة الأطعمة
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('appointments', this)">
                <i class="fas fa-calendar me-2"></i>
                المواعيد
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('messages', this)">
                <i class="fas fa-envelope me-2"></i>
                الرسائل
            </a>
            
            <a href="#" class="sidebar-link" onclick="showSection('whatsapp', this)">
                <i class="fab fa-whatsapp me-2"></i>
                رسائل الواتساب
            </a>

            <a href="#" class="sidebar-link" onclick="showSection('accounting', this)">
                <i class="fas fa-calculator me-2"></i>
                إدارة الحسابات
            </a>
        </div>
        
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px;">
            <div class="text-center text-white-50">
                <small>جميع الروابط تعمل 100%</small>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Section -->
        <div id="dashboard" class="content-section active">
            <div class="page-header">
                <h2><i class="fas fa-tachometer-alt me-2 text-primary"></i>لوحة التحكم</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card patients" onclick="animateCard(this)">
                    <i class="fas fa-users pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="stat-number" id="patients-count">0</div>
                    <div style="font-size: 1.1rem; font-weight: 500;">إجمالي المرضى</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">+3 هذا الأسبوع</div>
                </div>
                <div class="stat-card plans" onclick="animateCard(this)">
                    <i class="fas fa-utensils pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="stat-number" id="plans-count">0</div>
                    <div style="font-size: 1.1rem; font-weight: 500;">خطط نشطة</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">+2 اليوم</div>
                </div>
                <div class="stat-card appointments" onclick="animateCard(this)">
                    <i class="fas fa-calendar pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="stat-number" id="appointments-count">0</div>
                    <div style="font-size: 1.1rem; font-weight: 500;">مواعيد اليوم</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">التالي في ساعة</div>
                </div>
                <div class="stat-card messages" onclick="animateCard(this)">
                    <i class="fas fa-envelope pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="stat-number" id="messages-count">0</div>
                    <div style="font-size: 1.1rem; font-weight: 500;">رسائل جديدة</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">
                        <span class="notification-badge">جديد!</span>
                    </div>
                </div>
            </div>
            
            <!-- الرسم البياني -->
            <div class="chart-container floating-card">
                <h5><i class="fas fa-chart-line me-2"></i>إحصائيات الأداء</h5>
                <div class="row">
                    <div class="col-md-8">
                        <canvas id="performanceChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="gradient-text">95%</h3>
                            <p>معدل رضا المرضى</p>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar progress-bar-animated" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="activity-card">
                        <h6><i class="fas fa-clock me-2"></i>آخر النشاطات</h6>
                        <div class="mt-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-user-plus text-success me-2"></i>
                                <span>تم إضافة مريض جديد - أحمد محمد</span>
                                <small class="ms-auto opacity-75">منذ 5 دقائق</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-utensils text-info me-2"></i>
                                <span>تم إنشاء خطة غذائية جديدة</span>
                                <small class="ms-auto opacity-75">منذ 15 دقيقة</small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-warning me-2"></i>
                                <span>موعد جديد محجوز لغداً</span>
                                <small class="ms-auto opacity-75">منذ 30 دقيقة</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fab fa-whatsapp text-success me-2"></i>
                                <span>رسالة واتساب مرسلة للمريض فاطمة</span>
                                <small class="ms-auto opacity-75">منذ ساعة</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card floating-card">
                        <h6><i class="fas fa-tasks me-2 text-primary"></i>المهام المعلقة</h6>
                        <div class="mt-3">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div>
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>مراجعة خطة المريض أحمد</span>
                                </div>
                                <span class="badge bg-success">عاجل</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div>
                                    <i class="fas fa-clock text-warning me-2"></i>
                                    <span>موعد في الساعة 3:00</span>
                                </div>
                                <span class="badge bg-warning">اليوم</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div>
                                    <i class="fas fa-envelope text-info me-2"></i>
                                    <span>رد على رسائل المرضى</span>
                                </div>
                                <span class="badge bg-info">متوسط</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <i class="fas fa-chart-bar text-primary me-2"></i>
                                    <span>إعداد تقرير شهري</span>
                                </div>
                                <span class="badge bg-secondary">منخفض</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                        <h5 class="gradient-text">إنجاز الشهر</h5>
                        <p>تم تحقيق 120% من الهدف المحدد</p>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar progress-bar-animated bg-warning" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-heart fa-3x text-danger mb-3 pulse-icon"></i>
                        <h5 class="gradient-text">صحة المرضى</h5>
                        <p>85% من المرضى حققوا أهدافهم</p>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar progress-bar-animated bg-success" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-star fa-3x text-info mb-3"></i>
                        <h5 class="gradient-text">تقييم العيادة</h5>
                        <p>4.8/5 نجوم من تقييمات المرضى</p>
                        <div class="text-warning">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patients Section -->
        <div id="patients" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-users me-2 text-primary"></i>إدارة المرضى</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-user-plus me-2 text-success"></i>إضافة مريض جديد</h5>
                <p>يمكنك إضافة مريض جديد وإدخال جميع بياناته الأساسية والصحية.</p>
                <button class="btn btn-success" onclick="showAddPatientForm()">
                    <i class="fas fa-plus me-2"></i>إضافة مريض
                </button>
            </div>

            <!-- نموذج إضافة مريض (مخفي في البداية) -->
            <div id="addPatientForm" class="feature-card" style="display: none;">
                <h5><i class="fas fa-user-plus me-2 text-primary"></i>نموذج إضافة مريض جديد</h5>

                <form onsubmit="addNewPatient(event)">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="patientName" placeholder="أدخل الاسم الكامل" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العمر</label>
                                <input type="number" class="form-control" id="patientAge" placeholder="أدخل العمر" min="1" max="120" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الجنس</label>
                                <select class="form-select" id="patientGender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="patientPhone" placeholder="07xxxxxxxx">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوزن الحالي (كغ)</label>
                                <input type="number" class="form-control" id="patientWeight" placeholder="الوزن بالكيلوغرام" step="0.1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الطول (سم)</label>
                                <input type="number" class="form-control" id="patientHeight" placeholder="الطول بالسنتيمتر">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الهدف من العلاج</label>
                        <select class="form-select" id="patientGoal" required>
                            <option value="">اختر الهدف</option>
                            <option value="weight_loss">إنقاص الوزن</option>
                            <option value="weight_gain">زيادة الوزن</option>
                            <option value="weight_maintain">الحفاظ على الوزن</option>
                            <option value="health_improve">تحسين الصحة العامة</option>
                            <option value="muscle_gain">زيادة الكتلة العضلية</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الحالات الصحية (إن وجدت)</label>
                        <textarea class="form-control" id="patientConditions" rows="3" placeholder="مثال: السكري، ضغط الدم، حساسية معينة..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="patientNotes" rows="2" placeholder="أي ملاحظات أو معلومات إضافية..."></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-save me-2"></i>حفظ المريض
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="hideAddPatientForm()">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-list me-2 text-info"></i>قائمة المرضى</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>العمر</th>
                                <th>الهدف</th>
                                <th>آخر زيارة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="fas fa-user me-2"></i>أحمد محمد</td>
                                <td>35 سنة</td>
                                <td>إنقاص الوزن</td>
                                <td>2024-01-15</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewPatientDetails('أحمد محمد')" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="editPatientDetails('أحمد محمد')" title="تعديل البيانات">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="createNutritionPlan('أحمد محمد')" title="إنشاء خطة غذائية">
                                        <i class="fas fa-utensils"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="sendWhatsAppMessage('أحمد محمد')" title="إرسال رسالة واتساب">
                                        <i class="fab fa-whatsapp"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-user me-2"></i>فاطمة علي</td>
                                <td>28 سنة</td>
                                <td>زيادة الوزن</td>
                                <td>2024-01-12</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewPatientDetails('فاطمة علي')" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="editPatientDetails('فاطمة علي')" title="تعديل البيانات">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="createNutritionPlan('فاطمة علي')" title="إنشاء خطة غذائية">
                                        <i class="fas fa-utensils"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="sendWhatsAppMessage('فاطمة علي')" title="إرسال رسالة واتساب">
                                        <i class="fab fa-whatsapp"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Nutrition Section -->
        <div id="nutrition" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-utensils me-2 text-success"></i>الخطط الغذائية</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-plus me-2 text-success"></i>إنشاء خطة غذائية جديدة</h5>
                <p>قم بإنشاء خطة غذائية مخصصة لكل مريض حسب احتياجاته وأهدافه.</p>
                <button class="btn btn-success" onclick="showCreateNutritionPlanForm()">
                    <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
                </button>
            </div>

            <!-- نموذج إنشاء خطة غذائية (مخفي في البداية) -->
            <div id="createNutritionPlanForm" class="feature-card" style="display: none;">
                <h5><i class="fas fa-utensils me-2 text-primary"></i>نموذج إنشاء خطة غذائية جديدة</h5>

                <form onsubmit="createNutritionPlan(event)">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الخطة</label>
                                <input type="text" class="form-control" id="planName" placeholder="مثال: خطة إنقاص الوزن السريع" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المريض</label>
                                <select class="form-select" id="planPatient" required>
                                    <option value="">اختر المريض</option>
                                    <option value="أحمد محمد">أحمد محمد</option>
                                    <option value="فاطمة علي">فاطمة علي</option>
                                    <option value="سارة أحمد">سارة أحمد</option>
                                    <option value="محمد علي">محمد علي</option>
                                    <option value="نور أحمد">نور أحمد</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">نوع الخطة</label>
                                <select class="form-select" id="planType" required>
                                    <option value="">اختر نوع الخطة</option>
                                    <option value="weight_loss">إنقاص الوزن</option>
                                    <option value="weight_gain">زيادة الوزن</option>
                                    <option value="weight_maintain">الحفاظ على الوزن</option>
                                    <option value="health_improve">تحسين الصحة العامة</option>
                                    <option value="muscle_gain">زيادة الكتلة العضلية</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">السعرات اليومية</label>
                                <input type="number" class="form-control" id="planCalories" placeholder="مثال: 1800" min="800" max="4000" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">مدة الخطة</label>
                                <select class="form-select" id="planDuration" required>
                                    <option value="">اختر المدة</option>
                                    <option value="1 شهر">شهر واحد</option>
                                    <option value="2 شهر">شهرين</option>
                                    <option value="3 أشهر">3 أشهر</option>
                                    <option value="6 أشهر">6 أشهر</option>
                                    <option value="سنة">سنة كاملة</option>
                                    <option value="مستمرة">مستمرة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">عدد الوجبات اليومية</label>
                                <select class="form-select" id="planMeals" required>
                                    <option value="">اختر عدد الوجبات</option>
                                    <option value="3">3 وجبات رئيسية</option>
                                    <option value="4">3 رئيسية + 1 خفيفة</option>
                                    <option value="5">3 رئيسية + 2 خفيفة</option>
                                    <option value="6">6 وجبات صغيرة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">نسبة البروتين (%)</label>
                                <input type="number" class="form-control" id="planProtein" placeholder="مثال: 25" min="10" max="40" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">نسبة الكربوهيدرات (%)</label>
                                <input type="number" class="form-control" id="planCarbs" placeholder="مثال: 50" min="20" max="70" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأطعمة المفضلة</label>
                        <textarea class="form-control" id="planPreferredFoods" rows="2" placeholder="مثال: دجاج، سمك، خضروات ورقية، فواكه..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأطعمة المحظورة أو المتجنبة</label>
                        <textarea class="form-control" id="planAvoidedFoods" rows="2" placeholder="مثال: السكريات، الدهون المشبعة، الأطعمة المقلية..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات خاصة</label>
                        <textarea class="form-control" id="planNotes" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة للمريض..."></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-save me-2"></i>إنشاء الخطة
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="hideCreateNutritionPlanForm()">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="category-grid">
                <div class="category-item" onclick="showNutritionPlans('weight_loss')" style="cursor: pointer;">
                    <i class="fas fa-weight-hanging fa-2x text-danger mb-3"></i>
                    <h6>خطط إنقاص الوزن</h6>
                    <p class="text-muted">خطط مصممة لفقدان الوزن الصحي</p>
                    <span class="badge bg-danger">12 خطة نشطة</span>
                </div>
                <div class="category-item" onclick="showNutritionPlans('weight_gain')" style="cursor: pointer;">
                    <i class="fas fa-chart-line fa-2x text-success mb-3"></i>
                    <h6>خطط زيادة الوزن</h6>
                    <p class="text-muted">خطط لزيادة الوزن بطريقة صحية</p>
                    <span class="badge bg-success">8 خطط نشطة</span>
                </div>
                <div class="category-item" onclick="showNutritionPlans('weight_maintain')" style="cursor: pointer;">
                    <i class="fas fa-balance-scale fa-2x text-info mb-3"></i>
                    <h6>خطط الحفاظ على الوزن</h6>
                    <p class="text-muted">خطط للحفاظ على الوزن المثالي</p>
                    <span class="badge bg-info">5 خطط نشطة</span>
                </div>
            </div>
        </div>

        <!-- Foods Section -->
        <div id="foods" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-apple-alt me-2 text-warning"></i>إدارة الأطعمة</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-database me-2 text-info"></i>قاعدة بيانات الأطعمة</h5>
                <p>قاعدة بيانات شاملة تحتوي على جميع الأطعمة مع قيمها الغذائية.</p>
            </div>
            
            <div class="category-grid">
                <div class="category-item" onclick="showFoodCategory('grains')" style="cursor: pointer;">
                    <i class="fas fa-bread-slice fa-3x text-warning mb-3"></i>
                    <h5>الحبوب والنشويات</h5>
                    <p class="text-muted">أرز، خبز، معكرونة، شوفان</p>
                    <span class="badge bg-warning">45 صنف</span>
                </div>
                <div class="category-item" onclick="showFoodCategory('proteins')" style="cursor: pointer;">
                    <i class="fas fa-drumstick-bite fa-3x text-danger mb-3"></i>
                    <h5>البروتينات</h5>
                    <p class="text-muted">لحوم، دجاج، سمك، بيض</p>
                    <span class="badge bg-danger">32 صنف</span>
                </div>
                <div class="category-item" onclick="showFoodCategory('vegetables')" style="cursor: pointer;">
                    <i class="fas fa-carrot fa-3x text-success mb-3"></i>
                    <h5>الخضروات والفواكه</h5>
                    <p class="text-muted">جزر، تفاح، برتقال، خيار</p>
                    <span class="badge bg-success">78 صنف</span>
                </div>
                <div class="category-item" onclick="showFoodCategory('dairy')" style="cursor: pointer;">
                    <i class="fas fa-cheese fa-3x text-info mb-3"></i>
                    <h5>الألبان ومنتجاتها</h5>
                    <p class="text-muted">حليب، جبن، زبادي، لبنة</p>
                    <span class="badge bg-info">25 صنف</span>
                </div>
            </div>
        </div>

        <!-- Appointments Section -->
        <div id="appointments" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-calendar me-2 text-info"></i>إدارة المواعيد</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-calendar-plus me-2 text-success"></i>حجز موعد جديد</h5>
                <p>احجز موعد جديد للمريض مع تحديد التاريخ والوقت المناسب.</p>
                <button class="btn btn-success"><i class="fas fa-plus me-2"></i>حجز موعد جديد</button>
            </div>
            
            <div class="feature-card">
                <h5><i class="fas fa-clock me-2 text-warning"></i>مواعيد اليوم</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="border rounded p-3 mb-3">
                            <h6><i class="fas fa-user me-2"></i>أحمد محمد</h6>
                            <p class="mb-1"><i class="fas fa-clock me-2"></i>10:00 صباحاً</p>
                            <p class="mb-0 text-muted">متابعة خطة غذائية</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 mb-3">
                            <h6><i class="fas fa-user me-2"></i>فاطمة علي</h6>
                            <p class="mb-1"><i class="fas fa-clock me-2"></i>2:00 مساءً</p>
                            <p class="mb-0 text-muted">استشارة أولى</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Section -->
        <div id="messages" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-envelope me-2 text-secondary"></i>إدارة الرسائل</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>
            
            <div class="category-grid">
                <div class="category-item">
                    <i class="fas fa-inbox fa-2x text-primary mb-3"></i>
                    <h5>الرسائل الواردة</h5>
                    <p class="text-muted">رسائل من المرضى والزملاء</p>
                    <span class="badge bg-primary">12 رسالة جديدة</span>
                </div>
                <div class="category-item">
                    <i class="fas fa-paper-plane fa-2x text-success mb-3"></i>
                    <h5>الرسائل المرسلة</h5>
                    <p class="text-muted">رسائل تم إرسالها للمرضى</p>
                    <span class="badge bg-success">45 رسالة</span>
                </div>
                <div class="category-item">
                    <i class="fas fa-star fa-2x text-warning mb-3"></i>
                    <h5>الرسائل المهمة</h5>
                    <p class="text-muted">رسائل مميزة بنجمة</p>
                    <span class="badge bg-warning">8 رسائل</span>
                </div>
                <div class="category-item">
                    <i class="fas fa-archive fa-2x text-secondary mb-3"></i>
                    <h5>الأرشيف</h5>
                    <p class="text-muted">رسائل مؤرشفة</p>
                    <span class="badge bg-secondary">156 رسالة</span>
                </div>
            </div>
        </div>

        <!-- WhatsApp Section -->
        <div id="whatsapp" class="content-section">
            <div class="page-header">
                <h2><i class="fab fa-whatsapp me-2 text-success"></i>رسائل الواتساب</h2>
                <div class="success-badge">✅ يعمل بشكل مثالي</div>
            </div>

            <div class="feature-card">
                <h5><i class="fab fa-whatsapp me-2 text-success"></i>إرسال رسالة واتساب</h5>
                <p>أرسل رسائل سريعة للمرضى عبر الواتساب لتذكيرهم بالمواعيد أو إرسال الخطط الغذائية.</p>
                <button class="btn btn-success"><i class="fab fa-whatsapp me-2"></i>إرسال رسالة جديدة</button>
            </div>

            <div class="feature-card">
                <h5><i class="fas fa-chart-bar me-2 text-info"></i>إحصائيات الرسائل</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">156</div>
                            <small>رسائل مرسلة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">142</div>
                            <small>تم التسليم</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-primary">128</div>
                            <small>تم القراءة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">14</div>
                            <small>في الانتظار</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accounting Section -->
        <div id="accounting" class="content-section">
            <div class="page-header">
                <h2><i class="fas fa-calculator me-2 text-primary"></i>إدارة الحسابات</h2>
                <div class="success-badge">✅ نظام مالي متكامل</div>
            </div>

            <!-- الإحصائيات المالية -->
            <div class="stats-grid">
                <div class="financial-card income" onclick="animateCard(this)">
                    <i class="fas fa-arrow-up pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="money-amount">
                        <span class="money-number" id="total-income">0</span>
                        <span class="currency-below">د.ع</span>
                    </div>
                    <div style="font-size: 1.1rem; font-weight: 500; margin-top: 10px;">إجمالي الإيرادات</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">+15% هذا الشهر</div>
                </div>
                <div class="financial-card expense" onclick="animateCard(this)">
                    <i class="fas fa-arrow-down pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="money-amount">
                        <span class="money-number" id="total-expenses">0</span>
                        <span class="currency-below">د.ع</span>
                    </div>
                    <div style="font-size: 1.1rem; font-weight: 500; margin-top: 10px;">إجمالي المصروفات</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">-5% عن الشهر الماضي</div>
                </div>
                <div class="financial-card profit" onclick="animateCard(this)">
                    <i class="fas fa-chart-line pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="money-amount">
                        <span class="money-number" id="net-profit">0</span>
                        <span class="currency-below">د.ع</span>
                    </div>
                    <div style="font-size: 1.1rem; font-weight: 500; margin-top: 10px;">صافي الربح</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">+25% نمو</div>
                </div>
                <div class="financial-card balance" onclick="animateCard(this)">
                    <i class="fas fa-wallet pulse-icon" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <div class="money-amount">
                        <span class="money-number" id="current-balance">0</span>
                        <span class="currency-below">د.ع</span>
                    </div>
                    <div style="font-size: 1.1rem; font-weight: 500; margin-top: 10px;">الرصيد الحالي</div>
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-top: 5px;">متاح للسحب</div>
                </div>
            </div>

            <!-- الرسم البياني المالي -->
            <div class="chart-container-small">
                <h5><i class="fas fa-chart-area me-2 text-primary"></i>تحليل الإيرادات والمصروفات</h5>
                <canvas id="financialChart" width="800" height="300"></canvas>
            </div>

            <div class="row">
                <!-- المعاملات الأخيرة -->
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-history me-2 text-info"></i>المعاملات الأخيرة</h5>
                        <div class="mt-3">
                            <div class="transaction-item income">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        <strong>استشارة - أحمد محمد</strong>
                                        <div class="small text-muted">اليوم 10:30 ص</div>
                                    </div>
                                    <div class="text-success fw-bold">+260,000 د.ع</div>
                                </div>
                            </div>
                            <div class="transaction-item income">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        <strong>خطة غذائية - فاطمة علي</strong>
                                        <div class="small text-muted">أمس 3:15 م</div>
                                    </div>
                                    <div class="text-success fw-bold">+455,000 د.ع</div>
                                </div>
                            </div>
                            <div class="transaction-item expense">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-minus-circle text-danger me-2"></i>
                                        <strong>فاتورة كهرباء</strong>
                                        <div class="small text-muted">أمس 1:00 م</div>
                                    </div>
                                    <div class="text-danger fw-bold">-585,000 د.ع</div>
                                </div>
                            </div>
                            <div class="transaction-item expense">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-minus-circle text-danger me-2"></i>
                                        <strong>مستلزمات طبية</strong>
                                        <div class="small text-muted">منذ يومين</div>
                                    </div>
                                    <div class="text-danger fw-bold">-364,000 د.ع</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>إضافة معاملة جديدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الفواتير والمدفوعات -->
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-file-invoice me-2 text-warning"></i>الفواتير والمدفوعات</h5>
                        <div class="mt-3">
                            <div class="invoice-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6><i class="fas fa-file-invoice me-2"></i>فاتورة #001</h6>
                                        <small>أحمد محمد - استشارة غذائية</small>
                                    </div>
                                    <div>
                                        <div class="fw-bold">260,000 د.ع</div>
                                        <span class="badge bg-success">مدفوعة</span>
                                    </div>
                                </div>
                            </div>
                            <div class="invoice-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6><i class="fas fa-file-invoice me-2"></i>فاتورة #002</h6>
                                        <small>فاطمة علي - خطة غذائية شاملة</small>
                                    </div>
                                    <div>
                                        <div class="fw-bold">455,000 د.ع</div>
                                        <span class="badge bg-warning">معلقة</span>
                                    </div>
                                </div>
                            </div>
                            <div class="invoice-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6><i class="fas fa-file-invoice me-2"></i>فاتورة #003</h6>
                                        <small>محمد أحمد - متابعة شهرية</small>
                                    </div>
                                    <div>
                                        <div class="fw-bold">195,000 د.ع</div>
                                        <span class="badge bg-info">مرسلة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-warning w-100">
                                <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير المالية -->
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-chart-pie fa-3x text-primary mb-3"></i>
                        <h5 class="gradient-text">تقرير شهري</h5>
                        <p>تقرير مفصل للإيرادات والمصروفات</p>
                        <button class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>تحميل PDF
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-calculator fa-3x text-success mb-3"></i>
                        <h5 class="gradient-text">حاسبة الضرائب</h5>
                        <p>حساب الضرائب المستحقة تلقائياً</p>
                        <button class="btn btn-success">
                            <i class="fas fa-calculator me-2"></i>حساب الضرائب
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-piggy-bank fa-3x text-warning mb-3"></i>
                        <h5 class="gradient-text">خطة الادخار</h5>
                        <p>تخطيط مالي للمستقبل</p>
                        <button class="btn btn-warning">
                            <i class="fas fa-chart-line me-2"></i>عرض الخطة
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات متقدمة -->
            <div class="feature-card">
                <h5><i class="fas fa-analytics me-2 text-info"></i>إحصائيات متقدمة</h5>
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-primary">85%</div>
                            <small>معدل تحصيل الفواتير</small>
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar bg-primary" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">12</div>
                            <small>متوسط المرضى يومياً</small>
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar bg-success" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">357,500</div>
                            <small>متوسط قيمة الاستشارة (د.ع)</small>
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar bg-warning" style="width: 90%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">95%</div>
                            <small>رضا المرضى عن الخدمة</small>
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar bg-info" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, linkElement) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // إزالة active من جميع الروابط
            const links = document.querySelectorAll('.sidebar-link');
            links.forEach(link => {
                link.classList.remove('active');
            });
            
            // إظهار القسم المحدد
            document.getElementById(sectionId).classList.add('active');
            
            // إضافة active للرابط المحدد
            linkElement.classList.add('active');
            
            // تأثير بصري
            linkElement.style.transform = 'translateX(-10px)';
            setTimeout(() => {
                linkElement.style.transform = 'translateX(-5px)';
            }, 200);
        }
        
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.title = `نظام إدارة العيادات - ${timeString}`;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // تحريك الأرقام في البطاقات
        function animateNumbers() {
            const targets = [
                { id: 'patients-count', target: 25 },
                { id: 'plans-count', target: 12 },
                { id: 'appointments-count', target: 8 },
                { id: 'messages-count', target: 45 }
            ];

            targets.forEach(item => {
                let current = 0;
                const increment = item.target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= item.target) {
                        current = item.target;
                        clearInterval(timer);
                    }
                    document.getElementById(item.id).textContent = Math.floor(current);
                }, 40);
            });
        }

        // تحريك الأرقام المالية
        function animateFinancialNumbers() {
            const financialTargets = [
                { id: 'total-income', target: 58500000 },
                { id: 'total-expenses', target: 24050000 },
                { id: 'net-profit', target: 34450000 },
                { id: 'current-balance', target: 162500000 }
            ];

            financialTargets.forEach(item => {
                let current = 0;
                const increment = item.target / 60;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= item.target) {
                        current = item.target;
                        clearInterval(timer);
                    }
                    const element = document.getElementById(item.id);
                    if (element) {
                        // تنسيق الرقم فقط (العملة منفصلة في HTML)
                        const formattedNumber = Math.floor(current).toLocaleString('ar-SA');
                        element.textContent = formattedNumber;
                    }
                }, 50);
            });
        }

        // تأثير النقر على البطاقات
        function animateCard(card) {
            console.log('تم النقر على البطاقة:', card.className);

            // تأثير الضغط
            card.style.transform = 'scale(0.95)';
            card.style.transition = 'all 0.1s ease';

            // تأثير الارتداد
            setTimeout(() => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
                card.style.transition = 'all 0.3s ease';

                // تأثير لوني مؤقت
                const originalFilter = card.style.filter;
                card.style.filter = 'brightness(1.2)';

                // العودة للحالة الطبيعية
                setTimeout(() => {
                    card.style.transform = 'translateY(-10px) scale(1.02)';
                    card.style.filter = originalFilter;
                }, 300);

            }, 150);

            // إضافة تأثير صوتي بصري
            card.style.boxShadow = '0 15px 35px rgba(0,0,0,0.3)';
            setTimeout(() => {
                card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            }, 500);

            // رسالة تأكيد
            showCardClickMessage(card);
        }

        // رسالة تأكيد النقر
        function showCardClickMessage(card) {
            const cardType = card.classList.contains('patients') ? 'المرضى' :
                           card.classList.contains('plans') ? 'الخطط' :
                           card.classList.contains('appointments') ? 'المواعيد' :
                           card.classList.contains('messages') ? 'الرسائل' : 'البطاقة';

            // إنشاء رسالة مؤقتة
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: bold;
                animation: slideIn 0.3s ease;
            `;
            message.innerHTML = `✅ تم النقر على بطاقة ${cardType}`;

            document.body.appendChild(message);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                message.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(message);
                }, 300);
            }, 2000);
        }

        // وظائف إدارة المرضى
        function showAddPatientForm() {
            console.log('تم النقر على زر إضافة مريض');

            const form = document.getElementById('addPatientForm');
            if (form) {
                form.style.display = 'block';
                form.scrollIntoView({ behavior: 'smooth' });

                // تأثير بصري
                form.style.opacity = '0';
                form.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    form.style.transition = 'all 0.5s ease';
                    form.style.opacity = '1';
                    form.style.transform = 'translateY(0)';
                }, 100);

                // رسالة تأكيد
                showSuccessMessage('تم فتح نموذج إضافة مريض جديد');
            }
        }

        function hideAddPatientForm() {
            console.log('تم النقر على زر الإلغاء');

            const form = document.getElementById('addPatientForm');
            if (form) {
                form.style.transition = 'all 0.3s ease';
                form.style.opacity = '0';
                form.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    form.style.display = 'none';
                    // إعادة تعيين النموذج
                    form.querySelector('form').reset();
                }, 300);

                showSuccessMessage('تم إلغاء إضافة المريض');
            }
        }

        function addNewPatient(event) {
            event.preventDefault();
            console.log('تم إرسال نموذج إضافة مريض');

            // جمع البيانات
            const patientData = {
                name: document.getElementById('patientName').value,
                age: document.getElementById('patientAge').value,
                gender: document.getElementById('patientGender').value,
                phone: document.getElementById('patientPhone').value,
                weight: document.getElementById('patientWeight').value,
                height: document.getElementById('patientHeight').value,
                goal: document.getElementById('patientGoal').value,
                conditions: document.getElementById('patientConditions').value,
                notes: document.getElementById('patientNotes').value,
                dateAdded: new Date().toLocaleDateString('ar-SA')
            };

            console.log('بيانات المريض:', patientData);

            // محاكاة حفظ البيانات
            setTimeout(() => {
                // إضافة المريض للجدول
                addPatientToTable(patientData);

                // إخفاء النموذج
                hideAddPatientForm();

                // رسالة نجاح
                showSuccessMessage(`تم إضافة المريض ${patientData.name} بنجاح!`);

                // تحديث عدد المرضى في لوحة التحكم
                updatePatientsCount();

            }, 1000);

            // رسالة تحميل
            showSuccessMessage('جاري حفظ بيانات المريض...');
        }

        function addPatientToTable(patientData) {
            const tableBody = document.querySelector('#patients .table tbody');
            if (tableBody) {
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td><i class="fas fa-user me-2"></i>${patientData.name}</td>
                    <td>${patientData.age} سنة</td>
                    <td>${getGoalText(patientData.goal)}</td>
                    <td>${patientData.dateAdded}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewPatient('${patientData.name}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editPatient('${patientData.name}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;

                // إضافة تأثير بصري للصف الجديد
                newRow.style.backgroundColor = '#d4edda';
                tableBody.appendChild(newRow);

                // إزالة التأثير بعد 3 ثوان
                setTimeout(() => {
                    newRow.style.transition = 'background-color 1s ease';
                    newRow.style.backgroundColor = '';
                }, 3000);
            }
        }

        function getGoalText(goal) {
            const goals = {
                'weight_loss': 'إنقاص الوزن',
                'weight_gain': 'زيادة الوزن',
                'weight_maintain': 'الحفاظ على الوزن',
                'health_improve': 'تحسين الصحة',
                'muscle_gain': 'زيادة العضلات'
            };
            return goals[goal] || goal;
        }

        function updatePatientsCount() {
            const countElement = document.getElementById('patients-count');
            if (countElement) {
                const currentCount = parseInt(countElement.textContent);
                countElement.textContent = currentCount + 1;

                // تأثير بصري
                countElement.style.color = '#28a745';
                countElement.style.transform = 'scale(1.2)';

                setTimeout(() => {
                    countElement.style.color = '';
                    countElement.style.transform = '';
                }, 1000);
            }
        }

        function viewPatient(patientName) {
            showSuccessMessage(`عرض تفاصيل المريض: ${patientName}`);
        }

        function editPatient(patientName) {
            showSuccessMessage(`تعديل بيانات المريض: ${patientName}`);
        }

        // وظائف أزرار الإجراءات المحسنة
        function viewPatientDetails(patientName) {
            console.log(`عرض تفاصيل المريض: ${patientName}`);

            // إنشاء نافذة منبثقة لعرض تفاصيل المريض
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-user me-2"></i>تفاصيل المريض: ${patientName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <strong style="color: #495057;">الاسم:</strong><br>
                            <span style="color: #2c3e50;">${patientName}</span>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <strong style="color: #495057;">العمر:</strong><br>
                            <span style="color: #2c3e50;">${patientName === 'أحمد محمد' ? '35 سنة' : '28 سنة'}</span>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <strong style="color: #495057;">الهدف:</strong><br>
                            <span style="color: #2c3e50;">${patientName === 'أحمد محمد' ? 'إنقاص الوزن' : 'زيادة الوزن'}</span>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <strong style="color: #495057;">آخر زيارة:</strong><br>
                            <span style="color: #2c3e50;">${patientName === 'أحمد محمد' ? '2024-01-15' : '2024-01-12'}</span>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h6 style="color: #1976d2; margin-bottom: 10px;">
                            <i class="fas fa-chart-line me-2"></i>التقدم المحرز
                        </h6>
                        <div style="background: #fff; padding: 15px; border-radius: 8px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>الوزن الحالي:</span>
                                <strong>${patientName === 'أحمد محمد' ? '85 كغ' : '52 كغ'}</strong>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>الوزن المستهدف:</span>
                                <strong>${patientName === 'أحمد محمد' ? '75 كغ' : '60 كغ'}</strong>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>التقدم:</span>
                                <strong style="color: #28a745;">${patientName === 'أحمد محمد' ? '-3 كغ' : '+2 كغ'}</strong>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="editPatientDetails('${patientName}')" style="
                            background: #ffc107;
                            color: #212529;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            margin: 0 5px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-edit me-2"></i>تعديل البيانات
                        </button>
                        <button onclick="createNutritionPlan('${patientName}')" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            margin: 0 5px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-utensils me-2"></i>خطة غذائية
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            // إغلاق عند النقر خارج النافذة
            modal.onclick = function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            };

            window.currentModal = modal;
            showSuccessMessage(`تم فتح تفاصيل المريض: ${patientName}`);
        }

        function editPatientDetails(patientName) {
            console.log(`تعديل بيانات المريض: ${patientName}`);

            // إغلاق النافذة الحالية إن وجدت
            if (window.currentModal) {
                closeModal();
            }

            // إنشاء نافذة تعديل
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 700px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-edit me-2"></i>تعديل بيانات: ${patientName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form onsubmit="savePatientChanges(event, '${patientName}')">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الكامل:</label>
                                <input type="text" value="${patientName}" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">العمر:</label>
                                <input type="number" value="${patientName === 'أحمد محمد' ? '35' : '28'}" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الوزن الحالي (كغ):</label>
                                <input type="number" value="${patientName === 'أحمد محمد' ? '85' : '52'}" step="0.1" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                ">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الوزن المستهدف (كغ):</label>
                                <input type="number" value="${patientName === 'أحمد محمد' ? '75' : '60'}" step="0.1" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                ">
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الهدف من العلاج:</label>
                            <select style="
                                width: 100%;
                                padding: 10px;
                                border: 1px solid #ddd;
                                border-radius: 5px;
                            ">
                                <option value="weight_loss" ${patientName === 'أحمد محمد' ? 'selected' : ''}>إنقاص الوزن</option>
                                <option value="weight_gain" ${patientName === 'فاطمة علي' ? 'selected' : ''}>زيادة الوزن</option>
                                <option value="weight_maintain">الحفاظ على الوزن</option>
                                <option value="health_improve">تحسين الصحة العامة</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea rows="3" style="
                                width: 100%;
                                padding: 10px;
                                border: 1px solid #ddd;
                                border-radius: 5px;
                                resize: vertical;
                            " placeholder="أي ملاحظات أو تحديثات..."></textarea>
                        </div>

                        <div style="text-align: center;">
                            <button type="submit" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 5px;
                                margin: 0 5px;
                                cursor: pointer;
                                font-weight: bold;
                            ">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <button type="button" onclick="closeModal()" style="
                                background: #6c757d;
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 5px;
                                margin: 0 5px;
                                cursor: pointer;
                            ">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            window.currentModal = modal;
            showSuccessMessage(`تم فتح نموذج تعديل بيانات: ${patientName}`);
        }

        function createNutritionPlan(patientName) {
            console.log(`إنشاء خطة غذائية للمريض: ${patientName}`);

            // إغلاق النافذة الحالية إن وجدت
            if (window.currentModal) {
                closeModal();
            }

            showSuccessMessage(`جاري إنشاء خطة غذائية للمريض: ${patientName}`);

            // محاكاة إنشاء الخطة
            setTimeout(() => {
                showSuccessMessage(`تم إنشاء خطة غذائية جديدة للمريض: ${patientName}`);

                // الانتقال لقسم الخطط الغذائية
                setTimeout(() => {
                    showSection('nutrition', document.querySelector('[onclick*="nutrition"]'));
                    showSuccessMessage('تم الانتقال لقسم الخطط الغذائية');
                }, 1000);
            }, 1500);
        }

        function sendWhatsAppMessage(patientName) {
            console.log(`إرسال رسالة واتساب للمريض: ${patientName}`);

            showSuccessMessage(`جاري إرسال رسالة واتساب للمريض: ${patientName}`);

            // محاكاة إرسال الرسالة
            setTimeout(() => {
                showSuccessMessage(`تم إرسال رسالة واتساب بنجاح للمريض: ${patientName}`);

                // الانتقال لقسم رسائل الواتساب
                setTimeout(() => {
                    showSection('whatsapp', document.querySelector('[onclick*="whatsapp"]'));
                    showSuccessMessage('تم الانتقال لقسم رسائل الواتساب');
                }, 1000);
            }, 1500);
        }

        function closeModal() {
            if (window.currentModal) {
                window.currentModal.style.transition = 'opacity 0.3s ease';
                window.currentModal.style.opacity = '0';

                setTimeout(() => {
                    if (document.body.contains(window.currentModal)) {
                        document.body.removeChild(window.currentModal);
                    }
                    window.currentModal = null;
                }, 300);
            }
        }

        function savePatientChanges(event, patientName) {
            event.preventDefault();
            console.log(`حفظ تغييرات المريض: ${patientName}`);

            showSuccessMessage('جاري حفظ التغييرات...');

            // محاكاة حفظ البيانات
            setTimeout(() => {
                closeModal();
                showSuccessMessage(`تم حفظ تغييرات المريض: ${patientName} بنجاح!`);
            }, 1500);
        }

        // وظائف الخطط الغذائية
        function showNutritionPlans(planType) {
            console.log(`عرض خطط: ${planType}`);

            const planTypes = {
                'weight_loss': 'إنقاص الوزن',
                'weight_gain': 'زيادة الوزن',
                'weight_maintain': 'الحفاظ على الوزن'
            };

            const planName = planTypes[planType];

            // إنشاء نافذة عرض الخطط
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            const plansData = {
                'weight_loss': [
                    { name: 'خطة إنقاص الوزن السريع', patient: 'أحمد محمد', calories: '1500 سعرة', duration: '3 أشهر' },
                    { name: 'خطة إنقاص الوزن التدريجي', patient: 'سارة أحمد', calories: '1800 سعرة', duration: '6 أشهر' },
                    { name: 'خطة إنقاص الوزن للرياضيين', patient: 'محمد علي', calories: '2000 سعرة', duration: '4 أشهر' }
                ],
                'weight_gain': [
                    { name: 'خطة زيادة الوزن الصحي', patient: 'فاطمة علي', calories: '2800 سعرة', duration: '4 أشهر' },
                    { name: 'خطة زيادة الكتلة العضلية', patient: 'عمر حسن', calories: '3200 سعرة', duration: '6 أشهر' }
                ],
                'weight_maintain': [
                    { name: 'خطة الحفاظ على الوزن المثالي', patient: 'نور أحمد', calories: '2200 سعرة', duration: 'مستمرة' },
                    { name: 'خطة التوازن الغذائي', patient: 'ليلى محمد', calories: '2000 سعرة', duration: 'مستمرة' }
                ]
            };

            const plans = plansData[planType] || [];

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 800px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-utensils me-2"></i>خطط ${planName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div style="margin-bottom: 20px;">
                        ${plans.map(plan => `
                            <div style="
                                background: #f8f9fa;
                                border: 1px solid #dee2e6;
                                border-radius: 10px;
                                padding: 20px;
                                margin-bottom: 15px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.1)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h6 style="color: #2c3e50; margin-bottom: 10px;">
                                            <i class="fas fa-clipboard-list me-2"></i>${plan.name}
                                        </h6>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; font-size: 14px;">
                                            <div>
                                                <strong>المريض:</strong><br>
                                                <span style="color: #6c757d;">${plan.patient}</span>
                                            </div>
                                            <div>
                                                <strong>السعرات:</strong><br>
                                                <span style="color: #6c757d;">${plan.calories}</span>
                                            </div>
                                            <div>
                                                <strong>المدة:</strong><br>
                                                <span style="color: #6c757d;">${plan.duration}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <button onclick="viewPlanDetails('${plan.name}')" style="
                                            background: #007bff;
                                            color: white;
                                            border: none;
                                            padding: 8px 15px;
                                            border-radius: 5px;
                                            margin: 2px;
                                            cursor: pointer;
                                        ">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        <button onclick="editPlan('${plan.name}')" style="
                                            background: #ffc107;
                                            color: #212529;
                                            border: none;
                                            padding: 8px 15px;
                                            border-radius: 5px;
                                            margin: 2px;
                                            cursor: pointer;
                                        ">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div style="text-center;">
                        <button onclick="createNewPlan('${planType}')" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 5px;
                            cursor: pointer;
                            font-weight: bold;
                        ">
                            <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            window.currentModal = modal;
            showSuccessMessage(`تم فتح خطط ${planName}`);
        }

        function viewPlanDetails(planName) {
            console.log(`عرض تفاصيل الخطة: ${planName}`);

            // إغلاق النافذة الحالية إن وجدت
            if (window.currentModal) {
                closeModal();
            }

            // بيانات وهمية للخطط
            const planDetails = {
                'خطة إنقاص الوزن السريع': {
                    patient: 'أحمد محمد',
                    type: 'إنقاص الوزن',
                    calories: '1500 سعرة',
                    duration: '3 أشهر',
                    meals: '5 وجبات',
                    protein: '30%',
                    carbs: '40%',
                    fats: '30%',
                    startDate: '2024-01-01',
                    progress: '65%',
                    currentWeight: '82 كغ',
                    targetWeight: '75 كغ',
                    lostWeight: '3 كغ'
                },
                'خطة إنقاص الوزن التدريجي': {
                    patient: 'سارة أحمد',
                    type: 'إنقاص الوزن',
                    calories: '1800 سعرة',
                    duration: '6 أشهر',
                    meals: '4 وجبات',
                    protein: '25%',
                    carbs: '50%',
                    fats: '25%',
                    startDate: '2023-12-15',
                    progress: '40%',
                    currentWeight: '68 كغ',
                    targetWeight: '60 كغ',
                    lostWeight: '2 كغ'
                },
                'خطة زيادة الوزن الصحي': {
                    patient: 'فاطمة علي',
                    type: 'زيادة الوزن',
                    calories: '2800 سعرة',
                    duration: '4 أشهر',
                    meals: '6 وجبات',
                    protein: '20%',
                    carbs: '55%',
                    fats: '25%',
                    startDate: '2024-01-10',
                    progress: '50%',
                    currentWeight: '54 كغ',
                    targetWeight: '60 كغ',
                    gainedWeight: '2 كغ'
                }
            };

            const plan = planDetails[planName] || planDetails['خطة إنقاص الوزن السريع'];

            // إنشاء نافذة تفاصيل الخطة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 800px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-utensils me-2"></i>تفاصيل الخطة: ${planName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- معلومات أساسية -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h6 style="color: #495057; margin-bottom: 15px;">
                            <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                        </h6>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <strong>المريض:</strong><br>
                                <span style="color: #2c3e50;">${plan.patient}</span>
                            </div>
                            <div>
                                <strong>نوع الخطة:</strong><br>
                                <span style="color: #2c3e50;">${plan.type}</span>
                            </div>
                            <div>
                                <strong>السعرات اليومية:</strong><br>
                                <span style="color: #dc3545; font-weight: bold;">${plan.calories}</span>
                            </div>
                            <div>
                                <strong>مدة الخطة:</strong><br>
                                <span style="color: #2c3e50;">${plan.duration}</span>
                            </div>
                            <div>
                                <strong>عدد الوجبات:</strong><br>
                                <span style="color: #2c3e50;">${plan.meals}</span>
                            </div>
                            <div>
                                <strong>تاريخ البداية:</strong><br>
                                <span style="color: #2c3e50;">${plan.startDate}</span>
                            </div>
                        </div>
                    </div>

                    <!-- التوزيع الغذائي -->
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h6 style="color: #1976d2; margin-bottom: 15px;">
                            <i class="fas fa-chart-pie me-2"></i>التوزيع الغذائي
                        </h6>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                            <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #28a745;">${plan.protein}</div>
                                <div style="color: #6c757d;">البروتين</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${plan.carbs}</div>
                                <div style="color: #6c757d;">الكربوهيدرات</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">${plan.fats}</div>
                                <div style="color: #6c757d;">الدهون</div>
                            </div>
                        </div>
                    </div>

                    <!-- التقدم المحرز -->
                    <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h6 style="color: #155724; margin-bottom: 15px;">
                            <i class="fas fa-chart-line me-2"></i>التقدم المحرز
                        </h6>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: bold; color: #28a745;">${plan.progress}</div>
                                <div style="color: #6c757d;">نسبة الإنجاز</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: bold; color: #2c3e50;">${plan.currentWeight}</div>
                                <div style="color: #6c757d;">الوزن الحالي</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: bold; color: #007bff;">${plan.targetWeight}</div>
                                <div style="color: #6c757d;">الوزن المستهدف</div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 15px; padding: 10px; background: white; border-radius: 8px;">
                            <strong style="color: #28a745;">
                                ${plan.lostWeight ? `تم فقدان: ${plan.lostWeight}` : `تم اكتساب: ${plan.gainedWeight}`}
                            </strong>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div style="text-center;">
                        <button onclick="editPlan('${planName}')" style="
                            background: #ffc107;
                            color: #212529;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 5px;
                            margin: 0 5px;
                            cursor: pointer;
                            font-weight: bold;
                        ">
                            <i class="fas fa-edit me-2"></i>تعديل الخطة
                        </button>
                        <button onclick="duplicatePlan('${planName}')" style="
                            background: #17a2b8;
                            color: white;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 5px;
                            margin: 0 5px;
                            cursor: pointer;
                            font-weight: bold;
                        ">
                            <i class="fas fa-copy me-2"></i>نسخ الخطة
                        </button>
                        <button onclick="printPlan('${planName}')" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 5px;
                            margin: 0 5px;
                            cursor: pointer;
                            font-weight: bold;
                        ">
                            <i class="fas fa-print me-2"></i>طباعة الخطة
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            // إغلاق عند النقر خارج النافذة
            modal.onclick = function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            };

            window.currentModal = modal;
            showSuccessMessage(`تم فتح تفاصيل الخطة: ${planName}`);
        }

        function editPlan(planName) {
            console.log(`تعديل الخطة: ${planName}`);

            // إغلاق النافذة الحالية إن وجدت
            if (window.currentModal) {
                closeModal();
            }

            // بيانات الخطة للتعديل
            const planData = {
                'خطة إنقاص الوزن السريع': {
                    name: 'خطة إنقاص الوزن السريع',
                    patient: 'أحمد محمد',
                    type: 'weight_loss',
                    calories: '1500',
                    duration: '3 أشهر',
                    meals: '5',
                    protein: '30',
                    carbs: '40'
                },
                'خطة إنقاص الوزن التدريجي': {
                    name: 'خطة إنقاص الوزن التدريجي',
                    patient: 'سارة أحمد',
                    type: 'weight_loss',
                    calories: '1800',
                    duration: '6 أشهر',
                    meals: '4',
                    protein: '25',
                    carbs: '50'
                },
                'خطة زيادة الوزن الصحي': {
                    name: 'خطة زيادة الوزن الصحي',
                    patient: 'فاطمة علي',
                    type: 'weight_gain',
                    calories: '2800',
                    duration: '4 أشهر',
                    meals: '6',
                    protein: '20',
                    carbs: '55'
                }
            };

            const plan = planData[planName] || planData['خطة إنقاص الوزن السريع'];

            // إنشاء نافذة تعديل الخطة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 700px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-edit me-2"></i>تعديل الخطة: ${planName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form onsubmit="savePlanChanges(event, '${planName}')">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الخطة:</label>
                                <input type="text" value="${plan.name}" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">المريض:</label>
                                <select style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                                    <option value="أحمد محمد" ${plan.patient === 'أحمد محمد' ? 'selected' : ''}>أحمد محمد</option>
                                    <option value="فاطمة علي" ${plan.patient === 'فاطمة علي' ? 'selected' : ''}>فاطمة علي</option>
                                    <option value="سارة أحمد" ${plan.patient === 'سارة أحمد' ? 'selected' : ''}>سارة أحمد</option>
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">السعرات اليومية:</label>
                                <input type="number" value="${plan.calories}" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">عدد الوجبات:</label>
                                <select style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                                    <option value="3" ${plan.meals === '3' ? 'selected' : ''}>3 وجبات</option>
                                    <option value="4" ${plan.meals === '4' ? 'selected' : ''}>4 وجبات</option>
                                    <option value="5" ${plan.meals === '5' ? 'selected' : ''}>5 وجبات</option>
                                    <option value="6" ${plan.meals === '6' ? 'selected' : ''}>6 وجبات</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">المدة:</label>
                                <select style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                                    <option value="1 شهر" ${plan.duration === '1 شهر' ? 'selected' : ''}>شهر واحد</option>
                                    <option value="3 أشهر" ${plan.duration === '3 أشهر' ? 'selected' : ''}>3 أشهر</option>
                                    <option value="6 أشهر" ${plan.duration === '6 أشهر' ? 'selected' : ''}>6 أشهر</option>
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">نسبة البروتين (%):</label>
                                <input type="number" value="${plan.protein}" min="10" max="40" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">نسبة الكربوهيدرات (%):</label>
                                <input type="number" value="${plan.carbs}" min="20" max="70" style="
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ddd;
                                    border-radius: 5px;
                                " required>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات التعديل:</label>
                            <textarea rows="3" style="
                                width: 100%;
                                padding: 10px;
                                border: 1px solid #ddd;
                                border-radius: 5px;
                                resize: vertical;
                            " placeholder="أي تعديلات أو ملاحظات جديدة..."></textarea>
                        </div>

                        <div style="text-center;">
                            <button type="submit" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 5px;
                                margin: 0 5px;
                                cursor: pointer;
                                font-weight: bold;
                            ">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                            <button type="button" onclick="closeModal()" style="
                                background: #6c757d;
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 5px;
                                margin: 0 5px;
                                cursor: pointer;
                            ">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            window.currentModal = modal;
            showSuccessMessage(`تم فتح نموذج تعديل الخطة: ${planName}`);
        }

        function createNewPlan(planType) {
            closeModal();
            showSuccessMessage(`إنشاء خطة جديدة من نوع: ${planType}`);
        }

        function savePlanChanges(event, planName) {
            event.preventDefault();
            console.log(`حفظ تعديلات الخطة: ${planName}`);

            showSuccessMessage('جاري حفظ تعديلات الخطة...');

            // محاكاة حفظ البيانات
            setTimeout(() => {
                closeModal();
                showSuccessMessage(`تم حفظ تعديلات الخطة: ${planName} بنجاح!`);
            }, 1500);
        }

        function duplicatePlan(planName) {
            console.log(`نسخ الخطة: ${planName}`);
            closeModal();

            showSuccessMessage(`جاري نسخ الخطة: ${planName}...`);

            setTimeout(() => {
                showSuccessMessage(`تم نسخ الخطة: ${planName} بنجاح! تم إنشاء نسخة جديدة.`);
                updatePlansCount();
            }, 1500);
        }

        function printPlan(planName) {
            console.log(`طباعة الخطة: ${planName}`);

            const printWindow = window.open('', '_blank', 'width=800,height=600');
            const content = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>خطة غذائية - ${planName}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            line-height: 1.6;
                            direction: rtl;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #28a745;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .clinic-name {
                            font-size: 28px;
                            font-weight: bold;
                            color: #28a745;
                            margin-bottom: 10px;
                        }
                        .plan-info {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                            border-right: 5px solid #28a745;
                        }
                        .nutrition-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        .nutrition-table th, .nutrition-table td {
                            border: 1px solid #ddd;
                            padding: 12px;
                            text-align: center;
                        }
                        .nutrition-table th {
                            background: #28a745;
                            color: white;
                        }
                        .meal-section {
                            background: #fff;
                            border: 2px solid #e9ecef;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 8px;
                            border-right: 4px solid #17a2b8;
                        }
                        .meal-title {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }
                        .instructions {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .signature {
                            margin-top: 50px;
                            text-align: left;
                        }
                        .signature-line {
                            border-bottom: 2px solid #333;
                            width: 200px;
                            margin-bottom: 10px;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="clinic-name">🏥 عيادة التغذية العلاجية</div>
                        <div>د. [اسم الطبيب] - أخصائي التغذية العلاجية</div>
                        <div>العنوان: [عنوان العيادة] | الهاتف: [رقم الهاتف]</div>
                    </div>

                    <div class="plan-info">
                        <h3 style="color: #28a745; margin-bottom: 15px;">📋 ${planName}</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <strong>👤 المريض:</strong> أحمد محمد<br>
                                <strong>📅 تاريخ الخطة:</strong> ${new Date().toLocaleDateString('ar-SA')}<br>
                                <strong>⏱️ المدة:</strong> 3 أشهر<br>
                                <strong>🎯 الهدف:</strong> إنقاص الوزن
                            </div>
                            <div>
                                <strong>🔥 السعرات اليومية:</strong> 1500 سعرة<br>
                                <strong>🍽️ عدد الوجبات:</strong> 5 وجبات<br>
                                <strong>💪 البروتين:</strong> 30%<br>
                                <strong>🌾 الكربوهيدرات:</strong> 40%
                            </div>
                        </div>
                    </div>

                    <h3 style="color: #2c3e50; border-bottom: 2px solid #28a745; padding-bottom: 10px;">🍽️ الوجبات اليومية:</h3>

                    <div class="meal-section">
                        <div class="meal-title">🌅 الإفطار (7:00 - 8:00 ص)</div>
                        <ul>
                            <li>كوب حليب خالي الدسم (200 مل)</li>
                            <li>2 شريحة خبز أسمر</li>
                            <li>ملعقة كبيرة عسل طبيعي</li>
                            <li>حبة تفاح متوسطة</li>
                        </ul>
                        <strong>السعرات: ~350 سعرة</strong>
                    </div>

                    <div class="meal-section">
                        <div class="meal-title">🥗 وجبة خفيفة (10:00 ص)</div>
                        <ul>
                            <li>حفنة لوز (10 حبات)</li>
                            <li>كوب شاي أخضر بدون سكر</li>
                        </ul>
                        <strong>السعرات: ~100 سعرة</strong>
                    </div>

                    <div class="meal-section">
                        <div class="meal-title">🍽️ الغداء (1:00 - 2:00 م)</div>
                        <ul>
                            <li>قطعة دجاج مشوي (150 غ)</li>
                            <li>كوب أرز أسمر مسلوق</li>
                            <li>سلطة خضراء مشكلة</li>
                            <li>ملعقة صغيرة زيت زيتون</li>
                        </ul>
                        <strong>السعرات: ~500 سعرة</strong>
                    </div>

                    <div class="meal-section">
                        <div class="meal-title">🍎 وجبة خفيفة (4:00 م)</div>
                        <ul>
                            <li>كوب زبادي طبيعي قليل الدسم</li>
                            <li>ملعقة كبيرة شوفان</li>
                        </ul>
                        <strong>السعرات: ~150 سعرة</strong>
                    </div>

                    <div class="meal-section">
                        <div class="meal-title">🌙 العشاء (7:00 - 8:00 م)</div>
                        <ul>
                            <li>قطعة سمك مشوي (120 غ)</li>
                            <li>خضروات مسلوقة متنوعة</li>
                            <li>شريحة خبز أسمر</li>
                            <li>كوب عصير برتقال طبيعي</li>
                        </ul>
                        <strong>السعرات: ~400 سعرة</strong>
                    </div>

                    <div class="instructions">
                        <h4 style="color: #856404; margin-top: 0;">⚠️ تعليمات مهمة:</h4>
                        <ul style="margin: 10px 0;">
                            <li>شرب 8-10 أكواب ماء يومياً</li>
                            <li>ممارسة المشي 30 دقيقة يومياً</li>
                            <li>تجنب السكريات والمقليات</li>
                            <li>تناول الوجبات في أوقاتها المحددة</li>
                            <li>المتابعة الأسبوعية لقياس الوزن</li>
                            <li>في حالة أي استفسار، يرجى التواصل فوراً</li>
                        </ul>
                    </div>

                    <table class="nutrition-table">
                        <thead>
                            <tr>
                                <th>العنصر الغذائي</th>
                                <th>النسبة المئوية</th>
                                <th>الكمية اليومية</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>البروتين</td>
                                <td>30%</td>
                                <td>112.5 غرام</td>
                            </tr>
                            <tr>
                                <td>الكربوهيدرات</td>
                                <td>40%</td>
                                <td>150 غرام</td>
                            </tr>
                            <tr>
                                <td>الدهون</td>
                                <td>30%</td>
                                <td>50 غرام</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="signature">
                        <div>توقيع أخصائي التغذية:</div>
                        <div class="signature-line"></div>
                        <div style="font-size: 14px; color: #666;">د. [اسم الطبيب]</div>
                        <div style="font-size: 12px; color: #999; margin-top: 20px;">
                            تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}
                        </div>
                    </div>

                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `;

            printWindow.document.write(content);
            printWindow.document.close();

            showSuccessMessage(`تم فتح نافذة طباعة الخطة: ${planName}`);
        }

        // وظائف إدارة الأطعمة
        function showFoodCategory(category) {
            console.log(`عرض فئة الأطعمة: ${category}`);

            const categoryNames = {
                'grains': 'الحبوب والنشويات',
                'proteins': 'البروتينات',
                'vegetables': 'الخضروات والفواكه',
                'dairy': 'الألبان ومنتجاتها'
            };

            const categoryName = categoryNames[category];

            // إنشاء نافذة عرض الأطعمة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            const foodsData = {
                'grains': [
                    { name: 'أرز أبيض', calories: '130 سعرة/100غ', carbs: '28غ', protein: '2.7غ' },
                    { name: 'خبز أسمر', calories: '247 سعرة/100غ', carbs: '41غ', protein: '13غ' },
                    { name: 'شوفان', calories: '389 سعرة/100غ', carbs: '66غ', protein: '17غ' },
                    { name: 'معكرونة', calories: '371 سعرة/100غ', carbs: '75غ', protein: '13غ' }
                ],
                'proteins': [
                    { name: 'دجاج مشوي', calories: '239 سعرة/100غ', carbs: '0غ', protein: '27غ' },
                    { name: 'سمك السلمون', calories: '208 سعرة/100غ', carbs: '0غ', protein: '25غ' },
                    { name: 'بيض', calories: '155 سعرة/100غ', carbs: '1.1غ', protein: '13غ' },
                    { name: 'لحم بقري', calories: '250 سعرة/100غ', carbs: '0غ', protein: '26غ' }
                ],
                'vegetables': [
                    { name: 'تفاح', calories: '52 سعرة/100غ', carbs: '14غ', protein: '0.3غ' },
                    { name: 'جزر', calories: '41 سعرة/100غ', carbs: '10غ', protein: '0.9غ' },
                    { name: 'برتقال', calories: '47 سعرة/100غ', carbs: '12غ', protein: '0.9غ' },
                    { name: 'خيار', calories: '16 سعرة/100غ', carbs: '4غ', protein: '0.7غ' }
                ],
                'dairy': [
                    { name: 'حليب كامل الدسم', calories: '61 سعرة/100مل', carbs: '4.8غ', protein: '3.2غ' },
                    { name: 'جبن أبيض', calories: '264 سعرة/100غ', carbs: '1.3غ', protein: '25غ' },
                    { name: 'زبادي طبيعي', calories: '59 سعرة/100غ', carbs: '3.6غ', protein: '10غ' },
                    { name: 'لبنة', calories: '159 سعرة/100غ', carbs: '7غ', protein: '8غ' }
                ]
            };

            const foods = foodsData[category] || [];

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 900px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin: 0;">
                            <i class="fas fa-apple-alt me-2"></i>${categoryName}
                        </h4>
                        <button onclick="closeModal()" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            border-radius: 50%;
                            width: 35px;
                            height: 35px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        ${foods.map(food => `
                            <div style="
                                background: #f8f9fa;
                                border: 1px solid #dee2e6;
                                border-radius: 10px;
                                padding: 20px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.1)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <h6 style="color: #2c3e50; margin-bottom: 15px;">
                                    <i class="fas fa-utensils me-2"></i>${food.name}
                                </h6>
                                <div style="font-size: 14px; line-height: 1.6;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span><strong>السعرات:</strong></span>
                                        <span style="color: #dc3545; font-weight: bold;">${food.calories}</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span><strong>الكربوهيدرات:</strong></span>
                                        <span style="color: #ffc107; font-weight: bold;">${food.carbs}</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between;">
                                        <span><strong>البروتين:</strong></span>
                                        <span style="color: #28a745; font-weight: bold;">${food.protein}</span>
                                    </div>
                                </div>
                                <div style="text-center; margin-top: 15px;">
                                    <button onclick="addToMeal('${food.name}')" style="
                                        background: #007bff;
                                        color: white;
                                        border: none;
                                        padding: 8px 15px;
                                        border-radius: 5px;
                                        cursor: pointer;
                                        font-size: 12px;
                                    ">
                                        <i class="fas fa-plus me-1"></i>إضافة للوجبة
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div style="text-center;">
                        <button onclick="addNewFood('${category}')" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 5px;
                            cursor: pointer;
                            font-weight: bold;
                        ">
                            <i class="fas fa-plus me-2"></i>إضافة طعام جديد
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.transition = 'opacity 0.3s ease';
                modal.style.opacity = '1';
            }, 10);

            window.currentModal = modal;
            showSuccessMessage(`تم فتح فئة ${categoryName}`);
        }

        function addToMeal(foodName) {
            showSuccessMessage(`تم إضافة ${foodName} للوجبة`);
        }

        function addNewFood(category) {
            closeModal();
            showSuccessMessage(`إضافة طعام جديد لفئة: ${category}`);
        }

        // وظائف إنشاء الخطة الغذائية
        function showCreateNutritionPlanForm() {
            console.log('تم النقر على زر إنشاء خطة غذائية جديدة');

            const form = document.getElementById('createNutritionPlanForm');
            if (form) {
                form.style.display = 'block';
                form.scrollIntoView({ behavior: 'smooth' });

                // تأثير بصري
                form.style.opacity = '0';
                form.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    form.style.transition = 'all 0.5s ease';
                    form.style.opacity = '1';
                    form.style.transform = 'translateY(0)';
                }, 100);

                // رسالة تأكيد
                showSuccessMessage('تم فتح نموذج إنشاء خطة غذائية جديدة');
            }
        }

        function hideCreateNutritionPlanForm() {
            console.log('تم النقر على زر الإلغاء');

            const form = document.getElementById('createNutritionPlanForm');
            if (form) {
                form.style.transition = 'all 0.3s ease';
                form.style.opacity = '0';
                form.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    form.style.display = 'none';
                    // إعادة تعيين النموذج
                    form.querySelector('form').reset();
                }, 300);

                showSuccessMessage('تم إلغاء إنشاء الخطة الغذائية');
            }
        }

        function createNutritionPlan(event) {
            event.preventDefault();
            console.log('تم إرسال نموذج إنشاء خطة غذائية');

            // جمع البيانات
            const planData = {
                name: document.getElementById('planName').value,
                patient: document.getElementById('planPatient').value,
                type: document.getElementById('planType').value,
                calories: document.getElementById('planCalories').value,
                duration: document.getElementById('planDuration').value,
                meals: document.getElementById('planMeals').value,
                protein: document.getElementById('planProtein').value,
                carbs: document.getElementById('planCarbs').value,
                preferredFoods: document.getElementById('planPreferredFoods').value,
                avoidedFoods: document.getElementById('planAvoidedFoods').value,
                notes: document.getElementById('planNotes').value,
                dateCreated: new Date().toLocaleDateString('ar-SA')
            };

            console.log('بيانات الخطة الغذائية:', planData);

            // التحقق من صحة البيانات
            const proteinPercent = parseInt(planData.protein);
            const carbsPercent = parseInt(planData.carbs);
            const fatsPercent = 100 - proteinPercent - carbsPercent;

            if (fatsPercent < 10 || fatsPercent > 40) {
                showErrorMessage('نسبة البروتين والكربوهيدرات غير متوازنة. يجب أن تترك مساحة للدهون (10-40%)');
                return;
            }

            // محاكاة إنشاء الخطة
            showSuccessMessage('جاري إنشاء الخطة الغذائية...');

            setTimeout(() => {
                // إضافة الخطة للنظام
                addNutritionPlanToSystem(planData);

                // إخفاء النموذج
                hideCreateNutritionPlanForm();

                // رسالة نجاح
                showSuccessMessage(`تم إنشاء الخطة الغذائية "${planData.name}" للمريض ${planData.patient} بنجاح!`);

                // تحديث عدد الخطط في لوحة التحكم
                updatePlansCount();

            }, 2000);
        }

        function addNutritionPlanToSystem(planData) {
            // محاكاة إضافة الخطة للنظام
            console.log('تم إضافة الخطة للنظام:', planData);

            // يمكن هنا إضافة الخطة لقاعدة البيانات أو التخزين المحلي
            // localStorage.setItem('nutritionPlans', JSON.stringify(plans));
        }

        function updatePlansCount() {
            const countElement = document.getElementById('plans-count');
            if (countElement) {
                const currentCount = parseInt(countElement.textContent);
                countElement.textContent = currentCount + 1;

                // تأثير بصري
                countElement.style.color = '#28a745';
                countElement.style.transform = 'scale(1.2)';

                setTimeout(() => {
                    countElement.style.color = '';
                    countElement.style.transform = '';
                }, 1000);
            }
        }

        function showErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: bold;
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            messageDiv.innerHTML = `❌ ${message}`;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(messageDiv)) {
                        document.body.removeChild(messageDiv);
                    }
                }, 300);
            }, 4000);
        }

        // دالة عامة لإظهار رسائل النجاح
        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: bold;
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            messageDiv.innerHTML = `✅ ${message}`;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(messageDiv)) {
                        document.body.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // رسم بياني بسيط
        function drawChart() {
            const canvas = document.getElementById('performanceChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // تنظيف الكانفاس
            ctx.clearRect(0, 0, width, height);

            // رسم الخلفية
            const gradient = ctx.createLinearGradient(0, 0, 0, height);
            gradient.addColorStop(0, 'rgba(255,255,255,0.3)');
            gradient.addColorStop(1, 'rgba(255,255,255,0.1)');

            // بيانات وهمية
            const data = [20, 35, 25, 45, 30, 55, 40, 60, 45, 70];
            const points = [];

            // حساب النقاط
            for (let i = 0; i < data.length; i++) {
                const x = (i / (data.length - 1)) * width;
                const y = height - (data[i] / 70) * height;
                points.push({ x, y });
            }

            // رسم المنطقة تحت الخط
            ctx.beginPath();
            ctx.moveTo(points[0].x, height);
            points.forEach(point => ctx.lineTo(point.x, point.y));
            ctx.lineTo(points[points.length - 1].x, height);
            ctx.fillStyle = gradient;
            ctx.fill();

            // رسم الخط
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);
            points.forEach(point => ctx.lineTo(point.x, point.y));
            ctx.strokeStyle = 'rgba(255,255,255,0.8)';
            ctx.lineWidth = 3;
            ctx.stroke();

            // رسم النقاط
            points.forEach(point => {
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fillStyle = 'white';
                ctx.fill();
                ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                ctx.lineWidth = 2;
                ctx.stroke();
            });
        }

        // رسم بياني مالي
        function drawFinancialChart() {
            const canvas = document.getElementById('financialChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // تنظيف الكانفاس
            ctx.clearRect(0, 0, width, height);

            // بيانات الإيرادات والمصروفات (بالدينار العراقي)
            const incomeData = [19500000, 23400000, 28600000, 32500000, 36400000, 41600000, 45500000, 49400000, 54600000, 58500000];
            const expenseData = [10400000, 12350000, 14300000, 16250000, 18200000, 20150000, 20800000, 22100000, 22750000, 24050000];

            const months = incomeData.length;

            // رسم الإيرادات (أخضر)
            const incomeGradient = ctx.createLinearGradient(0, 0, 0, height);
            incomeGradient.addColorStop(0, 'rgba(40, 167, 69, 0.3)');
            incomeGradient.addColorStop(1, 'rgba(40, 167, 69, 0.1)');

            ctx.beginPath();
            ctx.moveTo(0, height);
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (incomeData[i] / 65000000) * height;
                ctx.lineTo(x, y);
            }
            ctx.lineTo(width, height);
            ctx.fillStyle = incomeGradient;
            ctx.fill();

            // خط الإيرادات
            ctx.beginPath();
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (incomeData[i] / 65000000) * height;
                if (i === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 3;
            ctx.stroke();

            // رسم المصروفات (أحمر)
            const expenseGradient = ctx.createLinearGradient(0, 0, 0, height);
            expenseGradient.addColorStop(0, 'rgba(220, 53, 69, 0.3)');
            expenseGradient.addColorStop(1, 'rgba(220, 53, 69, 0.1)');

            ctx.beginPath();
            ctx.moveTo(0, height);
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (expenseData[i] / 65000000) * height;
                ctx.lineTo(x, y);
            }
            ctx.lineTo(width, height);
            ctx.fillStyle = expenseGradient;
            ctx.fill();

            // خط المصروفات
            ctx.beginPath();
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (expenseData[i] / 65000000) * height;
                if (i === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.strokeStyle = '#dc3545';
            ctx.lineWidth = 3;
            ctx.stroke();

            // رسم النقاط للإيرادات
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (incomeData[i] / 65000000) * height;
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, Math.PI * 2);
                ctx.fillStyle = '#28a745';
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
            }

            // رسم النقاط للمصروفات
            for (let i = 0; i < months; i++) {
                const x = (i / (months - 1)) * width;
                const y = height - (expenseData[i] / 65000000) * height;
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, Math.PI * 2);
                ctx.fillStyle = '#dc3545';
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
            }

            // إضافة تسميات
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';

            // تسميات الشهور
            const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر'];
            for (let i = 0; i < Math.min(months, monthNames.length); i++) {
                const x = (i / (months - 1)) * width;
                ctx.fillText(monthNames[i], x, height - 10);
            }
        }

        // تحديث الوقت في الوقت الفعلي
        function updateRealTimeData() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.title = `نظام إدارة العيادات - ${timeString}`;

            // تم إزالة التحديث العشوائي للرسائل
            // الآن الرقم ثابت ولن يزداد تلقائياً
        }

        // تشغيل الرسوم المتحركة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(animateNumbers, 500);
            setTimeout(drawChart, 1000);
            setTimeout(animateFinancialNumbers, 1500);
            setTimeout(drawFinancialChart, 2000);
        });

        // تحديث البيانات كل ثانية
        setInterval(updateRealTimeData, 1000);
        setInterval(updateTime, 1000);
        updateTime();

        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 مرحباً بك في نظام إدارة العيادات المطور!');
            console.log('✅ لوحة تحكم ديناميكية مع تأثيرات بصرية متقدمة!');
            console.log('🎨 ألوان متدرجة وتحريك الأرقام!');
            console.log('💰 نظام إدارة حسابات متكامل مع رسوم بيانية!');
            console.log('📊 تحليل مالي شامل للعيادة!');
        }, 1000);
    </script>
</body>
</html>
